@page
@model NextBroker.Pages.Provizija.ProvizijaSetiranjeModel
@{
    ViewData["Title"] = "Сетирање на провизија";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2>@ViewData["Title"]</h2>
        <div>
            <a asp-page="./ProvizijaSetiranjeTablica" class="btn btn-secondary mr-2">
                <i class="fas fa-table"></i> Табличен преглед
            </a>
            <a asp-page="./ProvizijaSetiranjeDodajPravilo" class="btn btn-primary">
                <i class="fas fa-plus"></i> Додај ново правило
            </a>
            <a asp-page="./ProvizijaSetiranjeMasovenVnesPravila" class="btn btn-primary">
                <i class="fas fa-plus"></i> Масовен внес на правила
            </a>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="row mb-3">
        <div class="col-md-8">
            <form method="get" id="filtersForm">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h6 class="card-title mb-3">Филтер по статус:</h6>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="StatusFilter" id="statusSite" value="Сите"
                                           @(Model.StatusFilter == "Сите" ? "checked" : "") onchange="updateStatusFilter(this.value)">
                                    <label class="form-check-label" for="statusSite">
                                        Сите
                                    </label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="StatusFilter" id="statusAktivni" value="Активни"
                                           @(Model.StatusFilter == "Активни" ? "checked" : "") onchange="updateStatusFilter(this.value)">
                                    <label class="form-check-label" for="statusAktivni">
                                        Активни
                                    </label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="StatusFilter" id="statusNeaktivni" value="Неактивни"
                                           @(Model.StatusFilter == "Неактивни" ? "checked" : "") onchange="updateStatusFilter(this.value)">
                                    <label class="form-check-label" for="statusNeaktivni">
                                        Неактивни
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <h6 class="card-title mb-3">Осигурител:</h6>
                                <div class="osiguritel-checkboxes" style="max-height: 200px; overflow-y: auto; border: 1px solid #ced4da; border-radius: 0.25rem; padding: 10px;">
                                    @foreach (var option in Model.OsiguritelOptions)
                                    {
                                        <div class="form-check">
                                            <input class="form-check-input osiguritel-checkbox" type="checkbox"
                                                   value="@option" id="<EMAIL>(" ", "_")"
                                                   @(Model.SelectedOsiguriteli.Contains(option) ? "checked" : "")
                                                   onchange="updateOsiguritelCheckboxes()">
                                            <label class="form-check-label" for="<EMAIL>(" ", "_")">
                                                @option
                                            </label>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hidden fields to preserve filter values -->
                <input type="hidden" id="hiddenStatusFilter" name="StatusFilter" value="@Model.StatusFilter" />

                <!-- Hidden fields for selected Osiguriteli -->
                <div id="hiddenOsiguritelFields">
                    @if (Model.SelectedOsiguriteli != null)
                    {
                        @foreach (var selected in Model.SelectedOsiguriteli)
                        {
                            <input type="hidden" name="SelectedOsiguriteli" value="@selected" />
                        }
                    }
                </div>
                
                <!-- Hidden field for search term -->
                <input type="hidden" id="hiddenSearchTerm" name="SearchTerm" value="" />
            </form>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-md-6">
            <div class="input-group">
                <input type="text" id="searchInput" class="form-control" placeholder="Пребарувај...">
                <div class="input-group-append">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                </div>
            </div>
        </div>
        <div class="col-md-6 text-right">
            <div class="d-flex justify-content-end align-items-center">
                <button type="button" class="btn btn-success mr-2" id="exportToExcel">
                    <i class="fas fa-file-excel"></i> Извези во Excel
                </button>
                <select id="entriesPerPage" class="form-control" style="width: auto;">
                    <option value="10">10 записи</option>
                    <option value="20">20 записи</option>
                    <option value="30">30 записи</option>
                    <option value="100">100 записи</option>
                </select>
            </div>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-bordered table-striped" id="provizijaTable">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Датум креирано</th>
                    <th>Креирано од</th>
                    <th>Датум модифицирано</th>
                    <th>Модифицирано од</th>
                    <th>Назив</th>
                    <th>Клиент провизија</th>
                    <th>Класа име</th>
                    <th>Име</th>
                    <th>% Провизија (физички)</th>
                    <th>% Провизија (правни)</th>
                    <th>Начин</th>
                    <th>Тип на провизија</th>
                    <th>Важи од</th>
                    <th>Важи до</th>
                    <th>Примач на провизија</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.ProvizijaList)
                {
                    <tr>
                        <td>@item.Id</td>
                        <td>@(item.DateCreated?.ToString("dd.MM.yyyy"))</td>
                        <td>@item.UsernameCreated</td>
                        <td>@(item.DateModified?.ToString("dd.MM.yyyy"))</td>
                        <td>@item.UsernameModified</td>
                        <td>@item.Naziv</td>
                        <td>@item.KlientProvizija</td>
                        <td>@item.KlasaIme</td>
                        <td>@item.Ime</td>
                        <td>@item.ProcentNaProvizijaZaFizickiLica</td>
                        <td>@item.ProcentNaProvizijaZaPravniLica</td>
                        <td>@item.Nacin</td>
                        <td>@item.TipNaProvizija</td>
                        <td>@(item.DatumVaziOd?.ToString("dd.MM.yyyy"))</td>
                        <td>
                            @if (item.DatumVaziDo.HasValue)
                            {
                                @item.DatumVaziDo.Value.ToString("dd.MM.yyyy")
                            }
                            else
                            {
                                <button type="button" class="btn btn-sm btn-primary edit-datum" data-id="@item.Id">
                                    <i class="fas fa-edit"></i> Уреди
                                </button>
                            }
                        </td>
                        <td>@item.PrimacProvizija</td>
                    </tr>
                }
            </tbody>
        </table>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div id="tableInfo" class="text-muted">
                Прикажани <span id="showingStart">0</span> до <span id="showingEnd">0</span> од <span id="totalEntries">0</span> записи
            </div>
        </div>
        <div class="col-md-6">
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-end" id="pagination">
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editDatumModal" tabindex="-1" role="dialog" aria-labelledby="editDatumModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editDatumModalLabel">Уреди датум</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="datumForm" method="post">
                    <input type="hidden" id="provizijaId" name="provizijaId" />
                    <div class="form-group">
                        <label for="datumVaziDo">Важи до:</label>
                        <input type="date" class="form-control" id="datumVaziDo" name="datumVaziDo" required />
                    </div>
                    @Html.AntiForgeryToken()
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Откажи</button>
                <button type="button" class="btn btn-primary" id="saveDatum">Зачувај</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            let table = document.getElementById('provizijaTable');
            let tbody = table.querySelector('tbody');
            let rows = Array.from(tbody.querySelectorAll('tr'));
            let currentPage = 1;
            let rowsPerPage = 10;
            
            // Search functionality
            document.getElementById('searchInput').addEventListener('input', function (e) {
                let searchText = e.target.value.toLowerCase();
                // Update hidden field to keep search term in sync
                document.getElementById('hiddenSearchTerm').value = e.target.value;
                let filteredRows = rows.filter(row => {
                    return Array.from(row.cells).some(cell => 
                        cell.textContent.toLowerCase().includes(searchText)
                    );
                });
                displayRows(filteredRows);
            });

            // Entries per page change
            document.getElementById('entriesPerPage').addEventListener('change', function (e) {
                rowsPerPage = parseInt(e.target.value);
                currentPage = 1;
                displayRows(rows);
            });

            // Display rows function
            function displayRows(rowsToShow) {
                tbody.innerHTML = '';
                let start = (currentPage - 1) * rowsPerPage;
                let end = start + rowsPerPage;
                let paginatedRows = rowsToShow.slice(start, end);

                paginatedRows.forEach(row => tbody.appendChild(row.cloneNode(true)));
                updatePagination(rowsToShow.length);
                updateTableInfo(start + 1, Math.min(end, rowsToShow.length), rowsToShow.length);
                
                // Re-attach event listeners to edit buttons
                attachEditButtonListeners();
            }

            // Update pagination controls
            function updatePagination(totalRows) {
                let paginationElement = document.getElementById('pagination');
                paginationElement.innerHTML = '';
                let totalPages = Math.ceil(totalRows / rowsPerPage);

                // Previous button
                let prevLi = document.createElement('li');
                prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
                prevLi.innerHTML = '<a class="page-link" href="#">Претходна</a>';
                prevLi.addEventListener('click', () => {
                    if (currentPage > 1) {
                        currentPage--;
                        displayRows(rows);
                    }
                });
                paginationElement.appendChild(prevLi);

                // Page numbers
                for (let i = 1; i <= totalPages; i++) {
                    let li = document.createElement('li');
                    li.className = `page-item ${currentPage === i ? 'active' : ''}`;
                    li.innerHTML = `<a class="page-link" href="#">${i}</a>`;
                    li.addEventListener('click', () => {
                        currentPage = i;
                        displayRows(rows);
                    });
                    paginationElement.appendChild(li);
                }

                // Next button
                let nextLi = document.createElement('li');
                nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
                nextLi.innerHTML = '<a class="page-link" href="#">Следна</a>';
                nextLi.addEventListener('click', () => {
                    if (currentPage < totalPages) {
                        currentPage++;
                        displayRows(rows);
                    }
                });
                paginationElement.appendChild(nextLi);
            }

            // Update table info
            function updateTableInfo(start, end, total) {
                document.getElementById('showingStart').textContent = total === 0 ? 0 : start;
                document.getElementById('showingEnd').textContent = end;
                document.getElementById('totalEntries').textContent = total;
            }
            
            // Function to attach event listeners to edit buttons
            function attachEditButtonListeners() {
                document.querySelectorAll('.edit-datum').forEach(button => {
                    button.addEventListener('click', function() {
                        const id = this.getAttribute('data-id');
                        document.getElementById('provizijaId').value = id;
                        $('#editDatumModal').modal('show');
                    });
                });
            }
            
            // Save button click handler
            document.getElementById('saveDatum').addEventListener('click', function() {
                const id = document.getElementById('provizijaId').value;
                const datum = document.getElementById('datumVaziDo').value;
                
                if (!datum) {
                    alert('Мора да внесете датум!');
                    return;
                }
                
                // Send AJAX request to update the datum
                fetch('?handler=UpdateDatumVaziDo', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify({
                        id: id,
                        datumVaziDo: datum
                    })
                })
                .then(response => {
                    if (response.ok) return response.json();
                    throw new Error('Network response was not ok.');
                })
                .then(data => {
                    if (data.success) {
                        // Close modal
                        $('#editDatumModal').modal('hide');
                        
                        // Refresh the page to show updated data
                        window.location.reload();
                    } else {
                        alert('Грешка при зачувување: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Се случи грешка при зачувување на податоците.');
                });
            });

            // Initial display
            displayRows(rows);

            // Initial attachment of edit button listeners
            attachEditButtonListeners();

            // Initialize hidden fields for Osiguritel checkboxes
            updateHiddenOsiguritelFields();

            // Export to Excel functionality
            document.getElementById('exportToExcel').addEventListener('click', function() {
                // Create form data with current filters
                const formData = new FormData();
                formData.append('StatusFilter', document.getElementById('hiddenStatusFilter').value);

                // Add selected Osiguriteli
                const hiddenOsiguritelFields = document.querySelectorAll('input[name="SelectedOsiguriteli"]');
                hiddenOsiguritelFields.forEach(field => {
                    formData.append('SelectedOsiguriteli', field.value);
                });

                // Add search term
                const searchTerm = document.getElementById('searchInput').value;
                formData.append('SearchTerm', searchTerm);

                // Add anti-forgery token
                formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

                // Create and submit form for export
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '?handler=ExportToExcel';
                form.style.display = 'none';

                // Convert FormData to form inputs
                for (let [key, value] of formData.entries()) {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = key;
                    input.value = value;
                    form.appendChild(input);
                }

                document.body.appendChild(form);
                form.submit();
                document.body.removeChild(form);
            });
        });

        // Filter update functions
        function updateStatusFilter(value) {
            document.getElementById('hiddenStatusFilter').value = value;
            document.getElementById('filtersForm').submit();
        }

        function updateOsiguritelCheckboxes() {
            // Get the checkbox that was just clicked
            const clickedCheckbox = event.target;

            // Handle "Сите" checkbox logic
            const siteCheckbox = document.querySelector('input[value="Сите"].osiguritel-checkbox');
            const otherCheckboxes = document.querySelectorAll('.osiguritel-checkbox:not([value="Сите"])');

            if (clickedCheckbox.value === 'Сите' && clickedCheckbox.checked) {
                // If "Сите" is checked, uncheck all others
                otherCheckboxes.forEach(cb => cb.checked = false);
            } else if (clickedCheckbox.value !== 'Сите' && clickedCheckbox.checked) {
                // If any other checkbox is checked, uncheck "Сите"
                if (siteCheckbox) siteCheckbox.checked = false;
            }

            // Check if no checkboxes are selected
            const anyChecked = document.querySelectorAll('.osiguritel-checkbox:checked').length > 0;
            if (!anyChecked) {
                // If no checkboxes are selected, check "Сите"
                if (siteCheckbox) siteCheckbox.checked = true;
            }

            // Update hidden fields to match current checkbox states
            updateHiddenOsiguritelFields();

            // Submit the form with a small delay to ensure DOM updates
            setTimeout(() => {
                document.getElementById('filtersForm').submit();
            }, 100);
        }

        function updateHiddenOsiguritelFields() {
            // Clear existing hidden fields
            const hiddenContainer = document.getElementById('hiddenOsiguritelFields');
            hiddenContainer.innerHTML = '';

            // Get all checked checkboxes and create hidden fields
            const checkedBoxes = document.querySelectorAll('.osiguritel-checkbox:checked');
            console.log('Checked boxes count:', checkedBoxes.length);

            checkedBoxes.forEach(checkbox => {
                console.log('Adding hidden field for:', checkbox.value);
                const hiddenField = document.createElement('input');
                hiddenField.type = 'hidden';
                hiddenField.name = 'SelectedOsiguriteli';
                hiddenField.value = checkbox.value;
                hiddenContainer.appendChild(hiddenField);
            });

            // Log all hidden fields
            const allHiddenFields = hiddenContainer.querySelectorAll('input[name="SelectedOsiguriteli"]');
            console.log('Total hidden fields created:', allHiddenFields.length);
        }
    </script>
}
