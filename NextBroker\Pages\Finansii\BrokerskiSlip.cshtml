@page
@model NextBroker.Pages.Finansii.BrokerskiSlipModel
@{
    ViewData["Title"] = "Брокерски Слип";
    Layout = "_Layout";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2>Брокерски Слип</h2>

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    @TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <!-- Collapsible Form -->
            <div class="card mb-4">
                <div class="card-header">
                    <button class="btn btn-primary" type="button" data-bs-toggle="collapse" data-bs-target="#addEntryForm" aria-expanded="false" aria-controls="addEntryForm">
                        <i class="fas fa-plus"></i> Додај нов запис
                    </button>
                </div>
                <div class="collapse" id="addEntryForm">
                    <div class="card-body">
                        <form method="post">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Input.OsiguritelnoBrokerskoDrustvo" class="form-label"></label>
                                        <input asp-for="Input.OsiguritelnoBrokerskoDrustvo" class="form-control" />
                                        <span asp-validation-for="Input.OsiguritelnoBrokerskoDrustvo" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Input.Osiguritel" class="form-label"></label>
                                        <select asp-for="Input.Osiguritel" asp-items="Model.Osiguriteli" class="form-select">
                                            <option value="">-- Избери осигурител --</option>
                                        </select>
                                        <span asp-validation-for="Input.Osiguritel" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Input.Klasa" class="form-label"></label>
                                        <select asp-for="Input.Klasa" asp-items="Model.Klasi" class="form-select">
                                            <option value="">-- Избери класа --</option>
                                        </select>
                                        <span asp-validation-for="Input.Klasa" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Input.Proizvod" class="form-label"></label>
                                        <select asp-for="Input.Proizvod" asp-items="Model.Produkti" class="form-select">
                                            <option value="">-- Избери производ --</option>
                                        </select>
                                        <span asp-validation-for="Input.Proizvod" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Input.DogovoruvacImePrezimeNaziv" class="form-label"></label>
                                        <input asp-for="Input.DogovoruvacImePrezimeNaziv" class="form-control" />
                                        <span asp-validation-for="Input.DogovoruvacImePrezimeNaziv" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Input.EMBGMB" class="form-label"></label>
                                        <input asp-for="Input.EMBGMB" class="form-control" />
                                        <span asp-validation-for="Input.EMBGMB" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Input.Osigurenik" class="form-label"></label>
                                        <input asp-for="Input.Osigurenik" class="form-control" />
                                        <span asp-validation-for="Input.Osigurenik" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Input.PredmetNaOsiguruvanje" class="form-label"></label>
                                        <input asp-for="Input.PredmetNaOsiguruvanje" class="form-control" />
                                        <span asp-validation-for="Input.PredmetNaOsiguruvanje" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Input.OsiguritelnoPokriteOd" class="form-label"></label>
                                        <input asp-for="Input.OsiguritelnoPokriteOd" type="date" class="form-control" />
                                        <span asp-validation-for="Input.OsiguritelnoPokriteOd" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Input.OsiguritelnoPokriteDo" class="form-label"></label>
                                        <input asp-for="Input.OsiguritelnoPokriteDo" type="date" class="form-control" />
                                        <span asp-validation-for="Input.OsiguritelnoPokriteDo" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label asp-for="Input.Provizija" class="form-label"></label>
                                        <input asp-for="Input.Provizija" class="form-control" />
                                        <span asp-validation-for="Input.Provizija" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label asp-for="Input.PremijaGodishno" class="form-label"></label>
                                        <input asp-for="Input.PremijaGodishno" class="form-control" />
                                        <span asp-validation-for="Input.PremijaGodishno" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label asp-for="Input.PremijaVkupno" class="form-label"></label>
                                        <input asp-for="Input.PremijaVkupno" class="form-control" />
                                        <span asp-validation-for="Input.PremijaVkupno" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label asp-for="Input.Zabeleshka" class="form-label"></label>
                                        <textarea asp-for="Input.Zabeleshka" class="form-control" rows="3"></textarea>
                                        <span asp-validation-for="Input.Zabeleshka" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save"></i> Зачувај
                                    </button>
                                    <button type="button" class="btn btn-secondary" data-bs-toggle="collapse" data-bs-target="#addEntryForm">
                                        <i class="fas fa-times"></i> Откажи
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Data Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-table"></i> Листа на записи
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.Entries.Any())
                    {
                        <div class="table-responsive">
                            <table id="brokerskiSlipTable" class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Датум</th>
                                        <th>Корисник</th>
                                        <th>Брокерско друштво</th>
                                        <th>Осигурител</th>
                                        <th>Класа</th>
                                        <th>Производ</th>
                                        <th>Договорувач</th>
                                        <th>ЕМБ/МБ</th>
                                        <th>Осигуреник</th>
                                        <th>Предмет</th>
                                        <th>Покритие од</th>
                                        <th>Покритие до</th>
                                        <th>Провизија</th>
                                        <th>Премија годишно</th>
                                        <th>Премија вкупно</th>
                                        <th>Забелешка</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var entry in Model.Entries)
                                    {
                                        <tr>
                                            <td>@entry.DateCreated?.ToString("dd.MM.yyyy HH:mm")</td>
                                            <td>@entry.UsernameCreated</td>
                                            <td>@entry.OsiguritelnoBrokerskoDrustvo</td>
                                            <td>@entry.Osiguritel</td>
                                            <td>@entry.Klasa</td>
                                            <td>@entry.Proizvod</td>
                                            <td>@entry.DogovoruvacImePrezimeNaziv</td>
                                            <td>@entry.EMBGMB</td>
                                            <td>@entry.Osigurenik</td>
                                            <td>@entry.PredmetNaOsiguruvanje</td>
                                            <td>@entry.OsiguritelnoPokriteOd</td>
                                            <td>@entry.OsiguritelnoPokriteDo</td>
                                            <td>@entry.Provizija</td>
                                            <td>@entry.PremijaGodishno</td>
                                            <td>@entry.PremijaVkupno</td>
                                            <td>@entry.Zabeleshka</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Нема записи за прикажување.</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Initialize DataTable with search functionality
            $('#brokerskiSlipTable').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/mk.json"
                },
                "pageLength": 25,
                "order": [[0, "desc"]], // Sort by date descending
                "columnDefs": [
                    { "orderable": false, "targets": [15] } // Disable sorting for Zabeleshka column
                ],
                "scrollX": true,
                "responsive": true
            });
        });
    </script>
}