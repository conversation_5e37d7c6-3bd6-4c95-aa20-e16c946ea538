@page
@model NextBroker.Pages.Polisi.DodajPolisaKlasa14Model
@{
    ViewData["Title"] = "Додај полиса Класа 14";
}

<h1 style='text-align: center;'>@ViewData["Title"]</h1>

<div class="container-fluid mt-4 px-4">
    <!-- Add this at the top of the form, after the opening <form> tag -->
    <div class="alert alert-success alert-dismissible fade" role="alert" id="successMessage" style="display:none;">
	        <strong>Успешно!</strong> Полисата е успешно зачувана.
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>

    <div class="alert alert-danger alert-dismissible fade" role="alert" id="errorMessage" style="display:none;">
        <strong>Грешка!</strong> <span id="errorText"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>

    <form method="post" id="polisaForm" class="dodaj-polisa-form">
        @Html.AntiForgeryToken()
        
        <!-- Add validation summary -->
        <div asp-validation-summary="All" class="text-danger"></div>

        <div class="card mb-3">
            <div class="card-header">
                <h5 class="mb-0">Основни информации</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="Input.KlientiIdOsiguritel" class="control-label"></label>
                            <select asp-for="Input.KlientiIdOsiguritel" class="form-control" asp-items="Model.Osiguriteli">
                                <option value="">-- Изберете осигурител --</option>
                            </select>
                            <span asp-validation-for="Input.KlientiIdOsiguritel" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="Input.KlasiOsiguruvanjeIdKlasa" class="control-label"></label>
                            <select asp-for="Input.KlasiOsiguruvanjeIdKlasa" class="form-control" asp-items="Model.KlasiOsiguruvanje">
                                <option value="">-- Изберете класа --</option>
                            </select>
                            <span asp-validation-for="Input.KlasiOsiguruvanjeIdKlasa" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="Input.ProduktiIdProizvod" class="control-label"></label>
                            <select asp-for="Input.ProduktiIdProizvod" class="form-control" asp-items="Model.Produkti">
                                <option value="">-- Изберете продукт --</option>
                            </select>
                            <span asp-validation-for="Input.ProduktiIdProizvod" class="text-danger"></span>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="Input.BrojNaPolisa" class="control-label"></label>
                            <input asp-for="Input.BrojNaPolisa" class="form-control" id="brojNaPolisaInput" required />
                            <span asp-validation-for="Input.BrojNaPolisa" class="text-danger"></span>
                            <span id="brojNaPolisaExistsError" class="text-danger" style="display:none;">Полиса со овој број веќе постои.</span>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-10 mb-3">
                        <label class="form-label">Договорувач (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                        <div class="input-group">
                            <button type="button" class="btn btn-outline-secondary btn-sm clear-field" 
                                    data-target="dogovoruvac" 
                                    style="width: 30px; height: 30px; padding: 0; margin-right: 5px;">
                                <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                            </button>
                            <input type="text" id="dogovoruvacMBSearch" class="form-control" 
                                   autocomplete="off"
                                   placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." />
                            <input type="hidden" asp-for="Input.KlientiIdDogovoruvac" id="KlientiIdDogovoruvac" />
                        </div>
                        <div id="dogovoruvacSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                    </div>
                </div>
                <div id="osigurenikContainer" class="row">
                    <div class="col-md-10 mb-3">
                        <label class="form-label">Осигуреник (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив) <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <button type="button" class="btn btn-outline-secondary btn-sm clear-field" 
                                    data-target="osigurenik" 
                                    style="width: 30px; height: 30px; padding: 0; margin-right: 5px;">
                                <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                            </button>
                            <input type="text" id="osigurenikMBSearch" class="form-control" 
                                   autocomplete="off"
                                   placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." required />
                            <input type="hidden" asp-for="Input.KlientiIdOsigurenik" id="KlientiIdOsigurenik" required />
                        </div>
                        <div id="osigurenikSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                        <span class="text-danger field-validation-valid" data-valmsg-for="Input.KlientiIdOsigurenik" data-valmsg-replace="true"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-10 mb-3">
                        <label class="form-label">Соработник/вработен (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                        <div class="input-group">
                            <button type="button" class="btn btn-outline-secondary btn-sm clear-field" 
                                    data-target="sorabotnik" 
                                    style="width: 30px; height: 30px; padding: 0; margin-right: 5px;">
                                <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                            </button>
                            <input type="text" id="sorabotnikMBSearch" class="form-control" 
                                   autocomplete="off"
                                   placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." />
                            <input type="hidden" asp-for="Input.KlientiIdSorabotnik" id="KlientiIdSorabotnik" />
                        </div>
                        <div id="sorabotnikSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-check mb-3">
                            <input type="checkbox" class="form-check-input" asp-for="Input.Faktoring" id="faktoring">
                            <label class="form-check-label" asp-for="Input.Faktoring">Факторинг</label>
                        </div>
                        <div class="form-check mb-3" style="display: none;">
                            <input type="checkbox" class="form-check-input" asp-for="Input.GeneriranaFakturaIzlezna" id="generiranaFakturaIzlezna">
                            <label class="form-check-label" asp-for="Input.GeneriranaFakturaIzlezna">Генерирана излезна фактура</label>
                        </div>
               
                        <!-- Add hidden fields to ensure values are still submitted -->
                        <div style="display: none;">
                            <input asp-for="Input.Storno" type="hidden" value="false" />
                            <input asp-for="Input.SifrarnikPricinaZaStornoId" type="hidden" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

       

        <!-- Move these rows to be last in the form, just before closing </div></div></form> -->

        
        <div class="row mt-3">
            <div class="col-md-4">
                <div class="form-group">
                    <label asp-for="Input.VaziOd" class="control-label"></label>
                    <input asp-for="Input.VaziOd" class="form-control" type="date" />
                    <span asp-validation-for="Input.VaziOd" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label asp-for="Input.VaziDo" class="control-label"></label>
                    <input asp-for="Input.VaziDo" class="form-control" type="date" />
                    <span asp-validation-for="Input.VaziDo" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label asp-for="Input.DatumNaIzdavanje" class="control-label"></label>
                    <input asp-for="Input.DatumNaIzdavanje" class="form-control" type="date" />
                    <span asp-validation-for="Input.DatumNaIzdavanje" class="text-danger"></span>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="Input.ValutiId" class="control-label"></label>
                    <select asp-for="Input.ValutiId" class="form-select" asp-items="Model.Valuti">
                        <option value="">-- Избери валута --</option>
                    </select>
                    <span asp-validation-for="Input.ValutiId" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="Input.NaciniNaPlakanjeId" class="control-label"></label>
                    <select asp-for="Input.NaciniNaPlakanjeId" class="form-select" asp-items="Model.NaciniNaPlakanje">
                        <option value="">-- Избери начин на плаќање --</option>
                    </select>
                    <span asp-validation-for="Input.NaciniNaPlakanjeId" class="text-danger"></span>
                </div>
            </div>
           
            <!-- Add hidden fields to ensure values are still submitted -->
            <div style="display: none;">
                <input asp-for="Input.TipoviNaPlakanjeId" type="hidden" value="1" />
                <input asp-for="Input.BankiId" type="hidden" />
            </div>
        </div>

        <!-- Add these fields after the existing fields -->
        <div class="row mt-3">
            <div class="col-md-4">
                <div class="form-group">
                    <label asp-for="Input.TipNaFaktura" class="control-label">Тип на фактура</label>
                    <select asp-for="Input.TipNaFaktura" 
                            asp-items="Model.TipoviNaFaktura" 
                            class="form-select"
                            required>
                        <option value="">-- Избери тип на фактура --</option>
                    </select>
                    <span asp-validation-for="Input.TipNaFaktura" class="text-danger"></span>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-6" style="display: none;">
                <div class="form-group">
                    <label asp-for="Input.BrojNaVleznaFaktura" class="control-label">Број на влезна фактура</label>
                    <input asp-for="Input.BrojNaVleznaFaktura" class="form-control" />
                    <span asp-validation-for="Input.BrojNaVleznaFaktura" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-6" style="display: none;">
                <div class="form-group">
                    <label asp-for="Input.BrojNaIzleznaFaktura" class="control-label">Број на излезна фактура</label>
                    <input asp-for="Input.BrojNaIzleznaFaktura" class="form-control" />
                    <span asp-validation-for="Input.BrojNaIzleznaFaktura" class="text-danger"></span>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-6" style="display: none;">
                <div class="form-group">
                    <label asp-for="Input.DatumNaVleznaFaktura" class="control-label">Датум на влезна фактура</label>
                    <input asp-for="Input.DatumNaVleznaFaktura" class="form-control" type="date" />
                    <span asp-validation-for="Input.DatumNaVleznaFaktura" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-6" style="display: none;">
                <div class="form-group">
                    <label asp-for="Input.DatumNaIzleznaFaktura" class="control-label">Датум на излезна фактура</label>
                    <input asp-for="Input.DatumNaIzleznaFaktura" class="form-control" type="date" />
                    <span asp-validation-for="Input.DatumNaIzleznaFaktura" class="text-danger"></span>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-6" style="display: none;">
                <div class="form-group">
                    <label asp-for="Input.RokNaPlakanjeVleznaFaktura" class="control-label">Рок на плаќање влезна фактура</label>
                    <input asp-for="Input.RokNaPlakanjeVleznaFaktura" class="form-control" type="date" />
                    <span asp-validation-for="Input.RokNaPlakanjeVleznaFaktura" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-6" style="display: none;">
                <div class="form-group">
                    <label asp-for="Input.RokNaPlakanjeIzleznaFaktura" class="control-label">Рок на плаќање излезна фактура</label>
                    <input asp-for="Input.RokNaPlakanjeIzleznaFaktura" class="form-control" type="date" />
                    <span asp-validation-for="Input.RokNaPlakanjeIzleznaFaktura" class="text-danger"></span>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="Input.SifrarnikValutiIdFranshizaValuta" class="control-label"></label>
                    <select asp-for="Input.SifrarnikValutiIdFranshizaValuta" 
                            class="form-select" 
                            asp-items="Model.Valuti">
                        <option value="">-- Изберете валута --</option>
                    </select>
                    <span asp-validation-for="Input.SifrarnikValutiIdFranshizaValuta" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="Input.ProcentFransiza" class="control-label">Процент франшиза</label>
                    <input asp-for="Input.ProcentFransiza" class="form-control" type="number" step="0.01" />
                    <span asp-validation-for="Input.ProcentFransiza" class="text-danger"></span>
                </div>
            </div>
                        <div class="col-md-3 mb-3">
                <label asp-for="Input.FranshizaIznos" class="form-label"></label>
                <div class="input-group">
                    <input asp-for="Input.FranshizaIznos" 
                           class="form-control decimal-input" 
                           type="number" 
                           step="0.0001" />
                </div>
                <span asp-validation-for="Input.FranshizaIznos" class="text-danger"></span>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-6">
                <div class="form-group">
                    <label asp-for="Input.KoregiranaStapkaNaProvizija" class="control-label">Корегирана стапка на провизија</label>
                    <input asp-for="Input.KoregiranaStapkaNaProvizija" class="form-control" type="number" step="0.01" />
                    <span asp-validation-for="Input.KoregiranaStapkaNaProvizija" class="text-danger"></span>
                </div>
            </div>
        </div>

        <!-- Add new card section for Klasa 14 insurance -->
        <div class="card mb-3 mt-4">
            <div class="card-header">
                <h5 class="mb-0">Осигурување Класа 14</h5>
            </div>
            <div class="card-body">
               
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Input.OsigurenaSuma" class="control-label">Осигурена сума</label>
                                    <input asp-for="Input.OsigurenaSuma" class="form-control" type="number" step="0.0001" />
                                    <span asp-validation-for="Input.OsigurenaSuma" class="text-danger"></span>
                                </div>
                            </div>  
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Input.Premija" class="control-label">Премија</label>
                                    <input asp-for="Input.Premija" class="form-control" type="number" step="0.0001" />
                                    <span asp-validation-for="Input.Premija" class="text-danger"></span>
                                </div>
                            </div>                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Input.Opis" class="control-label">Опис</label>
                                    <input asp-for="Input.Opis" class="form-control" />
                                    <span asp-validation-for="Input.Opis" class="text-danger"></span>
                                </div>
                            </div>                          
                                                   
                        <div class="form-group">
                            <label asp-for="Input.VkupnaPremija" class="control-label"></label>
                            <input asp-for="Input.VkupnaPremija" class="form-control" type="number" step="0.0001" style="background-color: #f8f9fa;" />
                            <span asp-validation-for="Input.VkupnaPremija" class="text-danger"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        
                    <!-- Popusti Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Попусти</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.ProcentNaPopustZaFakturaVoRok" class="form-label">Процент на попуст за фактура во рок</label>
                        <input asp-for="Input.ProcentNaPopustZaFakturaVoRok" class="form-control decimal-input" type="number" step="0.01" />
                        <span asp-validation-for="Input.ProcentNaPopustZaFakturaVoRok" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.IznosZaPlakjanjeVoRok" class="form-label">Износ за плаќање во рок</label>
                        <input asp-for="Input.IznosZaPlakjanjeVoRok" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.IznosZaPlakjanjeVoRok" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.ProcentKomercijalenPopust" class="form-label">Процент комерцијален попуст</label>
                        <input asp-for="Input.ProcentKomercijalenPopust" class="form-control decimal-input" type="number" step="0.01" />
                        <span asp-validation-for="Input.ProcentKomercijalenPopust" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.ProcentFinansiski" class="form-label">Процент финансиски</label>
                        <input asp-for="Input.ProcentFinansiski" class="form-control decimal-input" type="number" step="0.01" />
                        <span asp-validation-for="Input.ProcentFinansiski" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.PremijaZaNaplata" class="form-label">Премија за наплата</label>
                        <input asp-for="Input.PremijaZaNaplata" class="form-control decimal-input" type="number" step="0.0001" style="background-color: #f8f9fa;" />
                        <span asp-validation-for="Input.PremijaZaNaplata" class="text-danger"></span>
                    </div>
                </div>
            </div>
        </div>



        <!-- Add this before the closing </form> tag -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="d-flex justify-content-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Зачувај полиса
                    </button>
                </div>
            </div>
        </div>
    </form>

    <!-- Add this after the form, before the Scripts section -->
    <div class="alert alert-success alert-dismissible fade" role="alert" id="successMessage" style="display:none;">
	        <strong>Успешно!</strong> Полисата е успешно зачувана.
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
</div>

@section Styles {
    <style>
        #dogovoruvacSearchResults .list-group-item.text-center,
        #osigurenikSearchResults .list-group-item.text-center,
        #sorabotnikSearchResults .list-group-item.text-center {
            padding: 1rem;
        }
        #dogovoruvacSearchResults .list-group-item.text-center p,
        #osigurenikSearchResults .list-group-item.text-center p,
        #sorabotnikSearchResults .list-group-item.text-center p {
            color: #6c757d;
            margin-bottom: 0.75rem;
        }
        #dogovoruvacSearchResults .btn-primary,
        #osigurenikSearchResults .btn-primary,
        #sorabotnikSearchResults .btn-primary {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }
    </style>
}

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        // Polisa Klasa 9 Calculations
        $(document).ready(function() {
            // Function to calculate total premium
            function calculateTotalPremium() {
                let total = 0;
                
                // Add all the values
                total += parseFloat($('#Input_OsnovnaPremija').val() || 0);
                total += parseFloat($('#Input_DopolnitelnaPremija').val() || 0);

                // Update the total premium field
                $('#Input_VkupnaPremija').val(total.toFixed(4));
            }

            // Function to calculate discounts
            function calculateDiscounts() {
                const vkupnaPremija = parseFloat($('#Input_VkupnaPremija').val() || 0);
                const procentNaPopustZaFakturaVoRok = parseFloat($('#Input_ProcentNaPopustZaFakturaVoRok').val() || 0);
                const procentKomercijalenPopust = parseFloat($('#Input_ProcentKomercijalenPopust').val() || 0);
                const procentFinansiski = parseFloat($('#Input_ProcentFinansiski').val() || 0);

                // Calculate discount for payment within deadline
                const iznosZaPlakjanjeVoRok = vkupnaPremija * (1 - procentNaPopustZaFakturaVoRok / 100);
                $('#Input_IznosZaPlakjanjeVoRok').val(iznosZaPlakjanjeVoRok.toFixed(4));

                // Calculate final premium after all discounts
                let premijaZaNaplata = vkupnaPremija;
                premijaZaNaplata *= (1 - procentKomercijalenPopust / 100);
                premijaZaNaplata *= (1 - procentFinansiski / 100);
                $('#Input_PremijaZaNaplata').val(premijaZaNaplata.toFixed(4));
            }

            // Add event listeners for all relevant input fields
            const calculationFields = [
                'Input_Vrednost',
                'Input_SumaNaOsiguruvanje',
                'Input_OsiguruvanjeNaObjektIPomoshniProstorii',
                'Input_OdgovornostKonTretiLica',
                'Input_TroshociZaNuznoSmestuvanje',
                'Input_PredmetiVoDomakjinstvo',
                'Input_Krazhba',
                'Input_KrsenjeStaklo',
                'Input_DopolnitelniTroshoci',
                'Input_OsnovnaPremija',
                'Input_DopolnitelnaPremija'
            ];

            const discountFields = [
                'Input_ProcentNaPopustZaFakturaVoRok',
                'Input_ProcentKomercijalenPopust',
                'Input_ProcentFinansiski'
            ];

            // Add event listeners for calculation fields
            calculationFields.forEach(fieldId => {
                $(`#${fieldId}`).on('input', function() {
                    calculateTotalPremium();
                    calculateDiscounts();
                });
            });

            // Add event listeners for discount fields
            discountFields.forEach(fieldId => {
                $(`#${fieldId}`).on('input', function() {
                    calculateDiscounts();
                });
            });

            // Initial calculations
            calculateTotalPremium();
            calculateDiscounts();
        });

        // Define the function globally
        function openAddClientWindow(sourceField) {
            const width = 800;
            const height = 600;
            const left = (window.screen.width - width) / 2;
            const top = (window.screen.height - height) / 2;
            
            const popup = window.open(`/Klienti/DodajKlient?fromPolisa=true&source=${sourceField}`, 'DodajKlient', 
                `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes`);
                
            // Listen for messages from the popup
            window.addEventListener('message', function(event) {
                if (event.data.type === 'clientAdded') {
                    // Clear the search field based on the source
                    if (event.data.source === 'dogovoruvac') {
                        $('#dogovoruvacMBSearch').val('');
                        $('#KlientiIdDogovoruvac').val('');
                        $('#dogovoruvacSearchResults').hide();
                    } else if (event.data.source === 'osigurenik') {
                        $('#osigurenikMBSearch').val('');
                        $('#KlientiIdOsigurenik').val('');
                        $('#osigurenikSearchResults').hide();
                    } else if (event.data.source === 'sorabotnik') {
                        $('#sorabotnikMBSearch').val('');
                        $('#KlientiIdSorabotnik').val('');
                        $('#sorabotnikSearchResults').hide();
                    }
                }
            });
        }

        $(document).ready(function() {
            // Function to create search functionality
            function createSearchFunctionality(searchInputId, resultsContainerId, hiddenInputId, searchHandler = 'SearchKlienti') {
                let searchTimeout;
                const antiForgeryToken = $('input[name="__RequestVerificationToken"]').val();

                $(`#${searchInputId}`).on('input', function() {
                    clearTimeout(searchTimeout);
                    const searchTerm = $(this).val();
                    const resultsDiv = $(`#${resultsContainerId}`);

                    if (searchTerm.length < 1) {
                        resultsDiv.hide();
                        return;
                    }

                    searchTimeout = setTimeout(function() {
                        $.ajax({
                            url: `?handler=${searchHandler}`,
                            type: 'GET',
                            data: { mb: searchTerm },
                            headers: {
                                "RequestVerificationToken": antiForgeryToken
                            },
                            success: function(data) {
                                if (data && data.length > 0) {
                                    let html = '<div class="list-group">';
                                    data.forEach(item => {
                                        let displayText = '';
                                        if (item.tip === 'P') {
                                            displayText = `${item.naziv}`;
                                            let identifiers = [];
                                            if (item.mb) identifiers.push(`МБ: ${item.mb}`);
                                            if (item.edb) identifiers.push(`ЕДБ: ${item.edb}`);
                                            if (identifiers.length > 0) {
                                                displayText += ` (${identifiers.join(', ')})`;
                                            }
                                        } else {
                                            displayText = `${item.ime} ${item.prezime}`;
                                            let identifiers = [];
                                            if (item.embg) identifiers.push(`ЕМБГ: ${item.embg}`);
                                            if (item.mb) identifiers.push(`МБ: ${item.mb}`);
                                            if (identifiers.length > 0) {
                                                displayText += ` (${identifiers.join(', ')})`;
                                            }
                                        }
                                        html += `<a href="#" class="list-group-item list-group-item-action" data-id="${item.id}">
                                                  ${displayText}
                                               </a>`;
                                    });
                                    html += '</div>';
                                    resultsDiv.html(html).show();
                                } else {
                                    resultsDiv.html(`
                                        <div class="list-group">
                                            <div class="list-group-item text-center">
                                                <p class="mb-2">Нема пронајдени резултати</p>
                                                <button type="button" class="btn btn-primary" onclick="openAddClientWindow('${
                                                    searchInputId === 'dogovoruvacMBSearch' ? 'dogovoruvac' : 
                                                    searchInputId === 'osigurenikMBSearch' ? 'osigurenik' : 'sorabotnik'
                                                }')">
                                                    <i class="fas fa-plus"></i> Додај клиент
                                                </button>
                                            </div>
                                        </div>
                                    `).show();
                                }
                            },
                            error: function() {
                                resultsDiv.html(`
                                    <div class="list-group">
                                        <div class="list-group-item text-center">
                                            <p class="text-danger mb-2">Грешка при пребарување</p>
                                            <button type="button" class="btn btn-primary" onclick="openAddClientWindow('${
                                                searchInputId === 'dogovoruvacMBSearch' ? 'dogovoruvac' : 
                                                searchInputId === 'osigurenikMBSearch' ? 'osigurenik' : 'sorabotnik'
                                            }')">
                                                <i class="fas fa-plus"></i> Додај клиент
                                            </button>
                                        </div>
                                    </div>
                                `).show();
                            }
                        });
                    }, 300);
                });

                // Handle selection
                $(document).on('click', `#${resultsContainerId} .list-group-item`, function(e) {
                    e.preventDefault();
                    const id = $(this).data('id');
                    const displayText = $(this).text();
                    $(`#${searchInputId}`).val(displayText.trim());
                    $(`#${hiddenInputId}`).val(id);
                    $(`#${resultsContainerId}`).hide();
                });

                // Hide results when clicking outside
                $(document).on('click', function(e) {
                    if (!$(e.target).closest(`#${searchInputId}, #${resultsContainerId}`).length) {
                        $(`#${resultsContainerId}`).hide();
                    }
                });
            }

            // Initialize search for all fields
            createSearchFunctionality('dogovoruvacMBSearch', 'dogovoruvacSearchResults', 'KlientiIdDogovoruvac');
            createSearchFunctionality('osigurenikMBSearch', 'osigurenikSearchResults', 'KlientiIdOsigurenik');
            createSearchFunctionality('sorabotnikMBSearch', 'sorabotnikSearchResults', 'KlientiIdSorabotnik', 'SearchSorabotnici');

            // Update click handlers for clear buttons
            $('.clear-field').click(function() {
                const target = $(this).data('target');
                switch(target) {
                    case 'dogovoruvac':
                        $('#dogovoruvacMBSearch').val('');
                        $('#KlientiIdDogovoruvac').val('');
                        $('#dogovoruvacSearchResults').hide();
                        break;
                    case 'osigurenik':
                        $('#osigurenikMBSearch').val('');
                        $('#KlientiIdOsigurenik').val('');
                        $('#osigurenikSearchResults').hide();
                        break;
                    case 'sorabotnik':
                        $('#sorabotnikMBSearch').val('');
                        $('#KlientiIdSorabotnik').val('');
                        $('#sorabotnikSearchResults').hide();
                        break;
                }
            });

         
            // Initialize select2 for PricinaZaStorno dropdown
            $('#Input_SifrarnikPricinaZaStornoId').select2({
                width: '100%'
            });

            // Add custom validation rules
            $.validator.setDefaults({
                ignore: []
            });

            // Mark BrojNaVleznaFaktura as optional
            $('#Input_BrojNaVleznaFaktura').rules('remove', 'required');

            // Add form submit handler
            $('#polisaForm').submit(function(e) {
                // Don't prevent default - let the form submit normally
                
                // Show success message
                $('#successMessage, #bottomSuccessMessage')
                    .show()
                    .addClass('show');
                
                // Disable submit button
                $(this).find('button[type="submit"]').prop('disabled', true);
                
                // Let form submit proceed
                return true;
            });

            // Add custom validation for Osigurenik
            $.validator.addMethod("requiredOsigurenik", function(value, element) {
                return $('#KlientiIdOsigurenik').val() !== '';
            }, "Осигуреник е задолжително поле");

            // Add validation rules
            $('#polisaForm').validate({
                rules: {
                    'Input.KlientiIdOsigurenik': {
                        requiredOsigurenik: true
                    }
                },
                messages: {
                    'Input.KlientiIdOsigurenik': {
                        requiredOsigurenik: "Осигуреник е задолжително поле"
                    }
                }
            });
        });

         // Add this at the very end
        function checkPolicyNumber() {
            var brojNaPolisa = $('#brojNaPolisaInput').val();
            if (brojNaPolisa) {
                $.ajax({
                    url: '?handler=CheckBrojNaPolisa',
                    type: 'GET',
                    data: { brojNaPolisa: brojNaPolisa },
                    headers: {
                        "RequestVerificationToken": 
                            $('input:hidden[name="__RequestVerificationToken"]').val()
                    },
                    success: function(result) {
                        if (result > 0) {
                            $('#brojNaPolisaExistsError').show();
                        } else {
                            $('#brojNaPolisaExistsError').hide();
                        }
                    },
                    error: function() {
                        $('#brojNaPolisaExistsError').hide();
                    }
                });
            } else {
                $('#brojNaPolisaExistsError').hide();
            }
        }

        // Attach the event handler after page load
        $(function() {
            $('#brojNaPolisaInput').on('blur', checkPolicyNumber);
        });

        // Define the validation function
        function validateDates() {
            var vaziOd = $('#Input_VaziOd').val();
            var vaziDo = $('#Input_VaziDo').val();
            
            if (vaziOd && vaziDo) {
                var dateVaziOd = new Date(vaziOd);
                var dateVaziDo = new Date(vaziDo);
                
                if (dateVaziDo < dateVaziOd) {
                    // Add error message if it doesn't exist
                    if (!$('#vaziDoError').length) {
                        $('#Input_VaziDo').after('<span id="vaziDoError" class="text-danger">Датумот "Важи до" не може да биде пред "Важи од"</span>');
                    }
                    // Set VaziDo to VaziOd
                    $('#Input_VaziDo').val(vaziOd);
                    return false;
                } else {
                    // Remove error message if exists
                    $('#vaziDoError').remove();
                    return true;
                }
            }
            return true;
        }

        // Attach the event handler after page load
        $(function() {
            $('#Input_VaziOd, #Input_VaziDo').on('blur change', validateDates);
            
            // Run initial validation
            validateDates();
        });

        $(document).ready(function() {
            // Add these new functions
            function calculateMonthsBetween(startDate, endDate) {
                const start = new Date(startDate);
                const end = new Date(endDate);
                return (end.getFullYear() - start.getFullYear()) * 12 + (end.getMonth() - start.getMonth()) + 1;
            }

            function filterPaymentOptions() {
                const vaziOd = $('#Input_VaziOd').val();
                const vaziDo = $('#Input_VaziDo').val();
                
                if (vaziOd && vaziDo) {
                    const months = calculateMonthsBetween(vaziOd, vaziDo);
                    const paymentSelect = $('#Input_NaciniNaPlakanjeId');
                    
                    // Store original options if not already stored
                    if (!paymentSelect.data('original-options')) {
                        paymentSelect.data('original-options', paymentSelect.find('option').clone());
                    }
                    
                    // Reset options
                    paymentSelect.empty().append('<option value="">-- Избери начин на плаќање --</option>');
                    
                    // Get original options and filter them
                    const originalOptions = paymentSelect.data('original-options');
                    originalOptions.each(function() {
                        const $option = $(this);
                        if ($option.val() === '') return true; // Skip the placeholder option
                        
                        const optionText = $option.text().trim();
                        const rateMatch = optionText.match(/(\d+)\s+рати/);
                        
                        // Always include "Еднократно" option
                        if (optionText === 'Еднократно') {
                            paymentSelect.append($option.clone());
                            return true;
                        }
                        
                        // For rate options, check against policy duration
                        if (rateMatch) {
                            const rates = parseInt(rateMatch[1]);
                            if (rates <= months) {
                                paymentSelect.append($option.clone());
                            }
                        }
                    });
                }
            }

            // Add event listeners for date changes
            $('#Input_VaziOd, #Input_VaziDo').on('change', filterPaymentOptions);

            // Initial filtering on page load
            filterPaymentOptions();
        });
    </script>
} 