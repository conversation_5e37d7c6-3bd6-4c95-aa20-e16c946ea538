using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace NextBroker.Pages.Finansii
{
    public class BrokerskiSlipModel : SecurePageModel
    {
        public BrokerskiSlipModel(IConfiguration configuration)
            : base(configuration)
        {
        }

        [BindProperty]
        public BrokerskiSlipInputModel Input { get; set; } = new();

        public IEnumerable<SelectListItem> Osiguriteli { get; set; } = new List<SelectListItem>();
        public IEnumerable<SelectListItem> Klasi { get; set; } = new List<SelectListItem>();
        public IEnumerable<SelectListItem> Produkti { get; set; } = new List<SelectListItem>();
        public List<BrokerskiSlipEntry> Entries { get; set; } = new List<BrokerskiSlipEntry>();

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("BrokerskiSlip"))
            {
                return RedirectToAccessDenied();
            }

            await LoadDropdownData();
            await LoadEntries();
            return Page();
        }

        public async Task<IActionResult> OnGetGenerateDocument(long id)
        {
            if (!await HasPageAccess("BrokerskiSlip"))
            {
                return RedirectToAccessDenied();
            }

            try
            {
                // Get the specific entry by ID
                var entry = await GetBrokerskiSlipEntryById(id);
                if (entry == null)
                {
                    return new JsonResult(new { success = false, error = "Записот не е пронајден." });
                }

                // Generate HTML document
                var documentHtml = GenerateBrokerskiSlipHtml(entry);

                // Return the HTML as JSON for AJAX consumption
                return new JsonResult(new { success = true, html = documentHtml });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, error = ex.Message });
            }
        }

        public async Task<IActionResult> OnPost()
        {
            if (!await HasPageAccess("BrokerskiSlip"))
            {
                return RedirectToAccessDenied();
            }

            if (!ModelState.IsValid)
            {
                await LoadDropdownData();
                await LoadEntries();
                return Page();
            }

            string username = HttpContext.Session.GetString("Username");
            if (string.IsNullOrEmpty(username))
            {
                TempData["ErrorMessage"] = "Вашата сесија е истечена. Најавете се повторно.";
                return RedirectToPage("/Account/Login");
            }

            try
            {
                await InsertBrokerskiSlip(username);
                TempData["SuccessMessage"] = "Брокерскиот слип е успешно додаден.";
                return RedirectToPage();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Грешка при додавање: {ex.Message}";
                await LoadDropdownData();
                await LoadEntries();
                return Page();
            }
        }

        private async Task LoadDropdownData()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                // Load Osiguriteli
                using (SqlCommand cmd = new SqlCommand("SELECT Naziv FROM Klienti WHERE Osiguritel = 1", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var osiguriteli = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        string naziv = reader["Naziv"].ToString();
                        osiguriteli.Add(new SelectListItem(naziv, naziv));
                    }
                    Osiguriteli = osiguriteli;
                }

                // Load Klasi
                using (SqlCommand cmd = new SqlCommand("SELECT KlasaIme FROM KlasiOsiguruvanje", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var klasi = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        string klasaIme = reader["KlasaIme"].ToString();
                        klasi.Add(new SelectListItem(klasaIme, klasaIme));
                    }
                    Klasi = klasi;
                }

                // Load Produkti
                using (SqlCommand cmd = new SqlCommand("SELECT Ime FROM Produkti", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var produkti = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        string ime = reader["Ime"].ToString();
                        produkti.Add(new SelectListItem(ime, ime));
                    }
                    Produkti = produkti;
                }
            }
        }

        private async Task LoadEntries()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, DateCreated, UsernameCreated, OsiguritelnoBrokerskoDrustvo,
                           Osiguritel, Klasa, Proizvod, DogovoruvacImePrezimeNaziv,
                           EMBGMB, Osigurenik, PredmetNaOsiguruvanje,
                           OsiguritelnoPokriteOd, OsiguritelnoPokriteDo,
                           Provizija, PremijaGodishno, PremijaVkupno, Zabeleshka
                    FROM BrokerskiSlip
                    ORDER BY DateCreated DESC", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var entries = new List<BrokerskiSlipEntry>();
                    while (await reader.ReadAsync())
                    {
                        entries.Add(new BrokerskiSlipEntry
                        {
                            Id = (long)reader["Id"],
                            DateCreated = reader["DateCreated"] as DateTime?,
                            UsernameCreated = reader["UsernameCreated"] as string,
                            OsiguritelnoBrokerskoDrustvo = reader["OsiguritelnoBrokerskoDrustvo"] as string,
                            Osiguritel = reader["Osiguritel"] as string,
                            Klasa = reader["Klasa"] as string,
                            Proizvod = reader["Proizvod"] as string,
                            DogovoruvacImePrezimeNaziv = reader["DogovoruvacImePrezimeNaziv"] as string,
                            EMBGMB = reader["EMBGMB"] as string,
                            Osigurenik = reader["Osigurenik"] as string,
                            PredmetNaOsiguruvanje = reader["PredmetNaOsiguruvanje"] as string,
                            OsiguritelnoPokriteOd = reader["OsiguritelnoPokriteOd"] as string,
                            OsiguritelnoPokriteDo = reader["OsiguritelnoPokriteDo"] as string,
                            Provizija = reader["Provizija"] as string,
                            PremijaGodishno = reader["PremijaGodishno"] as string,
                            PremijaVkupno = reader["PremijaVkupno"] as string,
                            Zabeleshka = reader["Zabeleshka"] as string
                        });
                    }
                    Entries = entries;
                }
            }
        }

        private async Task InsertBrokerskiSlip(string username)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    INSERT INTO BrokerskiSlip (
                        UsernameCreated, OsiguritelnoBrokerskoDrustvo, Osiguritel, Klasa, Proizvod,
                        DogovoruvacImePrezimeNaziv, EMBGMB, Osigurenik, PredmetNaOsiguruvanje,
                        OsiguritelnoPokriteOd, OsiguritelnoPokriteDo, Provizija,
                        PremijaGodishno, PremijaVkupno, Zabeleshka
                    ) VALUES (
                        @UsernameCreated, @OsiguritelnoBrokerskoDrustvo, @Osiguritel, @Klasa, @Proizvod,
                        @DogovoruvacImePrezimeNaziv, @EMBGMB, @Osigurenik, @PredmetNaOsiguruvanje,
                        @OsiguritelnoPokriteOd, @OsiguritelnoPokriteDo, @Provizija,
                        @PremijaGodishno, @PremijaVkupno, @Zabeleshka
                    )", connection))
                {
                    cmd.Parameters.AddWithValue("@UsernameCreated", username);
                    cmd.Parameters.AddWithValue("@OsiguritelnoBrokerskoDrustvo", (object)Input.OsiguritelnoBrokerskoDrustvo ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@Osiguritel", (object)Input.Osiguritel ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@Klasa", (object)Input.Klasa ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@Proizvod", (object)Input.Proizvod ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@DogovoruvacImePrezimeNaziv", (object)Input.DogovoruvacImePrezimeNaziv ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@EMBGMB", (object)Input.EMBGMB ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@Osigurenik", (object)Input.Osigurenik ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@PredmetNaOsiguruvanje", (object)Input.PredmetNaOsiguruvanje ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguritelnoPokriteOd", (object)Input.OsiguritelnoPokriteOd?.ToString("yyyy-MM-dd") ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguritelnoPokriteDo", (object)Input.OsiguritelnoPokriteDo?.ToString("yyyy-MM-dd") ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@Provizija", (object)Input.Provizija ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaGodishno", (object)Input.PremijaGodishno ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaVkupno", (object)Input.PremijaVkupno ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@Zabeleshka", (object)Input.Zabeleshka ?? DBNull.Value);

                    await cmd.ExecuteNonQueryAsync();
                }
            }
        }

        private async Task<BrokerskiSlipEntry?> GetBrokerskiSlipEntryById(long id)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, DateCreated, UsernameCreated, OsiguritelnoBrokerskoDrustvo,
                           Osiguritel, Klasa, Proizvod, DogovoruvacImePrezimeNaziv,
                           EMBGMB, Osigurenik, PredmetNaOsiguruvanje,
                           OsiguritelnoPokriteOd, OsiguritelnoPokriteDo, Provizija,
                           PremijaGodishno, PremijaVkupno, Zabeleshka
                    FROM BrokerskiSlip
                    WHERE Id = @Id", connection))
                {
                    cmd.Parameters.AddWithValue("@Id", id);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();

                    if (await reader.ReadAsync())
                    {
                        return new BrokerskiSlipEntry
                        {
                            Id = Convert.ToInt64(reader["Id"]),
                            DateCreated = reader["DateCreated"] as DateTime?,
                            UsernameCreated = reader["UsernameCreated"] as string,
                            OsiguritelnoBrokerskoDrustvo = reader["OsiguritelnoBrokerskoDrustvo"] as string,
                            Osiguritel = reader["Osiguritel"] as string,
                            Klasa = reader["Klasa"] as string,
                            Proizvod = reader["Proizvod"] as string,
                            DogovoruvacImePrezimeNaziv = reader["DogovoruvacImePrezimeNaziv"] as string,
                            EMBGMB = reader["EMBGMB"] as string,
                            Osigurenik = reader["Osigurenik"] as string,
                            PredmetNaOsiguruvanje = reader["PredmetNaOsiguruvanje"] as string,
                            OsiguritelnoPokriteOd = reader["OsiguritelnoPokriteOd"] as string,
                            OsiguritelnoPokriteDo = reader["OsiguritelnoPokriteDo"] as string,
                            Provizija = reader["Provizija"] as string,
                            PremijaGodishno = reader["PremijaGodishno"] as string,
                            PremijaVkupno = reader["PremijaVkupno"] as string,
                            Zabeleshka = reader["Zabeleshka"] as string
                        };
                    }
                }
            }
            return null;
        }

        private string GenerateBrokerskiSlipHtml(BrokerskiSlipEntry entry)
        {
            var html = $@"
                <div style=""font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; position: relative; padding: 20px 40px;"">
                    <!-- Background Logo -->
                    <div style=""position: absolute; top: 20px; right: 40px; width: 150px; opacity: 0.15; z-index: 0; pointer-events: none;"">
                        <img src=""/images/logo/INCO_LOGO_Regular.svg"" style=""width: 100%; height: auto;"" />
                    </div>

                    <!-- Decorative Corner Elements -->
                    <div style=""position: absolute; top: 10px; left: 10px; width: 20px; height: 20px; border-left: 1px solid rgba(212, 175, 55, 0.25); border-top: 1px solid rgba(212, 175, 55, 0.25);""></div>
                    <div style=""position: absolute; top: 10px; right: 10px; width: 20px; height: 20px; border-right: 1px solid rgba(212, 175, 55, 0.25); border-top: 1px solid rgba(212, 175, 55, 0.25);""></div>
                    <div style=""position: absolute; bottom: 10px; left: 10px; width: 20px; height: 20px; border-left: 1px solid rgba(212, 175, 55, 0.25); border-bottom: 1px solid rgba(212, 175, 55, 0.25);""></div>
                    <div style=""position: absolute; bottom: 10px; right: 10px; width: 20px; height: 20px; border-right: 1px solid rgba(212, 175, 55, 0.25); border-bottom: 1px solid rgba(212, 175, 55, 0.25);""></div>

                    <div style=""position: relative; z-index: 1;"">
                        <div style=""text-align: left; margin-bottom: 20px; padding-right: 160px;"">
                            <h3 style=""color: #2F4F4F; margin-bottom: 5px; font-size: 22px; text-shadow: 1px 1px 1px rgba(0,0,0,0.1);"">Осигурително Брокерско Друштво ИНКО АД Скопје</h3>
                            <h4 style=""color: #000; margin-top: 0; font-size: 18px;"">БРОКЕРСКИ СЛИП БРОЈ: {entry.Id}</h4>
                        </div>

                        <div style=""margin-bottom: 20px; background-color: rgba(47, 79, 79, 0.03); padding: 15px; border-radius: 6px; border: 1px solid rgba(212, 175, 55, 0.2); box-shadow: 0 1px 3px rgba(0,0,0,0.05);"">
                            <p style=""margin: 5px 0; font-size: 13px;""><strong style=""color: #2F4F4F; min-width: 180px; display: inline-block;"">Датум на креирање:</strong> {entry.DateCreated?.ToString("dd.MM.yyyy")}</p>
                            <p style=""margin: 5px 0; font-size: 13px;""><strong style=""color: #2F4F4F; min-width: 180px; display: inline-block;"">Креирано од:</strong> {entry.UsernameCreated}</p>
                        </div>

                        <table style=""width: 100%; border-collapse: collapse; margin-bottom: 20px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); background-color: white;"">
                            <thead>
                                <tr style=""background: linear-gradient(90deg, #2F4F4F, #1a2f2f); color: white;"">
                                    <th style=""border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 13px;"">Поле</th>
                                    <th style=""border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 13px;"">Вредност</th>
                                </tr>
                            </thead>
                            <tbody>";

            // Add data rows with alternating colors
            var fields = new[]
            {
                ("Осигурително брокерско друштво", entry.OsiguritelnoBrokerskoDrustvo),
                ("Осигурител", entry.Osiguritel),
                ("Класа", entry.Klasa),
                ("Производ", entry.Proizvod),
                ("Договорувач име/презиме/назив", entry.DogovoruvacImePrezimeNaziv),
                ("ЕМБ/МБ", entry.EMBGMB),
                ("Осигуреник", entry.Osigurenik),
                ("Предмет на осигурување", entry.PredmetNaOsiguruvanje),
                ("Осигурително покритие од", FormatDateString(entry.OsiguritelnoPokriteOd)),
                ("Осигурително покритие до", FormatDateString(entry.OsiguritelnoPokriteDo)),
                ("Провизија", entry.Provizija),
                ("Премија годишно", entry.PremijaGodishno),
                ("Премија вкупно", entry.PremijaVkupno),
                ("Забелешка", entry.Zabeleshka)
            };

            for (int i = 0; i < fields.Length; i++)
            {
                var backgroundColor = i % 2 == 0 ? "white" : "rgba(47, 79, 79, 0.02)";
                html += $@"
                                <tr style=""background-color: {backgroundColor};"">
                                    <td style=""border: 1px solid #ddd; padding: 8px; font-size: 13px; font-weight: bold; color: #2F4F4F;"">{fields[i].Item1}</td>
                                    <td style=""border: 1px solid #ddd; padding: 8px; font-size: 13px;"">{fields[i].Item2 ?? ""}</td>
                                </tr>";
            }

            html += @"
                            </tbody>
                        </table>

                        <div style=""margin-top: 30px; text-align: center; font-size: 12px; color: #666;"">
                            <p>Генерирано на: " + DateTime.Now.ToString("dd.MM.yyyy HH:mm") + @"</p>
                        </div>
                    </div>
                </div>";

            return html;
        }

        private static string FormatDateString(string? dateString)
        {
            if (string.IsNullOrEmpty(dateString))
                return "";

            // Try to parse the date string and format it as dd.MM.yyyy
            if (DateTime.TryParse(dateString, out DateTime date))
            {
                return date.ToString("dd.MM.yyyy");
            }

            // If parsing fails, return the original string
            return dateString;
        }
    }

    public class BrokerskiSlipInputModel
    {
        [Display(Name = "Осигурително брокерско друштво")]
        public string? OsiguritelnoBrokerskoDrustvo { get; set; }

        [Display(Name = "Осигурител")]
        public string? Osiguritel { get; set; }

        [Display(Name = "Класа")]
        public string? Klasa { get; set; }

        [Display(Name = "Производ")]
        public string? Proizvod { get; set; }

        [Display(Name = "Договорувач име/презиме/назив")]
        public string? DogovoruvacImePrezimeNaziv { get; set; }

        [Display(Name = "ЕМБ/МБ")]
        public string? EMBGMB { get; set; }

        [Display(Name = "Осигуреник")]
        public string? Osigurenik { get; set; }

        [Display(Name = "Предмет на осигурување")]
        public string? PredmetNaOsiguruvanje { get; set; }

        [Display(Name = "Осигурително покритие од")]
        public DateTime? OsiguritelnoPokriteOd { get; set; }

        [Display(Name = "Осигурително покритие до")]
        public DateTime? OsiguritelnoPokriteDo { get; set; }

        [Display(Name = "Провизија")]
        public string? Provizija { get; set; }

        [Display(Name = "Премија годишно")]
        public string? PremijaGodishno { get; set; }

        [Display(Name = "Премија вкупно")]
        public string? PremijaVkupno { get; set; }

        [Display(Name = "Забелешка")]
        public string? Zabeleshka { get; set; }
    }

    public class BrokerskiSlipEntry
    {
        public long Id { get; set; }
        public DateTime? DateCreated { get; set; }
        public string? UsernameCreated { get; set; }
        public string? OsiguritelnoBrokerskoDrustvo { get; set; }
        public string? Osiguritel { get; set; }
        public string? Klasa { get; set; }
        public string? Proizvod { get; set; }
        public string? DogovoruvacImePrezimeNaziv { get; set; }
        public string? EMBGMB { get; set; }
        public string? Osigurenik { get; set; }
        public string? PredmetNaOsiguruvanje { get; set; }
        public string? OsiguritelnoPokriteOd { get; set; }
        public string? OsiguritelnoPokriteDo { get; set; }
        public string? Provizija { get; set; }
        public string? PremijaGodishno { get; set; }
        public string? PremijaVkupno { get; set; }
        public string? Zabeleshka { get; set; }
    }
}