using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.ComponentModel.DataAnnotations;
using OfficeOpenXml;
using System.Text;

namespace NextBroker.Pages.Finansii
{
    public class BrokerskiSlipModel : SecurePageModel
    {
        public BrokerskiSlipModel(IConfiguration configuration)
            : base(configuration)
        {
        }

        [BindProperty]
        public BrokerskiSlipInputModel Input { get; set; } = new();

        public IEnumerable<SelectListItem> Osiguriteli { get; set; } = new List<SelectListItem>();
        public IEnumerable<SelectListItem> Klasi { get; set; } = new List<SelectListItem>();
        public IEnumerable<SelectListItem> Produkti { get; set; } = new List<SelectListItem>();
        public List<BrokerskiSlipEntry> Entries { get; set; } = new List<BrokerskiSlipEntry>();

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("BrokerskiSlip"))
            {
                return RedirectToAccessDenied();
            }

            await LoadDropdownData();
            await LoadEntries();
            return Page();
        }

        public async Task<IActionResult> OnGetGenerateDocument(long id)
        {
            if (!await HasPageAccess("BrokerskiSlip"))
            {
                return RedirectToAccessDenied();
            }

            try
            {
                // Get the specific entry by ID
                var entry = await GetBrokerskiSlipEntryById(id);
                if (entry == null)
                {
                    TempData["ErrorMessage"] = "Записот не е пронајден.";
                    return RedirectToPage();
                }

                // Generate Excel document using EPPlus
                var documentBytes = GenerateExcelDocument(entry);

                // Return the document as download
                var fileName = $"BrokerskiSlip_{entry.Id}_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                return File(documentBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Грешка при генерирање на документот: {ex.Message}";
                return RedirectToPage();
            }
        }

        public async Task<IActionResult> OnPost()
        {
            if (!await HasPageAccess("BrokerskiSlip"))
            {
                return RedirectToAccessDenied();
            }

            if (!ModelState.IsValid)
            {
                await LoadDropdownData();
                await LoadEntries();
                return Page();
            }

            string username = HttpContext.Session.GetString("Username");
            if (string.IsNullOrEmpty(username))
            {
                TempData["ErrorMessage"] = "Вашата сесија е истечена. Најавете се повторно.";
                return RedirectToPage("/Account/Login");
            }

            try
            {
                await InsertBrokerskiSlip(username);
                TempData["SuccessMessage"] = "Брокерскиот слип е успешно додаден.";
                return RedirectToPage();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Грешка при додавање: {ex.Message}";
                await LoadDropdownData();
                await LoadEntries();
                return Page();
            }
        }

        private async Task LoadDropdownData()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                // Load Osiguriteli
                using (SqlCommand cmd = new SqlCommand("SELECT Naziv FROM Klienti WHERE Osiguritel = 1", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var osiguriteli = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        string naziv = reader["Naziv"].ToString();
                        osiguriteli.Add(new SelectListItem(naziv, naziv));
                    }
                    Osiguriteli = osiguriteli;
                }

                // Load Klasi
                using (SqlCommand cmd = new SqlCommand("SELECT KlasaIme FROM KlasiOsiguruvanje", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var klasi = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        string klasaIme = reader["KlasaIme"].ToString();
                        klasi.Add(new SelectListItem(klasaIme, klasaIme));
                    }
                    Klasi = klasi;
                }

                // Load Produkti
                using (SqlCommand cmd = new SqlCommand("SELECT Ime FROM Produkti", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var produkti = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        string ime = reader["Ime"].ToString();
                        produkti.Add(new SelectListItem(ime, ime));
                    }
                    Produkti = produkti;
                }
            }
        }

        private async Task LoadEntries()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, DateCreated, UsernameCreated, OsiguritelnoBrokerskoDrustvo,
                           Osiguritel, Klasa, Proizvod, DogovoruvacImePrezimeNaziv,
                           EMBGMB, Osigurenik, PredmetNaOsiguruvanje,
                           OsiguritelnoPokriteOd, OsiguritelnoPokriteDo,
                           Provizija, PremijaGodishno, PremijaVkupno, Zabeleshka
                    FROM BrokerskiSlip
                    ORDER BY DateCreated DESC", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var entries = new List<BrokerskiSlipEntry>();
                    while (await reader.ReadAsync())
                    {
                        entries.Add(new BrokerskiSlipEntry
                        {
                            Id = (long)reader["Id"],
                            DateCreated = reader["DateCreated"] as DateTime?,
                            UsernameCreated = reader["UsernameCreated"] as string,
                            OsiguritelnoBrokerskoDrustvo = reader["OsiguritelnoBrokerskoDrustvo"] as string,
                            Osiguritel = reader["Osiguritel"] as string,
                            Klasa = reader["Klasa"] as string,
                            Proizvod = reader["Proizvod"] as string,
                            DogovoruvacImePrezimeNaziv = reader["DogovoruvacImePrezimeNaziv"] as string,
                            EMBGMB = reader["EMBGMB"] as string,
                            Osigurenik = reader["Osigurenik"] as string,
                            PredmetNaOsiguruvanje = reader["PredmetNaOsiguruvanje"] as string,
                            OsiguritelnoPokriteOd = reader["OsiguritelnoPokriteOd"] as string,
                            OsiguritelnoPokriteDo = reader["OsiguritelnoPokriteDo"] as string,
                            Provizija = reader["Provizija"] as string,
                            PremijaGodishno = reader["PremijaGodishno"] as string,
                            PremijaVkupno = reader["PremijaVkupno"] as string,
                            Zabeleshka = reader["Zabeleshka"] as string
                        });
                    }
                    Entries = entries;
                }
            }
        }

        private async Task InsertBrokerskiSlip(string username)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    INSERT INTO BrokerskiSlip (
                        UsernameCreated, OsiguritelnoBrokerskoDrustvo, Osiguritel, Klasa, Proizvod,
                        DogovoruvacImePrezimeNaziv, EMBGMB, Osigurenik, PredmetNaOsiguruvanje,
                        OsiguritelnoPokriteOd, OsiguritelnoPokriteDo, Provizija,
                        PremijaGodishno, PremijaVkupno, Zabeleshka
                    ) VALUES (
                        @UsernameCreated, @OsiguritelnoBrokerskoDrustvo, @Osiguritel, @Klasa, @Proizvod,
                        @DogovoruvacImePrezimeNaziv, @EMBGMB, @Osigurenik, @PredmetNaOsiguruvanje,
                        @OsiguritelnoPokriteOd, @OsiguritelnoPokriteDo, @Provizija,
                        @PremijaGodishno, @PremijaVkupno, @Zabeleshka
                    )", connection))
                {
                    cmd.Parameters.AddWithValue("@UsernameCreated", username);
                    cmd.Parameters.AddWithValue("@OsiguritelnoBrokerskoDrustvo", (object)Input.OsiguritelnoBrokerskoDrustvo ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@Osiguritel", (object)Input.Osiguritel ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@Klasa", (object)Input.Klasa ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@Proizvod", (object)Input.Proizvod ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@DogovoruvacImePrezimeNaziv", (object)Input.DogovoruvacImePrezimeNaziv ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@EMBGMB", (object)Input.EMBGMB ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@Osigurenik", (object)Input.Osigurenik ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@PredmetNaOsiguruvanje", (object)Input.PredmetNaOsiguruvanje ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguritelnoPokriteOd", (object)Input.OsiguritelnoPokriteOd?.ToString("yyyy-MM-dd") ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguritelnoPokriteDo", (object)Input.OsiguritelnoPokriteDo?.ToString("yyyy-MM-dd") ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@Provizija", (object)Input.Provizija ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaGodishno", (object)Input.PremijaGodishno ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaVkupno", (object)Input.PremijaVkupno ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@Zabeleshka", (object)Input.Zabeleshka ?? DBNull.Value);

                    await cmd.ExecuteNonQueryAsync();
                }
            }
        }

        private async Task<BrokerskiSlipEntry?> GetBrokerskiSlipEntryById(long id)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, DateCreated, UsernameCreated, OsiguritelnoBrokerskoDrustvo,
                           Osiguritel, Klasa, Proizvod, DogovoruvacImePrezimeNaziv,
                           EMBGMB, Osigurenik, PredmetNaOsiguruvanje,
                           OsiguritelnoPokriteOd, OsiguritelnoPokriteDo, Provizija,
                           PremijaGodishno, PremijaVkupno, Zabeleshka
                    FROM BrokerskiSlip
                    WHERE Id = @Id", connection))
                {
                    cmd.Parameters.AddWithValue("@Id", id);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();

                    if (await reader.ReadAsync())
                    {
                        return new BrokerskiSlipEntry
                        {
                            Id = Convert.ToInt64(reader["Id"]),
                            DateCreated = reader["DateCreated"] as DateTime?,
                            UsernameCreated = reader["UsernameCreated"] as string,
                            OsiguritelnoBrokerskoDrustvo = reader["OsiguritelnoBrokerskoDrustvo"] as string,
                            Osiguritel = reader["Osiguritel"] as string,
                            Klasa = reader["Klasa"] as string,
                            Proizvod = reader["Proizvod"] as string,
                            DogovoruvacImePrezimeNaziv = reader["DogovoruvacImePrezimeNaziv"] as string,
                            EMBGMB = reader["EMBGMB"] as string,
                            Osigurenik = reader["Osigurenik"] as string,
                            PredmetNaOsiguruvanje = reader["PredmetNaOsiguruvanje"] as string,
                            OsiguritelnoPokriteOd = reader["OsiguritelnoPokriteOd"] as string,
                            OsiguritelnoPokriteDo = reader["OsiguritelnoPokriteDo"] as string,
                            Provizija = reader["Provizija"] as string,
                            PremijaGodishno = reader["PremijaGodishno"] as string,
                            PremijaVkupno = reader["PremijaVkupno"] as string,
                            Zabeleshka = reader["Zabeleshka"] as string
                        };
                    }
                }
            }
            return null;
        }

        private byte[] GenerateExcelDocument(BrokerskiSlipEntry entry)
        {
            // Note: EPPlus is primarily for Excel, but we can create a simple document format
            // For a proper Word document, you'd typically use DocumentFormat.OpenXml or similar
            // This creates an Excel file that can be opened and converted to Word if needed

            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            using (var package = new ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add("Брокерски Слип");

                // Set up the document header
                worksheet.Cells["A1"].Value = "БРОКЕРСКИ СЛИП";
                worksheet.Cells["A1"].Style.Font.Size = 16;
                worksheet.Cells["A1"].Style.Font.Bold = true;
                worksheet.Cells["A1:B1"].Merge = true;

                worksheet.Cells["A2"].Value = $"Документ број: {entry.Id}";
                worksheet.Cells["A3"].Value = $"Датум на креирање: {entry.DateCreated?.ToString("dd.MM.yyyy HH:mm")}";
                worksheet.Cells["A4"].Value = $"Креирано од: {entry.UsernameCreated}";

                // Add data fields
                int row = 6;
                AddDocumentField(worksheet, ref row, "Осигурително брокерско друштво:", entry.OsiguritelnoBrokerskoDrustvo);
                AddDocumentField(worksheet, ref row, "Осигурител:", entry.Osiguritel);
                AddDocumentField(worksheet, ref row, "Класа:", entry.Klasa);
                AddDocumentField(worksheet, ref row, "Производ:", entry.Proizvod);
                AddDocumentField(worksheet, ref row, "Договорувач име/презиме/назив:", entry.DogovoruvacImePrezimeNaziv);
                AddDocumentField(worksheet, ref row, "ЕМБ/МБ:", entry.EMBGMB);
                AddDocumentField(worksheet, ref row, "Осигуреник:", entry.Osigurenik);
                AddDocumentField(worksheet, ref row, "Предмет на осигурување:", entry.PredmetNaOsiguruvanje);
                AddDocumentField(worksheet, ref row, "Осигурително покритие од:", entry.OsiguritelnoPokriteOd);
                AddDocumentField(worksheet, ref row, "Осигурително покритие до:", entry.OsiguritelnoPokriteDo);
                AddDocumentField(worksheet, ref row, "Провизија:", entry.Provizija);
                AddDocumentField(worksheet, ref row, "Премија годишно:", entry.PremijaGodishno);
                AddDocumentField(worksheet, ref row, "Премија вкупно:", entry.PremijaVkupno);
                AddDocumentField(worksheet, ref row, "Забелешка:", entry.Zabeleshka);

                // Auto-fit columns
                worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

                // Set column widths
                worksheet.Column(1).Width = 30;
                worksheet.Column(2).Width = 40;

                return package.GetAsByteArray();
            }
        }

        private void AddDocumentField(ExcelWorksheet worksheet, ref int row, string label, string? value)
        {
            worksheet.Cells[row, 1].Value = label;
            worksheet.Cells[row, 1].Style.Font.Bold = true;
            worksheet.Cells[row, 2].Value = value ?? "";
            row++;
        }
    }

    public class BrokerskiSlipInputModel
    {
        [Display(Name = "Осигурително брокерско друштво")]
        public string? OsiguritelnoBrokerskoDrustvo { get; set; }

        [Display(Name = "Осигурител")]
        public string? Osiguritel { get; set; }

        [Display(Name = "Класа")]
        public string? Klasa { get; set; }

        [Display(Name = "Производ")]
        public string? Proizvod { get; set; }

        [Display(Name = "Договорувач име/презиме/назив")]
        public string? DogovoruvacImePrezimeNaziv { get; set; }

        [Display(Name = "ЕМБ/МБ")]
        public string? EMBGMB { get; set; }

        [Display(Name = "Осигуреник")]
        public string? Osigurenik { get; set; }

        [Display(Name = "Предмет на осигурување")]
        public string? PredmetNaOsiguruvanje { get; set; }

        [Display(Name = "Осигурително покритие од")]
        public DateTime? OsiguritelnoPokriteOd { get; set; }

        [Display(Name = "Осигурително покритие до")]
        public DateTime? OsiguritelnoPokriteDo { get; set; }

        [Display(Name = "Провизија")]
        public string? Provizija { get; set; }

        [Display(Name = "Премија годишно")]
        public string? PremijaGodishno { get; set; }

        [Display(Name = "Премија вкупно")]
        public string? PremijaVkupno { get; set; }

        [Display(Name = "Забелешка")]
        public string? Zabeleshka { get; set; }
    }

    public class BrokerskiSlipEntry
    {
        public long Id { get; set; }
        public DateTime? DateCreated { get; set; }
        public string? UsernameCreated { get; set; }
        public string? OsiguritelnoBrokerskoDrustvo { get; set; }
        public string? Osiguritel { get; set; }
        public string? Klasa { get; set; }
        public string? Proizvod { get; set; }
        public string? DogovoruvacImePrezimeNaziv { get; set; }
        public string? EMBGMB { get; set; }
        public string? Osigurenik { get; set; }
        public string? PredmetNaOsiguruvanje { get; set; }
        public string? OsiguritelnoPokriteOd { get; set; }
        public string? OsiguritelnoPokriteDo { get; set; }
        public string? Provizija { get; set; }
        public string? PremijaGodishno { get; set; }
        public string? PremijaVkupno { get; set; }
        public string? Zabeleshka { get; set; }
    }
}