using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Configuration;
using System.Data;
using Microsoft.Data.SqlClient;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Collections.Generic;
using RazorPortal.Services;
using OfficeOpenXml;
using System.IO;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Drawing;
using OfficeOpenXml.Style;
using iText.Html2pdf;
using iText.Kernel.Pdf;
using System.Text;

namespace NextBroker.Pages.Pregledi
{
    public class OBD1Row
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public int? BrojDogovori { get; set; }
        public decimal? Premija { get; set; }
        public decimal? RealiziranaProvizija { get; set; }
        public decimal? PresmetanaProvizija { get; set; }
        
        // Store original unrounded values for totals calculation
        public decimal? OriginalPremija { get; set; }
        public decimal? OriginalRealiziranaProvizija { get; set; }
        public decimal? OriginalPresmetanaProvizija { get; set; }
    }

    public class ASOKvartalenOBD1Model : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public ASOKvartalenOBD1Model(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
            InitializeQuarterOptions();
            InitializeYearOptions();
            // Don't initialize TableRows here - let it be null initially
        }

        // Hardcoded class codes and names (in order)
        public static readonly List<(string Code, string Name)> OBD1Classes = new()
        {
            ("01", "Осигурување од незгода"),
            ("02", "Здравствено осигурување"),
            ("03", "Осигурување на патнички возила - КАСКО"),
            ("04", "Осигурување на шински возила-КАСКО"),
            ("05", "Осигурување на воздухоплови-КАСКО"),
            ("06", "Осигурување на пловни објекти-КАСКО"),
            ("07", "Осигурување на стока во превоз"),
            ("08", "Осигурување на имот во пожар и некои други опасности"),
            ("09", "Останати осигурувања на имоти"),
            ("10", "Осигурување од автомобилска одговорност"),
            ("11", "Осигурување од одговорност од употреба на воздухоплови"),
            ("12", "Осигурување од одговорност од употреба на пловни објекти"),
            ("13", "Останати осигурувања од одговорност"),
            ("14", "Осигурување на кредити"),
            ("15", "Осигурување на гаранции"),
            ("16", "Осигурување од финансиски загуби"),
            ("17", "Осигурување на правна заштита"),
            ("18", "Осигурување на туристички услуги"),
            ("19", "Осигурување на живот"),
            ("19.01", "основно"),
            ("19.02", "дополнително"),
            ("19.03", "рентно"),
            ("20", "Осигурување на брак или породување"),
            ("21", "Осигурување на живот во врска со удели во инвест. фондови"),
            ("22", "Осигурување на тонтина"),
            ("23", "Осигурување на средства за капитал"),
        };

        [BindProperty]
        public List<OBD1Row> TableRows { get; set; } = new();
        [BindProperty(SupportsGet = true)]
        public string SelectedQuarter { get; set; }
        [BindProperty(SupportsGet = true)]
        public int SelectedYear { get; set; }
        [BindProperty(SupportsGet = true)]
        public int? SelectedOsiguritelId { get; set; }
        public SelectList QuarterOptions { get; set; }
        public SelectList YearOptions { get; set; }
        public SelectList OsiguritelOptions { get; set; }
        public List<string> DebugColumnNames { get; set; } = new(); // For debugging
        public bool IsSaved { get; set; }
        [BindProperty(SupportsGet = true)]
        public bool IsConfirmed { get; set; }

        public int? TotalBrojDogovori 
        { 
            get 
            {
                var total = TableRows?.Sum(r => r.BrojDogovori ?? 0) ?? 0;
                System.Diagnostics.Debug.WriteLine($"TotalBrojDogovori: {total}");
                return total;
            }
        }
        public decimal? TotalPremija 
        { 
            get 
            {
                // Sum individually rounded values
                var total = TableRows?.Sum(r => r.Premija.HasValue ? RoundHalfUp(r.Premija.Value) : 0) ?? 0;
                return total;
            }
        }
        public decimal? TotalRealiziranaProvizija 
        { 
            get 
            {
                var total = TableRows?.Sum(r => r.RealiziranaProvizija.HasValue ? RoundHalfUp(r.RealiziranaProvizija.Value) : 0) ?? 0;
                return total;
            }
        }
        public decimal? TotalPresmetanaProvizija 
        { 
            get 
            {
                var total = TableRows?.Sum(r => r.PresmetanaProvizija.HasValue ? RoundHalfUp(r.PresmetanaProvizija.Value) : 0) ?? 0;
                return total;
            }
        }

        public void InitializeQuarterOptions()
        {
            var quarters = new List<SelectListItem>
            {
                new SelectListItem { Value = "1", Text = "1-ви квартал (01.01-31.03)" },
                new SelectListItem { Value = "2", Text = "2-ри квартал (01.01-30.06)" },
                new SelectListItem { Value = "3", Text = "3-ти квартал (01.01-30.09)" },
                new SelectListItem { Value = "4", Text = "4-ти квартал (01.01-31.12)" }
            };
            QuarterOptions = new SelectList(quarters, "Value", "Text");
        }

        public void InitializeYearOptions()
        {
            int currentYear = DateTime.Now.Year;
            int startYear = 2025; // Adjust as needed
            var years = new List<SelectListItem>();
            for (int year = currentYear; year >= startYear; year--)
            {
                years.Add(new SelectListItem { Value = year.ToString(), Text = year.ToString() });
            }
            YearOptions = new SelectList(years, "Value", "Text");
            if (SelectedYear == 0)
                SelectedYear = currentYear;
        }

        public async Task InitializeOsiguritelOptionsAsync()
        {
            var osiguriteli = new List<SelectListItem>();
            
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                using (var cmd = new SqlCommand("SELECT Id, Naziv FROM Klienti WHERE Id BETWEEN 56 AND 72 ORDER BY Naziv", connection))
                {
                    using (var reader = await cmd.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            osiguriteli.Add(new SelectListItem
                            {
                                Value = reader["Id"].ToString(),
                                Text = reader["Naziv"].ToString()
                            });
                        }
                    }
                }
            }
            
            OsiguritelOptions = new SelectList(osiguriteli, "Value", "Text");
        }

        private (DateTime StartDate, DateTime EndDate) GetQuarterDateRange(string quarter, int year)
        {
            return quarter switch
            {
                "1" => (new DateTime(year, 1, 1), new DateTime(year, 3, 31)),
                "2" => (new DateTime(year, 1, 1), new DateTime(year, 6, 30)),
                "3" => (new DateTime(year, 1, 1), new DateTime(year, 9, 30)),
                "4" => (new DateTime(year, 1, 1), new DateTime(year, 12, 31)),
                _ => (new DateTime(year, 1, 1), new DateTime(year, 12, 31))
            };
        }

        public override async Task OnPageHandlerExecutionAsync(PageHandlerExecutingContext context, PageHandlerExecutionDelegate next)
        {
            if (!string.IsNullOrEmpty(SelectedQuarter) && SelectedYear != 0)
            {
                var (startDate, endDate) = GetQuarterDateRange(SelectedQuarter, SelectedYear);
                IsSaved = await CheckIfSavedAsync(startDate, endDate);
            }
            
            await next();
        }

        // Helper: Clean up old preview data (older than 24 hours)
        private async Task CleanupOldPreviewDataAsync()
        {
            try
            {
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    using (var cmd = new SqlCommand("DELETE FROM ASOKvartalenIzvestajArhiva WHERE UsernameCreated LIKE '%_PREVIEW' AND DateCreated < DATEADD(hour, -24, GETDATE())", connection))
                    {
                        await cmd.ExecuteNonQueryAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the error but don't throw it to prevent the page from crashing
                Console.WriteLine($"Error cleaning up old preview data: {ex.Message}");
            }
        }

        // Call cleanup on page load
        public async Task OnGetAsync()
        {
            System.Diagnostics.Debug.WriteLine("OnGetAsync: Page loaded");
            System.Diagnostics.Debug.WriteLine($"OnGetAsync: SelectedYear = {SelectedYear}, SelectedQuarter = {SelectedQuarter}");
            
            InitializeQuarterOptions();
            InitializeYearOptions();
            await InitializeOsiguritelOptionsAsync();
            
            System.Diagnostics.Debug.WriteLine($"OnGetAsync: YearOptions count = {YearOptions?.Count() ?? 0}");
            System.Diagnostics.Debug.WriteLine($"OnGetAsync: QuarterOptions count = {QuarterOptions?.Count() ?? 0}");
            System.Diagnostics.Debug.WriteLine($"OnGetAsync: OsiguritelOptions count = {OsiguritelOptions?.Count() ?? 0}");
            
            // Test the stored procedure
            await TestStoredProcedureAsync();
            
            // Clean up old preview data
            await CleanupOldPreviewDataAsync();
            
            System.Diagnostics.Debug.WriteLine("OnGetAsync: Completed");
        }

        public async Task<IActionResult> OnPostAsync()
        {
            System.Diagnostics.Debug.WriteLine("OnPostAsync: Starting");
            
            if (string.IsNullOrEmpty(SelectedQuarter))
            {
                System.Diagnostics.Debug.WriteLine("OnPostAsync: SelectedQuarter is null or empty");
                ModelState.AddModelError("SelectedQuarter", "Мора да изберете квартал.");
                return Page();
            }

            if (SelectedYear <= 0)
            {
                System.Diagnostics.Debug.WriteLine("OnPostAsync: SelectedYear is invalid");
                ModelState.AddModelError("SelectedYear", "Мора да изберете година.");
                return Page();
            }

            int currentYear = DateTime.Now.Year;
            if (SelectedYear > currentYear)
            {
                System.Diagnostics.Debug.WriteLine($"OnPostAsync: SelectedYear {SelectedYear} is greater than current year {currentYear}");
                ModelState.AddModelError("SelectedYear", $"Не можете да изберете година поголема од {currentYear}.");
                return Page();
            }

            var (startDate, endDate) = GetQuarterDateRange(SelectedQuarter, SelectedYear);
            System.Diagnostics.Debug.WriteLine($"OnPostAsync: Quarter={SelectedQuarter}, Year={SelectedYear}, OsiguritelId={SelectedOsiguritelId}, StartDate={startDate}, EndDate={endDate}");

            try
            {
                // Check if there's already confirmed data for this quarter/year
                System.Diagnostics.Debug.WriteLine("OnPostAsync: Checking if confirmed");
            bool isConfirmed = await CheckIfConfirmedAsync(startDate, endDate);
                System.Diagnostics.Debug.WriteLine($"OnPostAsync: IsConfirmed = {isConfirmed}");

            if (isConfirmed)
            {
                    System.Diagnostics.Debug.WriteLine("OnPostAsync: Loading confirmed data");
                    // Load confirmed data from archive
                    TableRows = await LoadExportRowsAsync(startDate, endDate);
                    IsConfirmed = true;
                    System.Diagnostics.Debug.WriteLine($"OnPostAsync: Loaded {TableRows?.Count ?? 0} confirmed rows");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("OnPostAsync: Loading data directly from stored procedure");
                    // Load data directly from stored procedure for preview
                    TableRows = await LoadDataFromStoredProcedureAsync(startDate, endDate, SelectedOsiguritelId);
                    System.Diagnostics.Debug.WriteLine($"OnPostAsync: Loaded {TableRows?.Count ?? 0} rows directly from stored procedure");
                    
                    // Check if we have any data
                    if (TableRows != null && TableRows.Count > 0)
                    {
                        var hasData = TableRows.Any(r => r.BrojDogovori.HasValue || r.Premija.HasValue || r.PresmetanaProvizija.HasValue || r.RealiziranaProvizija.HasValue);
                        System.Diagnostics.Debug.WriteLine($"OnPostAsync: Has data = {hasData}");
                        
                        if (hasData)
                        {
                            // Save to archive for preview
                            System.Diagnostics.Debug.WriteLine("OnPostAsync: Saving to archive for preview");
                            await SaveToArchiveAsync(startDate, endDate, true);
                            System.Diagnostics.Debug.WriteLine("OnPostAsync: Saved to archive");
                        }
                        else
                        {
                            // Initialize empty rows if no data was returned
                            TableRows = OBD1Classes.Select(c => new OBD1Row { Code = c.Code, Name = c.Name }).ToList();
                            System.Diagnostics.Debug.WriteLine("OnPostAsync: Using empty rows (no data found)");
                        }
                    }
                    else
                    {
                        // Initialize empty rows if no data was returned
                        TableRows = OBD1Classes.Select(c => new OBD1Row { Code = c.Code, Name = c.Name }).ToList();
                        System.Diagnostics.Debug.WriteLine("OnPostAsync: Using empty rows (no data returned)");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"OnPostAsync: Exception occurred: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"OnPostAsync: Stack trace: {ex.StackTrace}");
                ModelState.AddModelError("", $"Грешка при вчитување на податоци: {ex.Message}");
                
                // Initialize empty rows on error
                TableRows = OBD1Classes.Select(c => new OBD1Row { Code = c.Code, Name = c.Name }).ToList();
            }

            System.Diagnostics.Debug.WriteLine("OnPostAsync: Returning page");
            
            // Process form data to round decimal values to whole numbers
            ProcessFormData();
            
            // Test rounding logic
            TestRoundingLogic();
            
            return Page();
        }

        public async Task<IActionResult> OnPostConfirmAsync()
        {
            System.Diagnostics.Debug.WriteLine("OnPostConfirmAsync: Starting");
            
            if (string.IsNullOrEmpty(SelectedQuarter))
            {
                ModelState.AddModelError("SelectedQuarter", "Мора да изберете квартал.");
                return Page();
            }

            if (SelectedYear <= 0)
            {
                ModelState.AddModelError("SelectedYear", "Мора да изберете година.");
                return Page();
            }

            int currentYear = DateTime.Now.Year;
            if (SelectedYear > currentYear)
            {
                ModelState.AddModelError("SelectedYear", $"Не можете да изберете година поголема од {currentYear}.");
                return Page();
            }

            var (startDate, endDate) = GetQuarterDateRange(SelectedQuarter, SelectedYear);
            System.Diagnostics.Debug.WriteLine($"OnPostConfirmAsync: Quarter={SelectedQuarter}, Year={SelectedYear}, StartDate={startDate}, EndDate={endDate}");
            
            try
            {
                // Check if we have TableRows with user data
                if (TableRows == null || TableRows.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("OnPostConfirmAsync: No TableRows data available");
                    ModelState.AddModelError("", "Нема податоци за потврдување.");
                    return Page();
                }

                System.Diagnostics.Debug.WriteLine($"OnPostConfirmAsync: TableRows count = {TableRows.Count}");
                
                // Log the current data to see what the user entered
                for (int i = 0; i < TableRows.Count; i++)
                {
                    var row = TableRows[i];
                    System.Diagnostics.Debug.WriteLine($"Row {i} ({row.Code}): BrojDogovori={row.BrojDogovori}, Premija={row.Premija}, PresmetanaProvizija={row.PresmetanaProvizija}, RealiziranaProvizija={row.RealiziranaProvizija}");
                }

                // Process form data to round decimal values to whole numbers
                ProcessFormData();

                // Clean up any preview data first
                System.Diagnostics.Debug.WriteLine("OnPostConfirmAsync: Cleaning up preview data");
                await CleanupPreviewDataAsync(startDate, endDate);
                
                // Delete any existing confirmed data for this quarter/year
                System.Diagnostics.Debug.WriteLine("OnPostConfirmAsync: Deleting existing confirmed data");
                await DeleteAllRowsForQuarterAsync(startDate, endDate);
                
                // Save the current TableRows data as confirmed data
                System.Diagnostics.Debug.WriteLine("OnPostConfirmAsync: Saving current data as confirmed");
                var username = GetFinalMarker();
                System.Diagnostics.Debug.WriteLine($"OnPostConfirmAsync: Using username: {username}");
                await SaveToArchiveAsync(startDate, endDate, false); // false = not preview, so confirmed
                
                // Set the confirmed flag
                IsConfirmed = true;
                
                System.Diagnostics.Debug.WriteLine("OnPostConfirmAsync: Data confirmed successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"OnPostConfirmAsync: Exception occurred: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"OnPostConfirmAsync: Stack trace: {ex.StackTrace}");
                ModelState.AddModelError("", $"Грешка при потврдување на податоци: {ex.Message}");
            }
            
            return Page();
        }

        public async Task<IActionResult> OnPostCancelPreviewAsync()
        {
            if (string.IsNullOrEmpty(SelectedQuarter))
            {
                return RedirectToPage();
            }

            var (startDate, endDate) = GetQuarterDateRange(SelectedQuarter, SelectedYear);
            
            // Clean up preview data
            await CleanupPreviewDataAsync(startDate, endDate);
            
            // Reset the page
            TableRows = OBD1Classes.Select(c => new OBD1Row { Code = c.Code, Name = c.Name }).ToList();
            IsConfirmed = false;
            
            return Page();
        }

        public async Task<IActionResult> OnPostExportExcelAsync()
        {
            Console.WriteLine("=== EXCEL EXPORT DEBUG START ===");
            System.Diagnostics.Debug.WriteLine("OnPostExportExcelAsync: Starting Excel export");
            var (startDate, endDate) = GetQuarterDateRange(SelectedQuarter, SelectedYear);
            Console.WriteLine($"Export parameters: Quarter={SelectedQuarter}, Year={SelectedYear}, StartDate={startDate}, EndDate={endDate}");
            
            // Use TableRows for export (same as page totals)
            var exportRows = TableRows?.ToList() ?? new List<OBD1Row>();
            Console.WriteLine($"Using TableRows for export: {exportRows.Count} rows");
            
            // Calculate totals from original unrounded values (same as page totals)
            var exportTotalPremija = exportRows.Sum(x => x.OriginalPremija ?? x.Premija ?? 0);
            var exportTotalRealizirana = exportRows.Sum(x => x.OriginalRealiziranaProvizija ?? x.RealiziranaProvizija ?? 0);
            var exportTotalPresmetana = exportRows.Sum(x => x.OriginalPresmetanaProvizija ?? x.PresmetanaProvizija ?? 0);
            
            // Round the totals to 0 decimal places for display
            exportTotalPremija = Math.Round(exportTotalPremija, 0, MidpointRounding.AwayFromZero);
            exportTotalRealizirana = Math.Round(exportTotalRealizirana, 0, MidpointRounding.AwayFromZero);
            exportTotalPresmetana = Math.Round(exportTotalPresmetana, 0, MidpointRounding.AwayFromZero);
            
            Console.WriteLine($"Excel Export - Calculated from original values and rounded: Premija={exportTotalPremija}, Realizirana={exportTotalRealizirana}, Presmetana={exportTotalPresmetana}");
            
            System.Diagnostics.Debug.WriteLine("OnPostExportExcelAsync: Creating Excel file");
            
            using (var package = new ExcelPackage())
            {
                var ws = package.Workbook.Worksheets.Add("OBD1 Report");
                
                // 1. Header row: label (not bold) and company (bold)
                ws.Cells[1, 1].Value = "Осигурително брокерско друштво:";
                ws.Cells[1, 1].Style.Font.Bold = false;
                ws.Cells[1, 2, 1, 6].Merge = true;
                ws.Cells[1, 2].Value = "ОБД ИНКО АД СКОПЈЕ";
                ws.Cells[1, 2].Style.Font.Bold = true;
                ws.Cells[1, 2].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;

                // 2. Year and quarter rows
                ws.Cells[2, 1].Value = "Година:";
                ws.Cells[2, 1].Style.Font.Bold = true;
                ws.Cells[2, 2, 2, 3].Merge = true;
                ws.Cells[2, 2].Value = SelectedYear.ToString();
                ws.Cells[3, 1].Value = "Период:";
                ws.Cells[3, 1].Style.Font.Bold = true;
                ws.Cells[3, 2, 3, 3].Merge = true;
                ws.Cells[3, 2].Value = GetQuarterDisplayName(SelectedQuarter);
                ws.Cells[4, 1].Value = "Друштво за осигурување:";
                ws.Cells[4, 1].Style.Font.Bold = true;

                // 3. Title row: 'Образец: obd1' as bold, centered, large
                ws.Cells[6, 1, 6, 6].Merge = true;
                ws.Cells[6, 1].Value = "Образец: obd1";
                ws.Cells[6, 1].Style.Font.Bold = true;
                ws.Cells[6, 1].Style.Font.Size = 14;
                ws.Cells[6, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

                // 4. Double table header
                int tableHeaderRow1 = 7;
                int tableHeaderRow2 = 8;
                
                // First header row: merge and shade first two columns, set main column titles
                ws.Cells[tableHeaderRow1, 1, tableHeaderRow1, 2].Merge = true;
                ws.Cells[tableHeaderRow1, 1].Value = "";
                ws.Cells[tableHeaderRow1, 1, tableHeaderRow1, 2].Style.Fill.PatternType = ExcelFillStyle.Solid;
                ws.Cells[tableHeaderRow1, 1, tableHeaderRow1, 2].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                
                // Apply thin borders to first two columns
                for (int col = 1; col <= 2; col++)
                {
                    ws.Cells[tableHeaderRow1, col].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    ws.Cells[tableHeaderRow1, col].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    ws.Cells[tableHeaderRow1, col].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    ws.Cells[tableHeaderRow1, col].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                }
                
                ws.Cells[tableHeaderRow1, 3].Value = "Број на договори";
                ws.Cells[tableHeaderRow1, 4].Value = "Премија";
                ws.Cells[tableHeaderRow1, 5].Value = "Реализирана провизија";
                ws.Cells[tableHeaderRow1, 6].Value = "Пресметана провизија";
                
                for (int col = 3; col <= 6; col++)
                {
                    ws.Cells[tableHeaderRow1, col].Style.Font.Bold = true;
                    ws.Cells[tableHeaderRow1, col].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    ws.Cells[tableHeaderRow1, col].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    ws.Cells[tableHeaderRow1, col].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                    
                    // Apply thin borders to data columns
                    ws.Cells[tableHeaderRow1, col].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    ws.Cells[tableHeaderRow1, col].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    ws.Cells[tableHeaderRow1, col].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    ws.Cells[tableHeaderRow1, col].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                }
                
                // Second header row: column names and numbers
                ws.Cells[tableHeaderRow2, 1].Value = "Класа";
                ws.Cells[tableHeaderRow2, 2].Value = "Вид на осигурување";
                ws.Cells[tableHeaderRow2, 3].Value = "100";
                ws.Cells[tableHeaderRow2, 4].Value = "200";
                ws.Cells[tableHeaderRow2, 5].Value = "300";
                ws.Cells[tableHeaderRow2, 6].Value = "400";
                
                for (int col = 1; col <= 6; col++)
                {
                    ws.Cells[tableHeaderRow2, col].Style.Font.Bold = true;
                    ws.Cells[tableHeaderRow2, col].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    
                    // Apply thin borders to all columns
                    ws.Cells[tableHeaderRow2, col].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    ws.Cells[tableHeaderRow2, col].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    ws.Cells[tableHeaderRow2, col].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    ws.Cells[tableHeaderRow2, col].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                }

                // 5. Data rows with thin borders
                int dataStartRow = tableHeaderRow2 + 1;
                int dataEndRow = dataStartRow + exportRows.Count - 1;
                for (int i = 0; i < exportRows.Count; i++)
                {
                    var r = exportRows[i];
                    ws.Cells[dataStartRow + i, 1].Value = r.Code;
                    ws.Cells[dataStartRow + i, 2].Value = r.Name;
                    ws.Cells[dataStartRow + i, 3].Value = r.BrojDogovori;
                    ws.Cells[dataStartRow + i, 4].Value = r.Premija?.ToString("F0");
                    ws.Cells[dataStartRow + i, 5].Value = r.RealiziranaProvizija?.ToString("F0");
                    ws.Cells[dataStartRow + i, 6].Value = r.PresmetanaProvizija?.ToString("F0");
                    
                    for (int col = 1; col <= 6; col++)
                    {
                        ws.Cells[dataStartRow + i, col].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        
                        // Apply thin borders to all data cells
                        ws.Cells[dataStartRow + i, col].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        ws.Cells[dataStartRow + i, col].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        ws.Cells[dataStartRow + i, col].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                        ws.Cells[dataStartRow + i, col].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                    }
                }

                // 6. Totals row with thin borders
                int totalRow = dataEndRow + 1;
                ws.Cells[totalRow, 1].Value = "ВКУПНО";
                ws.Cells[totalRow, 1, totalRow, 2].Merge = true;
                ws.Cells[totalRow, 1, totalRow, 2].Style.Font.Bold = true;
                ws.Cells[totalRow, 3].Value = exportRows.Sum(x => x.BrojDogovori ?? 0);
                
                // Use calculated totals from export data
                Console.WriteLine($"Excel Export - Using calculated totals: Premija={exportTotalPremija}, Realizirana={exportTotalRealizirana}, Presmetana={exportTotalPresmetana}");
                
                ws.Cells[totalRow, 4].Value = exportTotalPremija.ToString("F0");
                ws.Cells[totalRow, 5].Value = exportTotalRealizirana.ToString("F0");
                ws.Cells[totalRow, 6].Value = exportTotalPresmetana.ToString("F0");
                
                Console.WriteLine($"Excel Export - Written to cells: Premija={ws.Cells[totalRow, 4].Value}, Realizirana={ws.Cells[totalRow, 5].Value}, Presmetana={ws.Cells[totalRow, 6].Value}");
                
                // Apply thin borders to totals row
                for (int col = 1; col <= 6; col++)
                {
                    ws.Cells[totalRow, col].Style.Font.Bold = true;
                    ws.Cells[totalRow, col].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    ws.Cells[totalRow, col].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    ws.Cells[totalRow, col].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    ws.Cells[totalRow, col].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    ws.Cells[totalRow, col].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                }

                // 7. Apply thick outer border to the entire table
                int tableStartRow = tableHeaderRow1;
                int tableEndRow = totalRow;
                int tableStartCol = 1;
                int tableEndCol = 6;
                
                // Top border (thick)
                using (var topRange = ws.Cells[tableStartRow, tableStartCol, tableStartRow, tableEndCol])
                {
                    topRange.Style.Border.Top.Style = ExcelBorderStyle.Thick;
                    topRange.Style.Border.Top.Color.SetColor(System.Drawing.Color.Black);
                }
                
                // Bottom border (thick)
                using (var bottomRange = ws.Cells[tableEndRow, tableStartCol, tableEndRow, tableEndCol])
                {
                    bottomRange.Style.Border.Bottom.Style = ExcelBorderStyle.Thick;
                    bottomRange.Style.Border.Bottom.Color.SetColor(System.Drawing.Color.Black);
                }
                
                // Left border (thick)
                using (var leftRange = ws.Cells[tableStartRow, tableStartCol, tableEndRow, tableStartCol])
                {
                    leftRange.Style.Border.Left.Style = ExcelBorderStyle.Thick;
                    leftRange.Style.Border.Left.Color.SetColor(System.Drawing.Color.Black);
                }
                
                // Right border (thick)
                using (var rightRange = ws.Cells[tableStartRow, tableEndCol, tableEndRow, tableEndCol])
                {
                    rightRange.Style.Border.Right.Style = ExcelBorderStyle.Thick;
                    rightRange.Style.Border.Right.Color.SetColor(System.Drawing.Color.Black);
                }

                // 8. Set column widths
                ws.Column(1).Width = 8;   // Класа
                ws.Column(2).Width = 38;  // Вид на осигурување
                ws.Column(3).Width = 18;  // Број на договори
                ws.Column(4).Width = 18;  // Премија
                ws.Column(5).Width = 22;  // Реализирана провизија
                ws.Column(6).Width = 22;  // Пресметана провизија

                var stream = new MemoryStream();
                package.SaveAs(stream);
                stream.Position = 0;
                string fileName = $"OBD1_{SelectedYear}_Q{SelectedQuarter}.xlsx";
                Console.WriteLine($"Excel file created: {fileName}");
                return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
        }

        // Place this at the class level, outside of any other method
        private string GetQuarterDisplayName(string quarter)
        {
            return quarter switch
            {
                "1" => "1-ви квартал (01.01-31.03)",
                "2" => "2-ри квартал (01.01-30.06)",
                "3" => "3-ти квартал (01.01-30.09)",
                "4" => "4-ти квартал (01.01-31.12)",
                _ => "(непознат период)"
            };
        }

        private async Task CleanupTempDataAsync(string sessionId)
        {
            if (!string.IsNullOrEmpty(sessionId))
            {
                try
                {
                    using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                    {
                        await connection.OpenAsync();
                        
                        // Check if the temporary table exists before trying to delete from it
                        using (var cmd = new SqlCommand(@"
                            IF OBJECT_ID('tempdb..#TempOBD1Data') IS NOT NULL
                            BEGIN
                                DELETE FROM #TempOBD1Data WHERE SessionId = @SessionId
                            END", connection))
                        {
                            cmd.Parameters.AddWithValue("@SessionId", sessionId);
                            await cmd.ExecuteNonQueryAsync();
                        }
                    }
                }
                catch (Exception ex)
                {
                    // Log the error but don't throw it to prevent the page from crashing
                    Console.WriteLine($"Error cleaning up temp data for session {sessionId}: {ex.Message}");
                }
            }
        }

        public async Task<IActionResult> OnPostExportPdfAsync()
        {
            Console.WriteLine("=== PDF EXPORT DEBUG START ===");
            System.Diagnostics.Debug.WriteLine("OnPostExportPdfAsync: Starting PDF export");
            var (startDate, endDate) = GetQuarterDateRange(SelectedQuarter, SelectedYear);
            Console.WriteLine($"Export parameters: Quarter={SelectedQuarter}, Year={SelectedYear}, StartDate={startDate}, EndDate={endDate}");
            
            // Use TableRows for export (same as page totals)
            var exportRows = TableRows?.ToList() ?? new List<OBD1Row>();
            Console.WriteLine($"Using TableRows for export: {exportRows.Count} rows");
            
            // Calculate totals from original unrounded values (same as page totals)
            var exportTotalPremija = exportRows.Sum(x => x.OriginalPremija ?? x.Premija ?? 0);
            var exportTotalRealizirana = exportRows.Sum(x => x.OriginalRealiziranaProvizija ?? x.RealiziranaProvizija ?? 0);
            var exportTotalPresmetana = exportRows.Sum(x => x.OriginalPresmetanaProvizija ?? x.PresmetanaProvizija ?? 0);
            
            // Round the totals using the same logic as the page
            exportTotalPremija = Math.Round(exportTotalPremija, 0, MidpointRounding.AwayFromZero);
            exportTotalRealizirana = Math.Round(exportTotalRealizirana, 0, MidpointRounding.AwayFromZero);
            exportTotalPresmetana = Math.Round(exportTotalPresmetana, 0, MidpointRounding.AwayFromZero);
            
            Console.WriteLine($"PDF Export - Calculated from original values and rounded: Premija={exportTotalPremija}, Realizirana={exportTotalRealizirana}, Presmetana={exportTotalPresmetana}");
            
            // Build HTML content
            var sb = new StringBuilder();
            sb.AppendLine("<!DOCTYPE html>");
            sb.AppendLine("<html>");
            sb.AppendLine("<head>");
            sb.AppendLine("<meta charset='utf-8'>");
            sb.AppendLine("<style>");
            sb.AppendLine("body { font-family: Arial, sans-serif; margin: 15px; font-size: 10px; }");
            sb.AppendLine(".header { margin-bottom: 15px; }");
            sb.AppendLine(".company-info { margin-bottom: 8px; }");
            sb.AppendLine(".company-name { font-weight: bold; font-size: 12px; }");
            sb.AppendLine(".title { text-align: center; font-weight: bold; font-size: 14px; margin: 15px 0; }");
            sb.AppendLine("table { width: 100%; border-collapse: collapse; margin-top: 15px; }");
            sb.AppendLine("th, td { border: 1px solid #000; padding: 4px; text-align: center; font-size: 9px; }");
            sb.AppendLine("th { background-color: #f0f0f0; font-weight: bold; }");
            sb.AppendLine(".totals-row { font-weight: bold; background-color: #f0f0f0; }");
            sb.AppendLine(".merged-cell { text-align: center; }");
            sb.AppendLine("</style>");
            sb.AppendLine("</head>");
            sb.AppendLine("<body>");
            
            // Header section
            sb.AppendLine("<div class='header'>");
            sb.AppendLine("<div class='company-info'>");
            sb.AppendLine("<span>Осигурително брокерско друштво: <span class='company-name'>ОБД ИНКО АД СКОПЈЕ</span></span>");
            sb.AppendLine("</div>");
            sb.AppendLine("<div class='company-info'>");
            sb.AppendLine($"<span>Година: {SelectedYear}</span>");
            sb.AppendLine("</div>");
            sb.AppendLine("<div class='company-info'>");
            sb.AppendLine($"<span>Период: {GetQuarterDisplayName(SelectedQuarter)}</span>");
            sb.AppendLine("</div>");
            sb.AppendLine("<div class='company-info'>");
            sb.AppendLine("<span>Друштво за осигурување:</span>");
            sb.AppendLine("</div>");
            sb.AppendLine("</div>");
            
            // Title
            sb.AppendLine("<div class='title'>Образец: obd1</div>");
            
            // Table
            sb.AppendLine("<table>");
            
            // First header row
            sb.AppendLine("<tr>");
            sb.AppendLine("<th colspan='2' style='background-color: #f0f0f0;'></th>");
            sb.AppendLine("<th>Број на договори</th>");
            sb.AppendLine("<th>Премија</th>");
            sb.AppendLine("<th>Реализирана провизија</th>");
            sb.AppendLine("<th>Пресметана провизија</th>");
            sb.AppendLine("</tr>");
            
            // Second header row
            sb.AppendLine("<tr>");
            sb.AppendLine("<th>Вид на осигурување</th>");
            sb.AppendLine("<th>Класа</th>");
            sb.AppendLine("<th>100</th>");
            sb.AppendLine("<th>200</th>");
            sb.AppendLine("<th>300</th>");
            sb.AppendLine("<th>400</th>");
            sb.AppendLine("</tr>");
            
            // Data rows
            foreach (var row in exportRows)
            {
                sb.AppendLine("<tr>");
                sb.AppendLine($"<td>{row.Name}</td>");
                sb.AppendLine($"<td>{row.Code}</td>");
                sb.AppendLine($"<td>{row.BrojDogovori?.ToString() ?? ""}</td>");
                sb.AppendLine($"<td>{row.Premija?.ToString("F0") ?? ""}</td>");
                sb.AppendLine($"<td>{row.RealiziranaProvizija?.ToString("F0") ?? ""}</td>");
                sb.AppendLine($"<td>{row.PresmetanaProvizija?.ToString("F0") ?? ""}</td>");
                sb.AppendLine("</tr>");
            }
            
            // Totals row
            sb.AppendLine("<tr class='totals-row'>");
            sb.AppendLine("<td colspan='2'>ВКУПНО</td>");
            sb.AppendLine($"<td>{exportRows.Sum(x => x.BrojDogovori ?? 0)}</td>");
            sb.AppendLine($"<td>{exportTotalPremija:F0}</td>");
            sb.AppendLine($"<td>{exportTotalRealizirana:F0}</td>");
            sb.AppendLine($"<td>{exportTotalPresmetana:F0}</td>");
            sb.AppendLine("</tr>");
            
            sb.AppendLine("</table>");
            sb.AppendLine("</body>");
            sb.AppendLine("</html>");
            
            // Convert HTML to PDF using iText7
            using (var stream = new MemoryStream())
            {
                using (var writer = new PdfWriter(stream))
                {
                    HtmlConverter.ConvertToPdf(sb.ToString(), writer);
                }
                
                string fileName = $"OBD1_{SelectedYear}_Q{SelectedQuarter}.pdf";
                return File(stream.ToArray(), "application/pdf", fileName);
            }
        }

        // Helper: Get preview marker for current user
        private string GetPreviewMarker() => (HttpContext.Session.GetString("Username") ?? "System") + "_PREVIEW";

        // Helper: Get final marker for current user
        private string GetFinalMarker() => HttpContext.Session.GetString("Username") ?? "System";

        // Helper: Delete preview rows for this user/quarter/year
        private async Task DeletePreviewRowsAsync(DateTime startDate, DateTime endDate)
        {
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                using (var cmd = new SqlCommand("DELETE FROM ASOKvartalenIzvestajArhiva WHERE StartDate = @StartDate AND EndDate = @EndDate AND UsernameCreated LIKE @PreviewMarker", connection))
                {
                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    cmd.Parameters.AddWithValue("@PreviewMarker", GetPreviewMarker() + "%");
                    await cmd.ExecuteNonQueryAsync();
                }
            }
        }

        // Helper: Check if confirmed data exists for this quarter/year
        private async Task<bool> CheckIfConfirmedAsync(DateTime startDate, DateTime endDate)
        {
            System.Diagnostics.Debug.WriteLine($"CheckIfConfirmedAsync: Checking for confirmed data between {startDate} and {endDate}");
            try
        {
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                    using (var cmd = new SqlCommand("SELECT COUNT(*) FROM ASOKvartalenIzvestajArhiva WHERE StartDate = @StartDate AND EndDate = @EndDate AND UsernameCreated NOT LIKE '%_PREVIEW'", connection))
                {
                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                        
                        var count = await cmd.ExecuteScalarAsync();
                        var result = Convert.ToInt32(count) > 0;
                        System.Diagnostics.Debug.WriteLine($"CheckIfConfirmedAsync: Found {count} confirmed records, returning {result}");
                        return result;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CheckIfConfirmedAsync: Exception occurred: {ex.Message}");
                return false;
            }
        }

        private async Task<List<OBD1Row>> LoadExportRowsAsync(DateTime startDate, DateTime endDate)
        {
            System.Diagnostics.Debug.WriteLine($"LoadExportRowsAsync: Loading export rows for {startDate} to {endDate}");
            try
            {
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                    using (var cmd = new SqlCommand("SELECT TOP 1 * FROM ASOKvartalenIzvestajArhiva WHERE StartDate = @StartDate AND EndDate = @EndDate ORDER BY DateCreated DESC", connection))
                {
                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                        
                        System.Diagnostics.Debug.WriteLine("LoadExportRowsAsync: Executing query");
                    using (var reader = await cmd.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                                System.Diagnostics.Debug.WriteLine("LoadExportRowsAsync: Found data, mapping to rows");
                                var rows = MapReaderToRows(reader);
                                System.Diagnostics.Debug.WriteLine($"LoadExportRowsAsync: Mapped {rows.Count} rows");
                            return rows;
                        }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine("LoadExportRowsAsync: No data found, returning empty rows");
                                return OBD1Classes.Select(c => new OBD1Row { Code = c.Code, Name = c.Name }).ToList();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"LoadExportRowsAsync: Exception occurred: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"LoadExportRowsAsync: Stack trace: {ex.StackTrace}");
                return OBD1Classes.Select(c => new OBD1Row { Code = c.Code, Name = c.Name }).ToList();
            }
        }

        // Helper: Map a SqlDataReader row to OBD1Rows
        private List<OBD1Row> MapReaderToRows(SqlDataReader reader)
        {
            var rows = OBD1Classes.Select(c => new OBD1Row { Code = c.Code, Name = c.Name }).ToList();
            int i = 0;
            rows[i++].BrojDogovori = reader["OsiguruvanjeOdNezgoda"] as int?;
            rows[i++].BrojDogovori = reader["ZdravstvenoOsiguruvanje"] as int?;
            rows[i++].BrojDogovori = reader["OsiguruvanjeNaPatnichkiVozilaKasko"] as int?;
            rows[i++].BrojDogovori = reader["OsiguruvanjeNaShinskiVozilaKasko"] as int?;
            rows[i++].BrojDogovori = reader["OsiguruvanjeNaVozduhoploviKasko"] as int?;
            rows[i++].BrojDogovori = reader["OsiguruvanjeNaPlovniObjektiKasko"] as int?;
            rows[i++].BrojDogovori = reader["OsiguruvanjeNaStokaVoPrevoz"] as int?;
            rows[i++].BrojDogovori = reader["OsiguruvanjeNaImotVoPozhar"] as int?;
            rows[i++].BrojDogovori = reader["OstanatiOsiguruvanjaNaImoti"] as int?;
            rows[i++].BrojDogovori = reader["OsiguruvanjeOdAvtomobilskaOdgovornost"] as int?;
            rows[i++].BrojDogovori = reader["OsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi"] as int?;
            rows[i++].BrojDogovori = reader["OsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti"] as int?;
            rows[i++].BrojDogovori = reader["OstanatiOsiguruvanjaOdOdgovornost"] as int?;
            rows[i++].BrojDogovori = reader["OsiguruvanjeNaKrediti"] as int?;
            rows[i++].BrojDogovori = reader["OsiguruvanjeNaGarantii"] as int?;
            rows[i++].BrojDogovori = reader["OsiguruvanjeOdFinansiskiZagubi"] as int?;
            rows[i++].BrojDogovori = reader["OsiguruvanjeNaPravnaZashtita"] as int?;
            rows[i++].BrojDogovori = reader["OsiguruvanjeNaTuristichkiUslugi"] as int?;
            rows[i++].BrojDogovori = reader["OsiguruvanjeNaZhivot"] as int?;
            rows[i++].BrojDogovori = reader["Osnovno"] as int?;
            rows[i++].BrojDogovori = reader["Dopolnitelno"] as int?;
            rows[i++].BrojDogovori = reader["Rentno"] as int?;
            rows[i++].BrojDogovori = reader["OsiguruvanjeNaBrakIliPoroduvanje"] as int?;
            rows[i++].BrojDogovori = reader["OsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi"] as int?;
            rows[i++].BrojDogovori = reader["OsiguruvanjeNaTontina"] as int?;
            rows[i++].BrojDogovori = reader["OsiguruvanjeNaSredstvaZaKapital"] as int?;
            i = 0;
            rows[i++].Premija = reader["PremijaOsiguruvanjeOdNezgoda"] as decimal?;
            rows[i++].Premija = reader["PremijaZdravstvenoOsiguruvanje"] as decimal?;
            rows[i++].Premija = reader["PremijaOsiguruvanjeNaPatnichkiVozilaKasko"] as decimal?;
            rows[i++].Premija = reader["PremijaOsiguruvanjeNaShinskiVozilaKasko"] as decimal?;
            rows[i++].Premija = reader["PremijaOsiguruvanjeNaVozduhoploviKasko"] as decimal?;
            rows[i++].Premija = reader["PremijaOsiguruvanjeNaPlovniObjektiKasko"] as decimal?;
            rows[i++].Premija = reader["PremijaOsiguruvanjeNaStokaVoPrevoz"] as decimal?;
            rows[i++].Premija = reader["PremijaOsiguruvanjeNaImotVoPozhar"] as decimal?;
            rows[i++].Premija = reader["PremijaOstanatiOsiguruvanjaNaImoti"] as decimal?;
            rows[i++].Premija = reader["PremijaOsiguruvanjeOdAvtomobilskaOdgovornost"] as decimal?;
            rows[i++].Premija = reader["PremijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi"] as decimal?;
            rows[i++].Premija = reader["PremijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti"] as decimal?;
            rows[i++].Premija = reader["PremijaOstanatiOsiguruvanjaOdOdgovornost"] as decimal?;
            rows[i++].Premija = reader["PremijaOsiguruvanjeNaKrediti"] as decimal?;
            rows[i++].Premija = reader["PremijaOsiguruvanjeNaGarantii"] as decimal?;
            rows[i++].Premija = reader["PremijaOsiguruvanjeOdFinansiskiZagubi"] as decimal?;
            rows[i++].Premija = reader["PremijaOsiguruvanjeNaPravnaZashtita"] as decimal?;
            rows[i++].Premija = reader["PremijaOsiguruvanjeNaTuristichkiUslugi"] as decimal?;
            rows[i++].Premija = reader["PremijaOsiguruvanjeNaZhivot"] as decimal?;
            rows[i++].Premija = reader["PremijaOsnovno"] as decimal?;
            rows[i++].Premija = reader["PremijaDopolnitelno"] as decimal?;
            rows[i++].Premija = reader["PremijaRentno"] as decimal?;
            rows[i++].Premija = reader["PremijaOsiguruvanjeNaBrakIliPoroduvanje"] as decimal?;
            rows[i++].Premija = reader["PremijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi"] as decimal?;
            rows[i++].Premija = reader["PremijaOsiguruvanjeNaTontina"] as decimal?;
            rows[i++].Premija = reader["PremijaOsiguruvanjeNaSredstvaZaKapital"] as decimal?;
            i = 0;
            rows[i++].PresmetanaProvizija = reader["PresmetanaProvizijaOsiguruvanjeOdNezgoda"] as decimal?;
            rows[i++].PresmetanaProvizija = reader["PresmetanaProvizijaZdravstvenoOsiguruvanje"] as decimal?;
            rows[i++].PresmetanaProvizija = reader["PresmetanaProvizijaOsiguruvanjeNaPatnichkiVozilaKasko"] as decimal?;
            rows[i++].PresmetanaProvizija = reader["PresmetanaProvizijaOsiguruvanjeNaShinskiVozilaKasko"] as decimal?;
            rows[i++].PresmetanaProvizija = reader["PresmetanaProvizijaOsiguruvanjeNaVozduhoploviKasko"] as decimal?;
            rows[i++].PresmetanaProvizija = reader["PresmetanaProvizijaOsiguruvanjeNaPlovniObjektiKasko"] as decimal?;
            rows[i++].PresmetanaProvizija = reader["PresmetanaProvizijaOsiguruvanjeNaStokaVoPrevoz"] as decimal?;
            rows[i++].PresmetanaProvizija = reader["PresmetanaProvizijaOsiguruvanjeNaImotVoPozhar"] as decimal?;
            rows[i++].PresmetanaProvizija = reader["PresmetanaProvizijaOstanatiOsiguruvanjaNaImoti"] as decimal?;
            rows[i++].PresmetanaProvizija = reader["PresmetanaProvizijaOsiguruvanjeOdAvtomobilskaOdgovornost"] as decimal?;
            rows[i++].PresmetanaProvizija = reader["PresmetanaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi"] as decimal?;
            rows[i++].PresmetanaProvizija = reader["PresmetanaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti"] as decimal?;
            rows[i++].PresmetanaProvizija = reader["PresmetanaProvizijaOstanatiOsiguruvanjaOdOdgovornost"] as decimal?;
            rows[i++].PresmetanaProvizija = reader["PresmetanaProvizijaOsiguruvanjeNaKrediti"] as decimal?;
            rows[i++].PresmetanaProvizija = reader["PresmetanaProvizijaOsiguruvanjeNaGarantii"] as decimal?;
            rows[i++].PresmetanaProvizija = reader["PresmetanaProvizijaOsiguruvanjeOdFinansiskiZagubi"] as decimal?;
            rows[i++].PresmetanaProvizija = reader["PresmetanaProvizijaOsiguruvanjeNaPravnaZashtita"] as decimal?;
            rows[i++].PresmetanaProvizija = reader["PresmetanaProvizijaOsiguruvanjeNaTuristichkiUslugi"] as decimal?;
            rows[i++].PresmetanaProvizija = reader["PresmetanaProvizijaOsiguruvanjeNaZhivot"] as decimal?;
            rows[i++].PresmetanaProvizija = reader["PresmetanaProvizijaOsnovno"] as decimal?;
            rows[i++].PresmetanaProvizija = reader["PresmetanaProvizijaDopolnitelno"] as decimal?;
            rows[i++].PresmetanaProvizija = reader["PresmetanaProvizijaRentno"] as decimal?;
            rows[i++].PresmetanaProvizija = reader["PresmetanaProvizijaOsiguruvanjeNaBrakIliPoroduvanje"] as decimal?;
            rows[i++].PresmetanaProvizija = reader["PresmetanaProvizijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi"] as decimal?;
            rows[i++].PresmetanaProvizija = reader["PresmetanaProvizijaOsiguruvanjeNaTontina"] as decimal?;
            rows[i++].PresmetanaProvizija = reader["PresmetanaProvizijaOsiguruvanjeNaSredstvaZaKapital"] as decimal?;
            i = 0;
            rows[i++].RealiziranaProvizija = reader["RealiziranaProvizijaOsiguruvanjeOdNezgoda"] as decimal?;
            rows[i++].RealiziranaProvizija = reader["RealiziranaProvizijaZdravstvenoOsiguruvanje"] as decimal?;
            rows[i++].RealiziranaProvizija = reader["RealiziranaProvizijaOsiguruvanjeNaPatnichkiVozilaKasko"] as decimal?;
            rows[i++].RealiziranaProvizija = reader["RealiziranaProvizijaOsiguruvanjeNaShinskiVozilaKasko"] as decimal?;
            rows[i++].RealiziranaProvizija = reader["RealiziranaProvizijaOsiguruvanjeNaVozduhoploviKasko"] as decimal?;
            rows[i++].RealiziranaProvizija = reader["RealiziranaProvizijaOsiguruvanjeNaPlovniObjektiKasko"] as decimal?;
            rows[i++].RealiziranaProvizija = reader["RealiziranaProvizijaOsiguruvanjeNaStokaVoPrevoz"] as decimal?;
            rows[i++].RealiziranaProvizija = reader["RealiziranaProvizijaOsiguruvanjeNaImotVoPozhar"] as decimal?;
            rows[i++].RealiziranaProvizija = reader["RealiziranaProvizijaOstanatiOsiguruvanjaNaImoti"] as decimal?;
            rows[i++].RealiziranaProvizija = reader["RealiziranaProvizijaOsiguruvanjeOdAvtomobilskaOdgovornost"] as decimal?;
            rows[i++].RealiziranaProvizija = reader["RealiziranaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi"] as decimal?;
            rows[i++].RealiziranaProvizija = reader["RealiziranaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti"] as decimal?;
            rows[i++].RealiziranaProvizija = reader["RealiziranaProvizijaOstanatiOsiguruvanjaOdOdgovornost"] as decimal?;
            rows[i++].RealiziranaProvizija = reader["RealiziranaProvizijaOsiguruvanjeNaKrediti"] as decimal?;
            rows[i++].RealiziranaProvizija = reader["RealiziranaProvizijaOsiguruvanjeNaGarantii"] as decimal?;
            rows[i++].RealiziranaProvizija = reader["RealiziranaProvizijaOsiguruvanjeOdFinansiskiZagubi"] as decimal?;
            rows[i++].RealiziranaProvizija = reader["RealiziranaProvizijaOsiguruvanjeNaPravnaZashtita"] as decimal?;
            rows[i++].RealiziranaProvizija = reader["RealiziranaProvizijaOsiguruvanjeNaTuristichkiUslugi"] as decimal?;
            rows[i++].RealiziranaProvizija = reader["RealiziranaProvizijaOsiguruvanjeNaZhivot"] as decimal?;
            rows[i++].RealiziranaProvizija = reader["RealiziranaProvizijaOsnovno"] as decimal?;
            rows[i++].RealiziranaProvizija = reader["RealiziranaProvizijaDopolnitelno"] as decimal?;
            rows[i++].RealiziranaProvizija = reader["RealiziranaProvizijaRentno"] as decimal?;
            rows[i++].RealiziranaProvizija = reader["RealiziranaProvizijaOsiguruvanjeNaBrakIliPoroduvanje"] as decimal?;
            rows[i++].RealiziranaProvizija = reader["RealiziranaProvizijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi"] as decimal?;
            rows[i++].RealiziranaProvizija = reader["RealiziranaProvizijaOsiguruvanjeNaTontina"] as decimal?;
            rows[i++].RealiziranaProvizija = reader["RealiziranaProvizijaOsiguruvanjeNaSredstvaZaKapital"] as decimal?;
            return rows;
        }

        // Helper: Delete ALL rows for this quarter/year (both preview and final)
        private async Task DeleteAllRowsForQuarterAsync(DateTime startDate, DateTime endDate)
        {
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                using (var cmd = new SqlCommand("DELETE FROM ASOKvartalenIzvestajArhiva WHERE StartDate = @StartDate AND EndDate = @EndDate", connection))
                {
                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    await cmd.ExecuteNonQueryAsync();
                }
            }
        }

        public async Task<IActionResult> OnPostSaveAsync()
        {
            var (startDate, endDate) = GetQuarterDateRange(SelectedQuarter, SelectedYear);
            if (await CheckIfConfirmedAsync(startDate, endDate))
            {
                IsSaved = true;
                return Page();
            }
            
            // Ensure TableRows is properly initialized
            if (TableRows == null || TableRows.Count != OBD1Classes.Count)
            {
                TableRows = OBD1Classes.Select(c => new OBD1Row { Code = c.Code, Name = c.Name }).ToList();
            }
            
            // Check if there's any actual data to save
            bool hasData = TableRows.Any(r => 
                r.BrojDogovori.HasValue || 
                r.Premija.HasValue || 
                r.PresmetanaProvizija.HasValue || 
                r.RealiziranaProvizija.HasValue);
            
            if (!hasData)
            {
                // No data in UI, try to load from database first
                var loadedRows = await LoadExportRowsAsync(startDate, endDate);
                if (loadedRows != null && loadedRows.Count > 0)
                {
                    // Check if loaded data has any values
                    bool loadedHasData = loadedRows.Any(r => 
                        r.BrojDogovori.HasValue || 
                        r.Premija.HasValue || 
                        r.PresmetanaProvizija.HasValue || 
                        r.RealiziranaProvizija.HasValue);
                    
                    if (loadedHasData)
                    {
                        TableRows = loadedRows;
                        hasData = true;
                    }
                }
            }
            
            if (!hasData)
            {
                // Still no data to save, redirect back to page
                return RedirectToPage();
            }
            
            // Delete preview rows for this user/quarter/year
            await DeletePreviewRowsAsync(startDate, endDate);
            
            // Insert final row
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                using (var cmd = new SqlCommand(@"INSERT INTO ASOKvartalenIzvestajArhiva
(DateCreated, UsernameCreated, StartDate, EndDate,
OsiguruvanjeOdNezgoda, ZdravstvenoOsiguruvanje, OsiguruvanjeNaPatnichkiVozilaKasko, OsiguruvanjeNaShinskiVozilaKasko, OsiguruvanjeNaVozduhoploviKasko, OsiguruvanjeNaPlovniObjektiKasko, OsiguruvanjeNaStokaVoPrevoz, OsiguruvanjeNaImotVoPozhar, OstanatiOsiguruvanjaNaImoti, OsiguruvanjeOdAvtomobilskaOdgovornost, OsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi, OsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti, OstanatiOsiguruvanjaOdOdgovornost, OsiguruvanjeNaKrediti, OsiguruvanjeNaGarantii, OsiguruvanjeOdFinansiskiZagubi, OsiguruvanjeNaPravnaZashtita, OsiguruvanjeNaTuristichkiUslugi, OsiguruvanjeNaZhivot, Osnovno, Dopolnitelno, Rentno, OsiguruvanjeNaBrakIliPoroduvanje, OsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi, OsiguruvanjeNaTontina, OsiguruvanjeNaSredstvaZaKapital,
PremijaOsiguruvanjeOdNezgoda, PremijaZdravstvenoOsiguruvanje, PremijaOsiguruvanjeNaPatnichkiVozilaKasko, PremijaOsiguruvanjeNaShinskiVozilaKasko, PremijaOsiguruvanjeNaVozduhoploviKasko, PremijaOsiguruvanjeNaPlovniObjektiKasko, PremijaOsiguruvanjeNaStokaVoPrevoz, PremijaOsiguruvanjeNaImotVoPozhar, PremijaOstanatiOsiguruvanjaNaImoti, PremijaOsiguruvanjeOdAvtomobilskaOdgovornost, PremijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi, PremijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti, PremijaOstanatiOsiguruvanjaOdOdgovornost, PremijaOsiguruvanjeNaKrediti, PremijaOsiguruvanjeNaGarantii, PremijaOsiguruvanjeOdFinansiskiZagubi, PremijaOsiguruvanjeNaPravnaZashtita, PremijaOsiguruvanjeNaTuristichkiUslugi, PremijaOsiguruvanjeNaZhivot, PremijaOsnovno, PremijaDopolnitelno, PremijaRentno, PremijaOsiguruvanjeNaBrakIliPoroduvanje, PremijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi, PremijaOsiguruvanjeNaTontina, PremijaOsiguruvanjeNaSredstvaZaKapital,
PresmetanaProvizijaOsiguruvanjeOdNezgoda, PresmetanaProvizijaZdravstvenoOsiguruvanje, PresmetanaProvizijaOsiguruvanjeNaPatnichkiVozilaKasko, PresmetanaProvizijaOsiguruvanjeNaShinskiVozilaKasko, PresmetanaProvizijaOsiguruvanjeNaVozduhoploviKasko, PresmetanaProvizijaOsiguruvanjeNaPlovniObjektiKasko, PresmetanaProvizijaOsiguruvanjeNaStokaVoPrevoz, PresmetanaProvizijaOsiguruvanjeNaImotVoPozhar, PresmetanaProvizijaOstanatiOsiguruvanjaNaImoti, PresmetanaProvizijaOsiguruvanjeOdAvtomobilskaOdgovornost, PresmetanaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi, PresmetanaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti, PresmetanaProvizijaOstanatiOsiguruvanjaOdOdgovornost, PresmetanaProvizijaOsiguruvanjeNaKrediti, PresmetanaProvizijaOsiguruvanjeNaGarantii, PresmetanaProvizijaOsiguruvanjeOdFinansiskiZagubi, PresmetanaProvizijaOsiguruvanjeNaPravnaZashtita, PresmetanaProvizijaOsiguruvanjeNaTuristichkiUslugi, PresmetanaProvizijaOsiguruvanjeNaZhivot, PresmetanaProvizijaOsnovno, PresmetanaProvizijaDopolnitelno, PresmetanaProvizijaRentno, PresmetanaProvizijaOsiguruvanjeNaBrakIliPoroduvanje, PresmetanaProvizijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi, PresmetanaProvizijaOsiguruvanjeNaTontina, PresmetanaProvizijaOsiguruvanjeNaSredstvaZaKapital,
RealiziranaProvizijaOsiguruvanjeOdNezgoda, RealiziranaProvizijaZdravstvenoOsiguruvanje, RealiziranaProvizijaOsiguruvanjeNaPatnichkiVozilaKasko, RealiziranaProvizijaOsiguruvanjeNaShinskiVozilaKasko, RealiziranaProvizijaOsiguruvanjeNaVozduhoploviKasko, RealiziranaProvizijaOsiguruvanjeNaPlovniObjektiKasko, RealiziranaProvizijaOsiguruvanjeNaStokaVoPrevoz, RealiziranaProvizijaOsiguruvanjeNaImotVoPozhar, RealiziranaProvizijaOstanatiOsiguruvanjaNaImoti, RealiziranaProvizijaOsiguruvanjeOdAvtomobilskaOdgovornost, RealiziranaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi, RealiziranaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti, RealiziranaProvizijaOstanatiOsiguruvanjaOdOdgovornost, RealiziranaProvizijaOsiguruvanjeNaKrediti, RealiziranaProvizijaOsiguruvanjeNaGarantii, RealiziranaProvizijaOsiguruvanjeOdFinansiskiZagubi, RealiziranaProvizijaOsiguruvanjeNaPravnaZashtita, RealiziranaProvizijaOsiguruvanjeNaTuristichkiUslugi, RealiziranaProvizijaOsiguruvanjeNaZhivot, RealiziranaProvizijaOsnovno, RealiziranaProvizijaDopolnitelno, RealiziranaProvizijaRentno, RealiziranaProvizijaOsiguruvanjeNaBrakIliPoroduvanje, RealiziranaProvizijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi, RealiziranaProvizijaOsiguruvanjeNaTontina, RealiziranaProvizijaOsiguruvanjeNaSredstvaZaKapital, VkupenBrojNaDogovori, VkupnaPremija, VkupnaProvizija, VkupnaRealiziranaProvizija)
VALUES
(GETDATE(), @UsernameCreated, @StartDate, @EndDate,
@OsiguruvanjeOdNezgoda, @ZdravstvenoOsiguruvanje, @OsiguruvanjeNaPatnichkiVozilaKasko, @OsiguruvanjeNaShinskiVozilaKasko, @OsiguruvanjeNaVozduhoploviKasko, @OsiguruvanjeNaPlovniObjektiKasko, @OsiguruvanjeNaStokaVoPrevoz, @OsiguruvanjeNaImotVoPozhar, @OstanatiOsiguruvanjaNaImoti, @OsiguruvanjeOdAvtomobilskaOdgovornost, @OsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi, @OsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti, @OstanatiOsiguruvanjaOdOdgovornost, @OsiguruvanjeNaKrediti, @OsiguruvanjeNaGarantii, @OsiguruvanjeOdFinansiskiZagubi, @OsiguruvanjeNaPravnaZashtita, @OsiguruvanjeNaTuristichkiUslugi, @OsiguruvanjeNaZhivot, @Osnovno, @Dopolnitelno, @Rentno, @OsiguruvanjeNaBrakIliPoroduvanje, @OsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi, @OsiguruvanjeNaTontina, @OsiguruvanjeNaSredstvaZaKapital,
@PremijaOsiguruvanjeOdNezgoda, @PremijaZdravstvenoOsiguruvanje, @PremijaOsiguruvanjeNaPatnichkiVozilaKasko, @PremijaOsiguruvanjeNaShinskiVozilaKasko, @PremijaOsiguruvanjeNaVozduhoploviKasko, @PremijaOsiguruvanjeNaPlovniObjektiKasko, @PremijaOsiguruvanjeNaStokaVoPrevoz, @PremijaOsiguruvanjeNaImotVoPozhar, @PremijaOstanatiOsiguruvanjaNaImoti, @PremijaOsiguruvanjeOdAvtomobilskaOdgovornost, @PremijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi, @PremijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti, @PremijaOstanatiOsiguruvanjaOdOdgovornost, @PremijaOsiguruvanjeNaKrediti, @PremijaOsiguruvanjeNaGarantii, @PremijaOsiguruvanjeOdFinansiskiZagubi, @PremijaOsiguruvanjeNaPravnaZashtita, @PremijaOsiguruvanjeNaTuristichkiUslugi, @PremijaOsiguruvanjeNaZhivot, @PremijaOsnovno, @PremijaDopolnitelno, @PremijaRentno, @PremijaOsiguruvanjeNaBrakIliPoroduvanje, @PremijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi, @PremijaOsiguruvanjeNaTontina, @PremijaOsiguruvanjeNaSredstvaZaKapital,
@PresmetanaProvizijaOsiguruvanjeOdNezgoda, @PresmetanaProvizijaZdravstvenoOsiguruvanje, @PresmetanaProvizijaOsiguruvanjeNaPatnichkiVozilaKasko, @PresmetanaProvizijaOsiguruvanjeNaShinskiVozilaKasko, @PresmetanaProvizijaOsiguruvanjeNaVozduhoploviKasko, @PresmetanaProvizijaOsiguruvanjeNaPlovniObjektiKasko, @PresmetanaProvizijaOsiguruvanjeNaStokaVoPrevoz, @PresmetanaProvizijaOsiguruvanjeNaImotVoPozhar, @PresmetanaProvizijaOstanatiOsiguruvanjaNaImoti, @PresmetanaProvizijaOsiguruvanjeOdAvtomobilskaOdgovornost, @PresmetanaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi, @PresmetanaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti, @PresmetanaProvizijaOstanatiOsiguruvanjaOdOdgovornost, @PresmetanaProvizijaOsiguruvanjeNaKrediti, @PresmetanaProvizijaOsiguruvanjeNaGarantii, @PresmetanaProvizijaOsiguruvanjeOdFinansiskiZagubi, @PresmetanaProvizijaOsiguruvanjeNaPravnaZashtita, @PresmetanaProvizijaOsiguruvanjeNaTuristichkiUslugi, @PresmetanaProvizijaOsiguruvanjeNaZhivot, @PresmetanaProvizijaOsnovno, @PresmetanaProvizijaDopolnitelno, @PresmetanaProvizijaRentno, @PresmetanaProvizijaOsiguruvanjeNaBrakIliPoroduvanje, @PresmetanaProvizijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi, @PresmetanaProvizijaOsiguruvanjeNaTontina, @PresmetanaProvizijaOsiguruvanjeNaSredstvaZaKapital,
@RealiziranaProvizijaOsiguruvanjeOdNezgoda, @RealiziranaProvizijaZdravstvenoOsiguruvanje, @RealiziranaProvizijaOsiguruvanjeNaPatnichkiVozilaKasko, @RealiziranaProvizijaOsiguruvanjeNaShinskiVozilaKasko, @RealiziranaProvizijaOsiguruvanjeNaVozduhoploviKasko, @RealiziranaProvizijaOsiguruvanjeNaPlovniObjektiKasko, @RealiziranaProvizijaOsiguruvanjeNaStokaVoPrevoz, @RealiziranaProvizijaOsiguruvanjeNaImotVoPozhar, @RealiziranaProvizijaOstanatiOsiguruvanjaNaImoti, @RealiziranaProvizijaOsiguruvanjeOdAvtomobilskaOdgovornost, @RealiziranaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi, @RealiziranaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti, @RealiziranaProvizijaOstanatiOsiguruvanjaOdOdgovornost, @RealiziranaProvizijaOsiguruvanjeNaKrediti, @RealiziranaProvizijaOsiguruvanjeNaGarantii, @RealiziranaProvizijaOsiguruvanjeOdFinansiskiZagubi, @RealiziranaProvizijaOsiguruvanjeNaPravnaZashtita, @RealiziranaProvizijaOsiguruvanjeNaTuristichkiUslugi, @RealiziranaProvizijaOsiguruvanjeNaZhivot, @RealiziranaProvizijaOsnovno, @RealiziranaProvizijaDopolnitelno, @RealiziranaProvizijaRentno, @RealiziranaProvizijaOsiguruvanjeNaBrakIliPoroduvanje, @RealiziranaProvizijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi, @RealiziranaProvizijaOsiguruvanjeNaTontina, @RealiziranaProvizijaOsiguruvanjeNaSredstvaZaKapital, @VkupnaRealiziranaProvizija)", connection))
                {
                    cmd.Parameters.AddWithValue("@UsernameCreated", GetFinalMarker());
                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    
                    // Add BrojDogovori parameters (26 columns)
                    cmd.Parameters.AddWithValue("@OsiguruvanjeOdNezgoda", TableRows[0].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@ZdravstvenoOsiguruvanje", TableRows[1].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaPatnichkiVozilaKasko", TableRows[2].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaShinskiVozilaKasko", TableRows[3].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaVozduhoploviKasko", TableRows[4].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaPlovniObjektiKasko", TableRows[5].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaStokaVoPrevoz", TableRows[6].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaImotVoPozhar", TableRows[7].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OstanatiOsiguruvanjaNaImoti", TableRows[8].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeOdAvtomobilskaOdgovornost", TableRows[9].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi", TableRows[10].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti", TableRows[11].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OstanatiOsiguruvanjaOdOdgovornost", TableRows[12].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaKrediti", TableRows[13].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaGarantii", TableRows[14].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeOdFinansiskiZagubi", TableRows[15].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaPravnaZashtita", TableRows[16].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaTuristichkiUslugi", TableRows[17].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaZhivot", TableRows[18].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@Osnovno", TableRows[19].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@Dopolnitelno", TableRows[20].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@Rentno", TableRows[21].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaBrakIliPoroduvanje", TableRows[22].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi", TableRows[23].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaTontina", TableRows[24].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaSredstvaZaKapital", TableRows[25].BrojDogovori ?? (object)DBNull.Value);
                    
                    // Add Premija parameters (26 columns)
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeOdNezgoda", TableRows[0].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaZdravstvenoOsiguruvanje", TableRows[1].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaPatnichkiVozilaKasko", TableRows[2].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaShinskiVozilaKasko", TableRows[3].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaVozduhoploviKasko", TableRows[4].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaPlovniObjektiKasko", TableRows[5].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaStokaVoPrevoz", TableRows[6].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaImotVoPozhar", TableRows[7].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOstanatiOsiguruvanjaNaImoti", TableRows[8].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeOdAvtomobilskaOdgovornost", TableRows[9].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi", TableRows[10].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti", TableRows[11].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOstanatiOsiguruvanjaOdOdgovornost", TableRows[12].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaKrediti", TableRows[13].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaGarantii", TableRows[14].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeOdFinansiskiZagubi", TableRows[15].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaPravnaZashtita", TableRows[16].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaTuristichkiUslugi", TableRows[17].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaZhivot", TableRows[18].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsnovno", TableRows[19].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaDopolnitelno", TableRows[20].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaRentno", TableRows[21].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaBrakIliPoroduvanje", TableRows[22].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi", TableRows[23].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaTontina", TableRows[24].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaSredstvaZaKapital", TableRows[25].Premija ?? (object)DBNull.Value);
                    
                    // Add PresmetanaProvizija parameters (26 columns)
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeOdNezgoda", TableRows[0].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaZdravstvenoOsiguruvanje", TableRows[1].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaPatnichkiVozilaKasko", TableRows[2].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaShinskiVozilaKasko", TableRows[3].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaVozduhoploviKasko", TableRows[4].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaPlovniObjektiKasko", TableRows[5].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaStokaVoPrevoz", TableRows[6].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaImotVoPozhar", TableRows[7].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOstanatiOsiguruvanjaNaImoti", TableRows[8].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeOdAvtomobilskaOdgovornost", TableRows[9].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi", TableRows[10].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti", TableRows[11].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOstanatiOsiguruvanjaOdOdgovornost", TableRows[12].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaKrediti", TableRows[13].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaGarantii", TableRows[14].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeOdFinansiskiZagubi", TableRows[15].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaPravnaZashtita", TableRows[16].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaTuristichkiUslugi", TableRows[17].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaZhivot", TableRows[18].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsnovno", TableRows[19].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaDopolnitelno", TableRows[20].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaRentno", TableRows[21].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaBrakIliPoroduvanje", TableRows[22].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi", TableRows[23].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaTontina", TableRows[24].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaSredstvaZaKapital", TableRows[25].PresmetanaProvizija ?? (object)DBNull.Value);
                    
                    // Add RealiziranaProvizija parameters (26 columns)
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeOdNezgoda", TableRows[0].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaZdravstvenoOsiguruvanje", TableRows[1].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaPatnichkiVozilaKasko", TableRows[2].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaShinskiVozilaKasko", TableRows[3].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaVozduhoploviKasko", TableRows[4].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaPlovniObjektiKasko", TableRows[5].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaStokaVoPrevoz", TableRows[6].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaImotVoPozhar", TableRows[7].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOstanatiOsiguruvanjaNaImoti", TableRows[8].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeOdAvtomobilskaOdgovornost", TableRows[9].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi", TableRows[10].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti", TableRows[11].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOstanatiOsiguruvanjaOdOdgovornost", TableRows[12].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaKrediti", TableRows[13].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaGarantii", TableRows[14].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeOdFinansiskiZagubi", TableRows[15].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaPravnaZashtita", TableRows[16].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaTuristichkiUslugi", TableRows[17].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaZhivot", TableRows[18].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsnovno", TableRows[19].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaDopolnitelno", TableRows[20].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaRentno", TableRows[21].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaBrakIliPoroduvanje", TableRows[22].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi", TableRows[23].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaTontina", TableRows[24].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaSredstvaZaKapital", TableRows[25].RealiziranaProvizija ?? (object)DBNull.Value);
                    
                    // Add VkupenBrojNaDogovori parameter
                    cmd.Parameters.AddWithValue("@VkupenBrojNaDogovori", TableRows.Sum(r => r.BrojDogovori ?? 0));
                    
                    // Add VkupnaPremija parameter
                    cmd.Parameters.AddWithValue("@VkupnaPremija", TableRows.Sum(r => r.Premija ?? 0));
                    
                    // Add VkupnaProvizija parameter
                    cmd.Parameters.AddWithValue("@VkupnaProvizija", TableRows.Sum(r => r.PresmetanaProvizija ?? 0));
                    
                    // Add VkupnaRealiziranaProvizija parameter
                    cmd.Parameters.AddWithValue("@VkupnaRealiziranaProvizija", TableRows.Sum(r => r.RealiziranaProvizija ?? 0));
                    
                    await cmd.ExecuteNonQueryAsync();
                }
            }
            
            IsSaved = true;
            return Page();
        }

        public async Task<IActionResult> OnPostCancelAsync()
        {
            var (startDate, endDate) = GetQuarterDateRange(SelectedQuarter, SelectedYear);
            
            // Delete only preview rows for this user/quarter/year
            await DeletePreviewRowsAsync(startDate, endDate);
            
            // Clear the UI data
            TableRows = OBD1Classes.Select(c => new OBD1Row { Code = c.Code, Name = c.Name }).ToList();
            
            IsSaved = false;
            return Page();
        }

        private async Task<bool> CheckIfSavedAsync(DateTime startDate, DateTime endDate)
        {
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                using (var cmd = new SqlCommand("SELECT COUNT(1) FROM ASOKvartalenIzvestajArhiva WHERE StartDate = @StartDate AND EndDate = @EndDate AND UsernameCreated = @Username", connection))
                {
                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    cmd.Parameters.AddWithValue("@Username", GetFinalMarker());
                    var count = (int)await cmd.ExecuteScalarAsync();
                    return count > 0;
                }
            }
        }

        private async Task GeneratePreviewAsync()
        {
            System.Diagnostics.Debug.WriteLine("GeneratePreviewAsync: Starting");
            var (startDate, endDate) = GetQuarterDateRange(SelectedQuarter, SelectedYear);
            System.Diagnostics.Debug.WriteLine($"GeneratePreviewAsync: StartDate={startDate}, EndDate={endDate}");
            
            try
            {
                // Clean up any previous preview data for this user/quarter/year
                System.Diagnostics.Debug.WriteLine("GeneratePreviewAsync: Cleaning up previous preview data");
                await CleanupPreviewDataAsync(startDate, endDate);

                // Load real data from the stored procedure
                System.Diagnostics.Debug.WriteLine("GeneratePreviewAsync: Loading data from stored procedure");
                var realData = await LoadDataFromStoredProcedureAsync(startDate, endDate, SelectedOsiguritelId);
                System.Diagnostics.Debug.WriteLine($"GeneratePreviewAsync: Loaded {realData?.Count ?? 0} rows from stored procedure");
                
                // If we got real data, use it; otherwise initialize empty rows
                if (realData != null && realData.Count > 0)
                {
                    // Check if we have any non-null values
                    var hasData = realData.Any(r => r.BrojDogovori.HasValue || r.Premija.HasValue || r.PresmetanaProvizija.HasValue || r.RealiziranaProvizija.HasValue);
                    System.Diagnostics.Debug.WriteLine($"GeneratePreviewAsync: Has data = {hasData}");
                    
                    if (hasData)
                    {
                        TableRows = realData;
                        System.Diagnostics.Debug.WriteLine("GeneratePreviewAsync: Using real data from stored procedure");
                    }
                    else
                    {
                        // Initialize empty rows if no data was returned from stored procedure
                        TableRows = OBD1Classes.Select(c => new OBD1Row { Code = c.Code, Name = c.Name }).ToList();
                        System.Diagnostics.Debug.WriteLine("GeneratePreviewAsync: Using empty rows (no real data found)");
                    }
                }
                else
                {
                    // Initialize empty rows if no data was returned from stored procedure
                    TableRows = OBD1Classes.Select(c => new OBD1Row { Code = c.Code, Name = c.Name }).ToList();
                    System.Diagnostics.Debug.WriteLine("GeneratePreviewAsync: Using empty rows (no data returned)");
                }

                // Save the data to the archive table for preview
                System.Diagnostics.Debug.WriteLine("GeneratePreviewAsync: Saving to archive table");
                await SaveToArchiveAsync(startDate, endDate, true);
                System.Diagnostics.Debug.WriteLine("GeneratePreviewAsync: Saved to archive table");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"GeneratePreviewAsync: Exception occurred: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"GeneratePreviewAsync: Stack trace: {ex.StackTrace}");
                // Initialize empty rows on error
                TableRows = OBD1Classes.Select(c => new OBD1Row { Code = c.Code, Name = c.Name }).ToList();
            }
        }

        private async Task GenerateReportAsync()
        {
            var (startDate, endDate) = GetQuarterDateRange(SelectedQuarter, SelectedYear);
            // Clean up any preview data for this user/quarter/year
            await CleanupPreviewDataAsync(startDate, endDate);

            // Load real data from the stored procedure first
                            var realData = await LoadDataFromStoredProcedureAsync(startDate, endDate, SelectedOsiguritelId);
            
            // If we got real data, use it; otherwise use current TableRows
            if (realData != null && realData.Count > 0)
            {
                TableRows = realData;
            }
            else if (TableRows == null || TableRows.Count != OBD1Classes.Count)
            {
                // Initialize empty rows if no data was returned from stored procedure
                TableRows = OBD1Classes.Select(c => new OBD1Row { Code = c.Code, Name = c.Name }).ToList();
            }

            // Only insert if there is any data
            bool hasData = TableRows.Any(r =>
                r.BrojDogovori.HasValue ||
                r.Premija.HasValue ||
                r.PresmetanaProvizija.HasValue ||
                r.RealiziranaProvizija.HasValue);

            if (!hasData)
                return;

            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                using (var cmd = new SqlCommand(@"INSERT INTO ASOKvartalenIzvestajArhiva
(DateCreated, UsernameCreated, StartDate, EndDate,
OsiguruvanjeOdNezgoda, ZdravstvenoOsiguruvanje, OsiguruvanjeNaPatnichkiVozilaKasko, OsiguruvanjeNaShinskiVozilaKasko, OsiguruvanjeNaVozduhoploviKasko, OsiguruvanjeNaPlovniObjektiKasko, OsiguruvanjeNaStokaVoPrevoz, OsiguruvanjeNaImotVoPozhar, OstanatiOsiguruvanjaNaImoti, OsiguruvanjeOdAvtomobilskaOdgovornost, OsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi, OsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti, OstanatiOsiguruvanjaOdOdgovornost, OsiguruvanjeNaKrediti, OsiguruvanjeNaGarantii, OsiguruvanjeOdFinansiskiZagubi, OsiguruvanjeNaPravnaZashtita, OsiguruvanjeNaTuristichkiUslugi, OsiguruvanjeNaZhivot, Osnovno, Dopolnitelno, Rentno, OsiguruvanjeNaBrakIliPoroduvanje, OsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi, OsiguruvanjeNaTontina, OsiguruvanjeNaSredstvaZaKapital,
PremijaOsiguruvanjeOdNezgoda, PremijaZdravstvenoOsiguruvanje, PremijaOsiguruvanjeNaPatnichkiVozilaKasko, PremijaOsiguruvanjeNaShinskiVozilaKasko, PremijaOsiguruvanjeNaVozduhoploviKasko, PremijaOsiguruvanjeNaPlovniObjektiKasko, PremijaOsiguruvanjeNaStokaVoPrevoz, PremijaOsiguruvanjeNaImotVoPozhar, PremijaOstanatiOsiguruvanjaNaImoti, PremijaOsiguruvanjeOdAvtomobilskaOdgovornost, PremijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi, PremijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti, PremijaOstanatiOsiguruvanjaOdOdgovornost, PremijaOsiguruvanjeNaKrediti, PremijaOsiguruvanjeNaGarantii, PremijaOsiguruvanjeOdFinansiskiZagubi, PremijaOsiguruvanjeNaPravnaZashtita, PremijaOsiguruvanjeNaTuristichkiUslugi, PremijaOsiguruvanjeNaZhivot, PremijaOsnovno, PremijaDopolnitelno, PremijaRentno, PremijaOsiguruvanjeNaBrakIliPoroduvanje, PremijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi, PremijaOsiguruvanjeNaTontina, PremijaOsiguruvanjeNaSredstvaZaKapital,
PresmetanaProvizijaOsiguruvanjeOdNezgoda, PresmetanaProvizijaZdravstvenoOsiguruvanje, PresmetanaProvizijaOsiguruvanjeNaPatnichkiVozilaKasko, PresmetanaProvizijaOsiguruvanjeNaShinskiVozilaKasko, PresmetanaProvizijaOsiguruvanjeNaVozduhoploviKasko, PresmetanaProvizijaOsiguruvanjeNaPlovniObjektiKasko, PresmetanaProvizijaOsiguruvanjeNaStokaVoPrevoz, PresmetanaProvizijaOsiguruvanjeNaImotVoPozhar, PresmetanaProvizijaOstanatiOsiguruvanjaNaImoti, PresmetanaProvizijaOsiguruvanjeOdAvtomobilskaOdgovornost, PresmetanaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi, PresmetanaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti, PresmetanaProvizijaOstanatiOsiguruvanjaOdOdgovornost, PresmetanaProvizijaOsiguruvanjeNaKrediti, PresmetanaProvizijaOsiguruvanjeNaGarantii, PresmetanaProvizijaOsiguruvanjeOdFinansiskiZagubi, PresmetanaProvizijaOsiguruvanjeNaPravnaZashtita, PresmetanaProvizijaOsiguruvanjeNaTuristichkiUslugi, PresmetanaProvizijaOsiguruvanjeNaZhivot, PresmetanaProvizijaOsnovno, PresmetanaProvizijaDopolnitelno, PresmetanaProvizijaRentno, PresmetanaProvizijaOsiguruvanjeNaBrakIliPoroduvanje, PresmetanaProvizijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi, PresmetanaProvizijaOsiguruvanjeNaTontina, PresmetanaProvizijaOsiguruvanjeNaSredstvaZaKapital,
RealiziranaProvizijaOsiguruvanjeOdNezgoda, RealiziranaProvizijaZdravstvenoOsiguruvanje, RealiziranaProvizijaOsiguruvanjeNaPatnichkiVozilaKasko, RealiziranaProvizijaOsiguruvanjeNaShinskiVozilaKasko, RealiziranaProvizijaOsiguruvanjeNaVozduhoploviKasko, RealiziranaProvizijaOsiguruvanjeNaPlovniObjektiKasko, RealiziranaProvizijaOsiguruvanjeNaStokaVoPrevoz, RealiziranaProvizijaOsiguruvanjeNaImotVoPozhar, RealiziranaProvizijaOstanatiOsiguruvanjaNaImoti, RealiziranaProvizijaOsiguruvanjeOdAvtomobilskaOdgovornost, RealiziranaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi, RealiziranaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti, RealiziranaProvizijaOstanatiOsiguruvanjaOdOdgovornost, RealiziranaProvizijaOsiguruvanjeNaKrediti, RealiziranaProvizijaOsiguruvanjeNaGarantii, RealiziranaProvizijaOsiguruvanjeOdFinansiskiZagubi, RealiziranaProvizijaOsiguruvanjeNaPravnaZashtita, RealiziranaProvizijaOsiguruvanjeNaTuristichkiUslugi, RealiziranaProvizijaOsiguruvanjeNaZhivot, RealiziranaProvizijaOsnovno, RealiziranaProvizijaDopolnitelno, RealiziranaProvizijaRentno, RealiziranaProvizijaOsiguruvanjeNaBrakIliPoroduvanje, RealiziranaProvizijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi, RealiziranaProvizijaOsiguruvanjeNaTontina, RealiziranaProvizijaOsiguruvanjeNaSredstvaZaKapital, VkupnaRealiziranaProvizija)
VALUES
(GETDATE(), @UsernameCreated, @StartDate, @EndDate,
@OsiguruvanjeOdNezgoda, @ZdravstvenoOsiguruvanje, @OsiguruvanjeNaPatnichkiVozilaKasko, @OsiguruvanjeNaShinskiVozilaKasko, @OsiguruvanjeNaVozduhoploviKasko, @OsiguruvanjeNaPlovniObjektiKasko, @OsiguruvanjeNaStokaVoPrevoz, @OsiguruvanjeNaImotVoPozhar, @OstanatiOsiguruvanjaNaImoti, @OsiguruvanjeOdAvtomobilskaOdgovornost, @OsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi, @OsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti, @OstanatiOsiguruvanjaOdOdgovornost, @OsiguruvanjeNaKrediti, @OsiguruvanjeNaGarantii, @OsiguruvanjeOdFinansiskiZagubi, @OsiguruvanjeNaPravnaZashtita, @OsiguruvanjeNaTuristichkiUslugi, @OsiguruvanjeNaZhivot, @Osnovno, @Dopolnitelno, @Rentno, @OsiguruvanjeNaBrakIliPoroduvanje, @OsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi, @OsiguruvanjeNaTontina, @OsiguruvanjeNaSredstvaZaKapital,
@PremijaOsiguruvanjeOdNezgoda, @PremijaZdravstvenoOsiguruvanje, @PremijaOsiguruvanjeNaPatnichkiVozilaKasko, @PremijaOsiguruvanjeNaShinskiVozilaKasko, @PremijaOsiguruvanjeNaVozduhoploviKasko, @PremijaOsiguruvanjeNaPlovniObjektiKasko, @PremijaOsiguruvanjeNaStokaVoPrevoz, @PremijaOsiguruvanjeNaImotVoPozhar, @PremijaOstanatiOsiguruvanjaNaImoti, @PremijaOsiguruvanjeOdAvtomobilskaOdgovornost, @PremijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi, @PremijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti, @PremijaOstanatiOsiguruvanjaOdOdgovornost, @PremijaOsiguruvanjeNaKrediti, @PremijaOsiguruvanjeNaGarantii, @PremijaOsiguruvanjeOdFinansiskiZagubi, @PremijaOsiguruvanjeNaPravnaZashtita, @PremijaOsiguruvanjeNaTuristichkiUslugi, @PremijaOsiguruvanjeNaZhivot, @PremijaOsnovno, @PremijaDopolnitelno, @PremijaRentno, @PremijaOsiguruvanjeNaBrakIliPoroduvanje, @PremijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi, @PremijaOsiguruvanjeNaTontina, @PremijaOsiguruvanjeNaSredstvaZaKapital,
@PresmetanaProvizijaOsiguruvanjeOdNezgoda, @PresmetanaProvizijaZdravstvenoOsiguruvanje, @PresmetanaProvizijaOsiguruvanjeNaPatnichkiVozilaKasko, @PresmetanaProvizijaOsiguruvanjeNaShinskiVozilaKasko, @PresmetanaProvizijaOsiguruvanjeNaVozduhoploviKasko, @PresmetanaProvizijaOsiguruvanjeNaPlovniObjektiKasko, @PresmetanaProvizijaOsiguruvanjeNaStokaVoPrevoz, @PresmetanaProvizijaOsiguruvanjeNaImotVoPozhar, @PresmetanaProvizijaOstanatiOsiguruvanjaNaImoti, @PresmetanaProvizijaOsiguruvanjeOdAvtomobilskaOdgovornost, @PresmetanaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi, @PresmetanaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti, @PresmetanaProvizijaOstanatiOsiguruvanjaOdOdgovornost, @PresmetanaProvizijaOsiguruvanjeNaKrediti, @PresmetanaProvizijaOsiguruvanjeNaGarantii, @PresmetanaProvizijaOsiguruvanjeOdFinansiskiZagubi, @PresmetanaProvizijaOsiguruvanjeNaPravnaZashtita, @PresmetanaProvizijaOsiguruvanjeNaTuristichkiUslugi, @PresmetanaProvizijaOsiguruvanjeNaZhivot, @PresmetanaProvizijaOsnovno, @PresmetanaProvizijaDopolnitelno, @PresmetanaProvizijaRentno, @PresmetanaProvizijaOsiguruvanjeNaBrakIliPoroduvanje, @PresmetanaProvizijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi, @PresmetanaProvizijaOsiguruvanjeNaTontina, @PresmetanaProvizijaOsiguruvanjeNaSredstvaZaKapital,
@RealiziranaProvizijaOsiguruvanjeOdNezgoda, @RealiziranaProvizijaZdravstvenoOsiguruvanje, @RealiziranaProvizijaOsiguruvanjeNaPatnichkiVozilaKasko, @RealiziranaProvizijaOsiguruvanjeNaShinskiVozilaKasko, @RealiziranaProvizijaOsiguruvanjeNaVozduhoploviKasko, @RealiziranaProvizijaOsiguruvanjeNaPlovniObjektiKasko, @RealiziranaProvizijaOsiguruvanjeNaStokaVoPrevoz, @RealiziranaProvizijaOsiguruvanjeNaImotVoPozhar, @RealiziranaProvizijaOstanatiOsiguruvanjaNaImoti, @RealiziranaProvizijaOsiguruvanjeOdAvtomobilskaOdgovornost, @RealiziranaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi, @RealiziranaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti, @RealiziranaProvizijaOstanatiOsiguruvanjaOdOdgovornost, @RealiziranaProvizijaOsiguruvanjeNaKrediti, @RealiziranaProvizijaOsiguruvanjeNaGarantii, @RealiziranaProvizijaOsiguruvanjeOdFinansiskiZagubi, @RealiziranaProvizijaOsiguruvanjeNaPravnaZashtita, @RealiziranaProvizijaOsiguruvanjeNaTuristichkiUslugi, @RealiziranaProvizijaOsiguruvanjeNaZhivot, @RealiziranaProvizijaOsnovno, @RealiziranaProvizijaDopolnitelno, @RealiziranaProvizijaRentno, @RealiziranaProvizijaOsiguruvanjeNaBrakIliPoroduvanje, @RealiziranaProvizijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi, @RealiziranaProvizijaOsiguruvanjeNaTontina, @RealiziranaProvizijaOsiguruvanjeNaSredstvaZaKapital, @VkupnaRealiziranaProvizija)", connection))
                {
                    cmd.Parameters.AddWithValue("@UsernameCreated", GetFinalMarker());
                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    
                    // Add BrojDogovori parameters (26 columns)
                    cmd.Parameters.AddWithValue("@OsiguruvanjeOdNezgoda", TableRows[0].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@ZdravstvenoOsiguruvanje", TableRows[1].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaPatnichkiVozilaKasko", TableRows[2].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaShinskiVozilaKasko", TableRows[3].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaVozduhoploviKasko", TableRows[4].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaPlovniObjektiKasko", TableRows[5].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaStokaVoPrevoz", TableRows[6].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaImotVoPozhar", TableRows[7].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OstanatiOsiguruvanjaNaImoti", TableRows[8].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeOdAvtomobilskaOdgovornost", TableRows[9].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi", TableRows[10].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti", TableRows[11].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OstanatiOsiguruvanjaOdOdgovornost", TableRows[12].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaKrediti", TableRows[13].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaGarantii", TableRows[14].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeOdFinansiskiZagubi", TableRows[15].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaPravnaZashtita", TableRows[16].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaTuristichkiUslugi", TableRows[17].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaZhivot", TableRows[18].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@Osnovno", TableRows[19].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@Dopolnitelno", TableRows[20].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@Rentno", TableRows[21].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaBrakIliPoroduvanje", TableRows[22].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi", TableRows[23].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaTontina", TableRows[24].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaSredstvaZaKapital", TableRows[25].BrojDogovori ?? (object)DBNull.Value);
                    
                    // Add Premija parameters (26 columns)
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeOdNezgoda", TableRows[0].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaZdravstvenoOsiguruvanje", TableRows[1].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaPatnichkiVozilaKasko", TableRows[2].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaShinskiVozilaKasko", TableRows[3].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaVozduhoploviKasko", TableRows[4].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaPlovniObjektiKasko", TableRows[5].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaStokaVoPrevoz", TableRows[6].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaImotVoPozhar", TableRows[7].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOstanatiOsiguruvanjaNaImoti", TableRows[8].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeOdAvtomobilskaOdgovornost", TableRows[9].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi", TableRows[10].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti", TableRows[11].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOstanatiOsiguruvanjaOdOdgovornost", TableRows[12].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaKrediti", TableRows[13].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaGarantii", TableRows[14].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeOdFinansiskiZagubi", TableRows[15].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaPravnaZashtita", TableRows[16].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaTuristichkiUslugi", TableRows[17].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaZhivot", TableRows[18].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsnovno", TableRows[19].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaDopolnitelno", TableRows[20].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaRentno", TableRows[21].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaBrakIliPoroduvanje", TableRows[22].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi", TableRows[23].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaTontina", TableRows[24].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaSredstvaZaKapital", TableRows[25].Premija ?? (object)DBNull.Value);
                    
                    // Add PresmetanaProvizija parameters (26 columns)
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeOdNezgoda", TableRows[0].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaZdravstvenoOsiguruvanje", TableRows[1].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaPatnichkiVozilaKasko", TableRows[2].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaShinskiVozilaKasko", TableRows[3].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaVozduhoploviKasko", TableRows[4].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaPlovniObjektiKasko", TableRows[5].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaStokaVoPrevoz", TableRows[6].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaImotVoPozhar", TableRows[7].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOstanatiOsiguruvanjaNaImoti", TableRows[8].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeOdAvtomobilskaOdgovornost", TableRows[9].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi", TableRows[10].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti", TableRows[11].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOstanatiOsiguruvanjaOdOdgovornost", TableRows[12].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaKrediti", TableRows[13].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaGarantii", TableRows[14].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeOdFinansiskiZagubi", TableRows[15].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaPravnaZashtita", TableRows[16].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaTuristichkiUslugi", TableRows[17].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaZhivot", TableRows[18].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsnovno", TableRows[19].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaDopolnitelno", TableRows[20].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaRentno", TableRows[21].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaBrakIliPoroduvanje", TableRows[22].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi", TableRows[23].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaTontina", TableRows[24].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaSredstvaZaKapital", TableRows[25].PresmetanaProvizija ?? (object)DBNull.Value);
                    
                    // Add RealiziranaProvizija parameters (26 columns)
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeOdNezgoda", TableRows[0].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaZdravstvenoOsiguruvanje", TableRows[1].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaPatnichkiVozilaKasko", TableRows[2].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaShinskiVozilaKasko", TableRows[3].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaVozduhoploviKasko", TableRows[4].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaPlovniObjektiKasko", TableRows[5].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaStokaVoPrevoz", TableRows[6].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaImotVoPozhar", TableRows[7].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOstanatiOsiguruvanjaNaImoti", TableRows[8].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeOdAvtomobilskaOdgovornost", TableRows[9].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi", TableRows[10].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti", TableRows[11].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOstanatiOsiguruvanjaOdOdgovornost", TableRows[12].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaKrediti", TableRows[13].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaGarantii", TableRows[14].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeOdFinansiskiZagubi", TableRows[15].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaPravnaZashtita", TableRows[16].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaTuristichkiUslugi", TableRows[17].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaZhivot", TableRows[18].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsnovno", TableRows[19].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaDopolnitelno", TableRows[20].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaRentno", TableRows[21].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaBrakIliPoroduvanje", TableRows[22].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi", TableRows[23].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaTontina", TableRows[24].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaSredstvaZaKapital", TableRows[25].RealiziranaProvizija ?? (object)DBNull.Value);
                    
                    // Add VkupenBrojNaDogovori parameter
                    cmd.Parameters.AddWithValue("@VkupenBrojNaDogovori", TableRows.Sum(r => r.BrojDogovori ?? 0));
                    
                    // Add VkupnaPremija parameter
                    cmd.Parameters.AddWithValue("@VkupnaPremija", TableRows.Sum(r => r.Premija ?? 0));
                    
                    // Add VkupnaProvizija parameter
                    cmd.Parameters.AddWithValue("@VkupnaProvizija", TableRows.Sum(r => r.PresmetanaProvizija ?? 0));
                    
                    // Add VkupnaRealiziranaProvizija parameter
                    cmd.Parameters.AddWithValue("@VkupnaRealiziranaProvizija", TableRows.Sum(r => r.RealiziranaProvizija ?? 0));
                    
                    await cmd.ExecuteNonQueryAsync();
                    System.Diagnostics.Debug.WriteLine("SaveToArchiveAsync: Successfully saved to archive table");
                }
            }
        }

        private async Task CleanupPreviewDataAsync(DateTime startDate, DateTime endDate)
        {
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                using (var cmd = new SqlCommand("DELETE FROM ASOKvartalenIzvestajArhiva WHERE StartDate = @StartDate AND EndDate = @EndDate AND UsernameCreated LIKE @PreviewMarker", connection))
                {
                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    cmd.Parameters.AddWithValue("@PreviewMarker", GetPreviewMarker() + "%");
                    await cmd.ExecuteNonQueryAsync();
                }
            }
        }

        private async Task<List<OBD1Row>> LoadDataFromStoredProcedureAsync(DateTime startDate, DateTime endDate, int? osiguritelId = null)
        {
            System.Diagnostics.Debug.WriteLine($"LoadDataFromStoredProcedureAsync: Loading data for {startDate} to {endDate}, OsiguritelId: {osiguritelId}");
            
            var rows = OBD1Classes.Select(c => new OBD1Row { Code = c.Code, Name = c.Name }).ToList();
            
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                using (var cmd = new SqlCommand("EXEC dbo.ASO_KVARTALEN_OBD1_Display_PER_OSIGURITEL @StartDate, @EndDate, @KlientiIdOsiguritel", connection))
                {
                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    cmd.Parameters.AddWithValue("@KlientiIdOsiguritel", (object)osiguritelId ?? DBNull.Value);
                    
                    System.Diagnostics.Debug.WriteLine("LoadDataFromStoredProcedureAsync: Executing stored procedure");
                    
                    using (var reader = await cmd.ExecuteReaderAsync())
                    {
                        // Get the column names from the result set
                        var columnNames = new List<string>();
                        for (int colIndex = 0; colIndex < reader.FieldCount; colIndex++)
                        {
                            columnNames.Add(reader.GetName(colIndex));
                        }
                        
                        System.Diagnostics.Debug.WriteLine($"LoadDataFromStoredProcedureAsync: Found {columnNames.Count} columns: {string.Join(", ", columnNames)}");
                        
                        // Store column names for debugging
                        DebugColumnNames = columnNames;
                        
                        // Read all rows and aggregate the data
                        int rowCount = 0;
                        bool dataFound = false;
                        
                        while (await reader.ReadAsync())
                        {
                            rowCount++;
                            System.Diagnostics.Debug.WriteLine($"LoadDataFromStoredProcedureAsync: Processing row {rowCount}");
                            
                            // Helper function to safely get column value
                            object GetColumnValue(string columnName)
                            {
                                try
                                {
                                    if (columnNames.Contains(columnName))
                                    {
                                        var value = reader[columnName];
                                        System.Diagnostics.Debug.WriteLine($"Column {columnName}: {value}");
                                        return value;
                                    }
                                    else
                                    {
                                        System.Diagnostics.Debug.WriteLine($"Column {columnName} not found in available columns: {string.Join(", ", columnNames)}");
                                        return DBNull.Value;
                                    }
                                }
                                catch (Exception ex)
                                {
                                    System.Diagnostics.Debug.WriteLine($"Error reading column {columnName}: {ex.Message}");
                                    return DBNull.Value;
                                }
                            }
                            
                            // Map the stored procedure results to our rows using the actual column names from ASO_OBD1
                            // The stored procedure returns individual class data first, then totals
                            
                            // Map BrojDogovori (contract counts) - first 26 columns
                            if (rows.Count >= 26)
                            {
                                // Map individual class contract counts
                                rows[0].BrojDogovori = GetColumnValue("Осигурување од незгода") as int?;
                                rows[1].BrojDogovori = GetColumnValue("Здравствено осигурување") as int?;
                                rows[2].BrojDogovori = GetColumnValue("Осигурување на патнички возила - КАСКО") as int?;
                                rows[3].BrojDogovori = GetColumnValue("Осигурување на шински возила-КАСКО") as int?;
                                rows[4].BrojDogovori = GetColumnValue("Осигурување на воздухоплови - КАСКО") as int?;
                                rows[5].BrojDogovori = GetColumnValue("Осигурување на пловни објекти - КАСКО") as int?;
                                rows[6].BrojDogovori = GetColumnValue("Осигурување на стока во превоз") as int?;
                                rows[7].BrojDogovori = GetColumnValue("Осигурување на имот во пожар и некои други опасности") as int?;
                                rows[8].BrojDogovori = GetColumnValue("Останати осигурувања на имоти") as int?;
                                rows[9].BrojDogovori = GetColumnValue("Осигурување од автомобилска одговорност") as int?;
                                rows[10].BrojDogovori = GetColumnValue("Осигурување од одговорност од употреба на воздухоплови") as int?;
                                rows[11].BrojDogovori = GetColumnValue("Осигурување од одговорност од употреба на пловни објекти") as int?;
                                rows[12].BrojDogovori = GetColumnValue("Останати осигурувања од одговорност") as int?;
                                rows[13].BrojDogovori = GetColumnValue("Осигурување на кредити") as int?;
                                rows[14].BrojDogovori = GetColumnValue("Осигурување на гаранции") as int?;
                                rows[15].BrojDogovori = GetColumnValue("Осигурување од финансиски загуби") as int?;
                                rows[16].BrojDogovori = GetColumnValue("Осигурување на правна заштита") as int?;
                                rows[17].BrojDogovori = GetColumnValue("Осигурување на туристички услуги") as int?;
                                rows[18].BrojDogovori = GetColumnValue("Осигурување на живот") as int?;
                                rows[19].BrojDogovori = GetColumnValue("основно") as int?;
                                rows[20].BrojDogovori = GetColumnValue("дополнително") as int?;
                                rows[21].BrojDogovori = GetColumnValue("рентно") as int?;
                                rows[22].BrojDogovori = GetColumnValue("Осигурување на брак или породување") as int?;
                                rows[23].BrojDogovori = GetColumnValue("Осигурување на живот во врска со удели во инвест. фондови") as int?;
                                rows[24].BrojDogovori = GetColumnValue("Осигурување на тонтина") as int?;
                                rows[25].BrojDogovori = GetColumnValue("Осигурување на средства за капитал") as int?;
                                
                                        dataFound = true;
                            }
                            
                            // Map Premija (premiums) - columns after the contract counts
                            if (rows.Count >= 26)
                            {
                                rows[0].Premija = GetColumnValue("Премија - Осигурување од незгода") as decimal?;
                                rows[1].Premija = GetColumnValue("Премија - Здравствено осигурување") as decimal?;
                                rows[2].Premija = GetColumnValue("Премија - Осигурување на патнички возила - КАСКО") as decimal?;
                                rows[3].Premija = GetColumnValue("Премија - Осигурување на шински возила-КАСКО") as decimal?;
                                rows[4].Premija = GetColumnValue("Премија - Осигурување на воздухоплови - КАСКО") as decimal?;
                                rows[5].Premija = GetColumnValue("Премија - Осигурување на пловни објекти - КАСКО") as decimal?;
                                rows[6].Premija = GetColumnValue("Премија - Осигурување на стока во превоз") as decimal?;
                                rows[7].Premija = GetColumnValue("Премија - Осигурување на имот во пожар и некои други опасности") as decimal?;
                                rows[8].Premija = GetColumnValue("Премија - Останати осигурувања на имоти") as decimal?;
                                rows[9].Premija = GetColumnValue("Премија - Осигурување од автомобилска одговорност") as decimal?;
                                rows[10].Premija = GetColumnValue("Премија - Осигурување од одговорност од употреба на воздухоплови") as decimal?;
                                rows[11].Premija = GetColumnValue("Премија - Осигурување од одговорност од употреба на пловни објекти") as decimal?;
                                rows[12].Premija = GetColumnValue("Премија - Останати осигурувања од одговорност") as decimal?;
                                rows[13].Premija = GetColumnValue("Премија - Осигурување на кредити") as decimal?;
                                rows[14].Premija = GetColumnValue("Премија - Осигурување на гаранции") as decimal?;
                                rows[15].Premija = GetColumnValue("Премија - Осигурување од финансиски загуби") as decimal?;
                                rows[16].Premija = GetColumnValue("Премија - Осигурување на правна заштита") as decimal?;
                                rows[17].Premija = GetColumnValue("Премија - Осигурување на туристички услуги") as decimal?;
                                rows[18].Premija = GetColumnValue("Премија - Осигурување на живот") as decimal?;
                                rows[19].Premija = GetColumnValue("Премија - основно") as decimal?;
                                rows[20].Premija = GetColumnValue("Премија - дополнително") as decimal?;
                                rows[21].Premija = GetColumnValue("Премија - рентно") as decimal?;
                                rows[22].Premija = GetColumnValue("Премија - Осигурување на брак или породување") as decimal?;
                                rows[23].Premija = GetColumnValue("Премија - Осигурување на живот во врска со удели во инвест. фондови") as decimal?;
                                rows[24].Premija = GetColumnValue("Премија - Осигурување на тонтина") as decimal?;
                                rows[25].Premija = GetColumnValue("Премија - Осигурување на средства за капитал") as decimal?;
                                
                                // Store original decimal values before any rounding and divide by 1000
                                for (int i = 0; i < rows.Count; i++)
                                {
                                    if (rows[i].Premija.HasValue)
                                    {
                                        rows[i].OriginalPremija = rows[i].Premija.Value / 1000m;
                                        rows[i].Premija = rows[i].Premija.Value / 1000m;
                                    }
                                }
                                
                                dataFound = true;
                            }
                            
                            // Map PresmetanaProvizija (calculated commissions) - columns after premiums
                            if (rows.Count >= 26)
                            {
                                rows[0].PresmetanaProvizija = GetColumnValue("Пресметана провизија - Осигурување од незгода") as decimal?;
                                rows[1].PresmetanaProvizija = GetColumnValue("Пресметана провизија - Здравствено осигурување") as decimal?;
                                rows[2].PresmetanaProvizija = GetColumnValue("Пресметана провизија - Осигурување на патнички возила - КАСКО") as decimal?;
                                rows[3].PresmetanaProvizija = GetColumnValue("Пресметана провизија - Осигурување на шински возила-КАСКО") as decimal?;
                                rows[4].PresmetanaProvizija = GetColumnValue("Пресметана провизија - Осигурување на воздухоплови - КАСКО") as decimal?;
                                rows[5].PresmetanaProvizija = GetColumnValue("Пресметана провизија - Осигурување на пловни објекти - КАСКО") as decimal?;
                                rows[6].PresmetanaProvizija = GetColumnValue("Пресметана провизија - Осигурување на стока во превоз") as decimal?;
                                rows[7].PresmetanaProvizija = GetColumnValue("Пресметана провизија - Осигурување на имот во пожар и некои други опасности") as decimal?;
                                rows[8].PresmetanaProvizija = GetColumnValue("Пресметана провизија - Останати осигурувања на имоти") as decimal?;
                                rows[9].PresmetanaProvizija = GetColumnValue("Пресметана провизија - Осигурување од автомобилска одговорност") as decimal?;
                                rows[10].PresmetanaProvizija = GetColumnValue("Пресметана провизија - Осигурување од одговорност од употреба на воздухоплови") as decimal?;
                                rows[11].PresmetanaProvizija = GetColumnValue("Пресметана провизија - Осигурување од одговорност од употреба на пловни објекти") as decimal?;
                                rows[12].PresmetanaProvizija = GetColumnValue("Пресметана провизија - Останати осигурувања од одговорност") as decimal?;
                                rows[13].PresmetanaProvizija = GetColumnValue("Пресметана провизија - Осигурување на кредити") as decimal?;
                                rows[14].PresmetanaProvizija = GetColumnValue("Пресметана провизија - Осигурување на гаранции") as decimal?;
                                rows[15].PresmetanaProvizija = GetColumnValue("Пресметана провизија - Осигурување од финансиски загуби") as decimal?;
                                rows[16].PresmetanaProvizija = GetColumnValue("Пресметана провизија - Осигурување на правна заштита") as decimal?;
                                rows[17].PresmetanaProvizija = GetColumnValue("Пресметана провизија - Осигурување на туристички услуги") as decimal?;
                                rows[18].PresmetanaProvizija = GetColumnValue("Пресметана провизија - Осигурување на живот") as decimal?;
                                rows[19].PresmetanaProvizija = GetColumnValue("Пресметана провизија - основно") as decimal?;
                                rows[20].PresmetanaProvizija = GetColumnValue("Пресметана провизија - дополнително") as decimal?;
                                rows[21].PresmetanaProvizija = GetColumnValue("Пресметана провизија - рентно") as decimal?;
                                rows[22].PresmetanaProvizija = GetColumnValue("Пресметана провизија - Осигурување на брак или породување") as decimal?;
                                rows[23].PresmetanaProvizija = GetColumnValue("Пресметана провизија - Осигурување на живот во врска со удели во инвест. фондови") as decimal?;
                                rows[24].PresmetanaProvizija = GetColumnValue("Пресметана провизија - Осигурување на тонтина") as decimal?;
                                rows[25].PresmetanaProvizija = GetColumnValue("Пресметана провизија - Осигурување на средства за капитал") as decimal?;
                                
                                // Store original decimal values before any rounding and divide by 1000
                                for (int i = 0; i < rows.Count; i++)
                                {
                                    if (rows[i].PresmetanaProvizija.HasValue)
                                    {
                                        rows[i].OriginalPresmetanaProvizija = rows[i].PresmetanaProvizija.Value / 1000m;
                                        rows[i].PresmetanaProvizija = rows[i].PresmetanaProvizija.Value / 1000m;
                                    }
                                }
                                
                                dataFound = true;
                            }
                            
                            // Map RealiziranaProvizija (realized commissions) - columns after calculated commissions
                            if (rows.Count >= 26)
                            {
                                System.Diagnostics.Debug.WriteLine("LoadDataFromStoredProcedureAsync: Mapping realized commission values");
                                
                                rows[0].RealiziranaProvizija = GetColumnValue("Реализирана провизија - Осигурување од незгода") as decimal?;
                                System.Diagnostics.Debug.WriteLine($"LoadDataFromStoredProcedureAsync: Row 0 RealiziranaProvizija = {rows[0].RealiziranaProvizija}");
                                
                                rows[1].RealiziranaProvizija = GetColumnValue("Реализирана провизија - Здравствено осигурување") as decimal?;
                                System.Diagnostics.Debug.WriteLine($"LoadDataFromStoredProcedureAsync: Row 1 RealiziranaProvizija = {rows[1].RealiziranaProvizija}");
                                
                                rows[2].RealiziranaProvizija = GetColumnValue("Реализирана провизија - Осигурување на патнички возила - КАСКО") as decimal?;
                                System.Diagnostics.Debug.WriteLine($"LoadDataFromStoredProcedureAsync: Row 2 RealiziranaProvizija = {rows[2].RealiziranaProvizija}");
                                
                                rows[3].RealiziranaProvizija = GetColumnValue("Реализирана провизија - Осигурување на шински возила-КАСКО") as decimal?;
                                rows[4].RealiziranaProvizija = GetColumnValue("Реализирана провизија - Осигурување на воздухоплови - КАСКО") as decimal?;
                                rows[5].RealiziranaProvizija = GetColumnValue("Реализирана провизија - Осигурување на пловни објекти - КАСКО") as decimal?;
                                rows[6].RealiziranaProvizija = GetColumnValue("Реализирана провизија - Осигурување на стока во превоз") as decimal?;
                                rows[7].RealiziranaProvizija = GetColumnValue("Реализирана провизија - Осигурување на имот во пожар и некои други опасности") as decimal?;
                                rows[8].RealiziranaProvizija = GetColumnValue("Реализирана провизија - Останати осигурувања на имоти") as decimal?;
                                rows[9].RealiziranaProvizija = GetColumnValue("Реализирана провизија - Осигурување од автомобилска одговорност") as decimal?;
                                rows[10].RealiziranaProvizija = GetColumnValue("Реализирана провизија - Осигурување од одговорност од употреба на воздухоплови") as decimal?;
                                rows[11].RealiziranaProvizija = GetColumnValue("Реализирана провизија - Осигурување од одговорност од употреба на пловни објекти") as decimal?;
                                rows[12].RealiziranaProvizija = GetColumnValue("Реализирана провизија - Останати осигурувања од одговорност") as decimal?;
                                rows[13].RealiziranaProvizija = GetColumnValue("Реализирана провизија - Осигурување на кредити") as decimal?;
                                rows[14].RealiziranaProvizija = GetColumnValue("Реализирана провизија - Осигурување на гаранции") as decimal?;
                                rows[15].RealiziranaProvizija = GetColumnValue("Реализирана провизија - Осигурување од финансиски загуби") as decimal?;
                                rows[16].RealiziranaProvizija = GetColumnValue("Реализирана провизија - Осигурување на правна заштита") as decimal?;
                                rows[17].RealiziranaProvizija = GetColumnValue("Реализирана провизија - Осигурување на туристички услуги") as decimal?;
                                rows[18].RealiziranaProvizija = GetColumnValue("Реализирана провизија - Осигурување на живот") as decimal?;
                                rows[19].RealiziranaProvizija = GetColumnValue("Реализирана провизија - основно") as decimal?;
                                rows[20].RealiziranaProvizija = GetColumnValue("Реализирана провизија - дополнително") as decimal?;
                                rows[21].RealiziranaProvizija = GetColumnValue("Реализирана провизија - рентно") as decimal?;
                                rows[22].RealiziranaProvizija = GetColumnValue("Реализирана провизија - Осигурување на брак или породување") as decimal?;
                                rows[23].RealiziranaProvizija = GetColumnValue("Реализирана провизија - Осигурување на живот во врска со удели во инвест. фондови") as decimal?;
                                rows[24].RealiziranaProvizija = GetColumnValue("Реализирана провизија - Осигурување на тонтина") as decimal?;
                                rows[25].RealiziranaProvizija = GetColumnValue("Реализирана провизија - Осигурување на средства за капитал") as decimal?;
                                
                                // Store original decimal values before any rounding and divide by 1000
                                for (int i = 0; i < rows.Count; i++)
                                {
                                    if (rows[i].RealiziranaProvizija.HasValue)
                                    {
                                        rows[i].OriginalRealiziranaProvizija = rows[i].RealiziranaProvizija.Value;
                                        rows[i].RealiziranaProvizija = rows[i].RealiziranaProvizija.Value / 1000m;
                                    }
                                }
                                

                                
                                dataFound = true;
                            }
                        }
                        
                        System.Diagnostics.Debug.WriteLine($"LoadDataFromStoredProcedureAsync: Processed {rowCount} rows from stored procedure, dataFound: {dataFound}");
                        
                        // If no rows were returned at all, log this information
                        if (rowCount == 0)
                        {
                            System.Diagnostics.Debug.WriteLine($"LoadDataFromStoredProcedureAsync: No rows returned from stored procedure for date range {startDate} to {endDate}");
                            System.Diagnostics.Debug.WriteLine($"LoadDataFromStoredProcedureAsync: This might indicate no data exists for the selected quarter/year");
                        }
                        
                        // If no data was found, try alternative column mapping
                        if (!dataFound && rowCount > 0)
                        {
                            System.Diagnostics.Debug.WriteLine("LoadDataFromStoredProcedureAsync: No data found with standard mapping, trying alternative approach");
                            
                            // Reset reader to beginning
                            reader.Close();
                            using (var cmd2 = new SqlCommand("EXEC dbo.ASO_KVARTALEN_OBD1_Display_PER_OSIGURITEL @StartDate, @EndDate, @KlientiIdOsiguritel", connection))
                            {
                                cmd2.Parameters.AddWithValue("@StartDate", startDate);
                                cmd2.Parameters.AddWithValue("@EndDate", endDate);
                                cmd2.Parameters.AddWithValue("@KlientiIdOsiguritel", (object)osiguritelId ?? DBNull.Value);
                                
                                using (var reader2 = await cmd2.ExecuteReaderAsync())
                                {
                                    if (await reader2.ReadAsync())
                                    {
                                        // Try to map by column content rather than position
                                        for (int i = 0; i < Math.Min(26, rows.Count); i++)
                                        {
                                            // Try to find columns that might contain our data
                                            for (int colIndex = 0; colIndex < reader2.FieldCount; colIndex++)
                                            {
                                                var columnName = reader2.GetName(colIndex);
                                                var value = reader2[colIndex];
                                                
                                                if (value != DBNull.Value)
                                                {
                                                    // Try to determine the type of data and map accordingly
                                                    if (colIndex < 26 && rows[i].BrojDogovori == null)
                                                    {
                                                        if (int.TryParse(value.ToString(), out int intValue))
                                                        {
                                                            rows[i].BrojDogovori = intValue;
                                                            System.Diagnostics.Debug.WriteLine($"Alternative mapping: BrojDogovori for row {i} from column {columnName}: {intValue}");
                                                            break;
                                                        }
                                                    }
                                                    else if (colIndex >= 26 && colIndex < 52 && rows[i].Premija == null)
                                                    {
                                                        if (decimal.TryParse(value.ToString(), out decimal decimalValue))
                                                        {
                                                            rows[i].Premija = decimalValue;
                                                            System.Diagnostics.Debug.WriteLine($"Alternative mapping: Premija for row {i} from column {columnName}: {decimalValue}");
                                                            break;
                                                        }
                                                    }
                                                    else if (colIndex >= 52 && colIndex < 78 && rows[i].PresmetanaProvizija == null)
                                                    {
                                                        if (decimal.TryParse(value.ToString(), out decimal decimalValue))
                                                        {
                                                            rows[i].PresmetanaProvizija = decimalValue;
                                                            System.Diagnostics.Debug.WriteLine($"Alternative mapping: PresmetanaProvizija for row {i} from column {columnName}: {decimalValue}");
                                                            break;
                                                        }
                                                    }
                                                    else if (colIndex >= 78 && colIndex < 104 && rows[i].RealiziranaProvizija == null)
                                                    {
                                                        if (decimal.TryParse(value.ToString(), out decimal decimalValue))
                                                        {
                                                            rows[i].RealiziranaProvizija = decimalValue;
                                                            System.Diagnostics.Debug.WriteLine($"Alternative mapping: RealiziranaProvizija for row {i} from column {columnName}: {decimalValue}");
                                                            break;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        
                        // If still no data found, initialize with zeros for testing
                        if (!dataFound)
                        {
                            System.Diagnostics.Debug.WriteLine("LoadDataFromStoredProcedureAsync: Still no data found, initializing with test values");
                            for (int i = 0; i < Math.Min(5, rows.Count); i++)
                            {
                                rows[i].BrojDogovori = 0;
                                rows[i].Premija = 0;
                                rows[i].PresmetanaProvizija = 0;
                                rows[i].RealiziranaProvizija = 0;
                            }
                        }
                    }
                }
            }
            
            System.Diagnostics.Debug.WriteLine($"LoadDataFromStoredProcedureAsync: Returning {rows.Count} rows");
            foreach (var row in rows)
            {
                System.Diagnostics.Debug.WriteLine($"Row {row.Code}: BrojDogovori={row.BrojDogovori}, Premija={row.Premija}, PresmetanaProvizija={row.PresmetanaProvizija}, RealiziranaProvizija={row.RealiziranaProvizija}");
            }
            
            // Log totals for debugging
            var totalBrojDogovori = rows.Sum(r => r.BrojDogovori ?? 0);
            var totalPremija = rows.Sum(r => r.Premija ?? 0);
            var totalPresmetanaProvizija = rows.Sum(r => r.PresmetanaProvizija ?? 0);
            var totalRealiziranaProvizija = rows.Sum(r => r.RealiziranaProvizija ?? 0);
            
            System.Diagnostics.Debug.WriteLine($"LoadDataFromStoredProcedureAsync: Calculated totals - BrojDogovori: {totalBrojDogovori}, Premija: {totalPremija}, PresmetanaProvizija: {totalPresmetanaProvizija}, RealiziranaProvizija: {totalRealiziranaProvizija}");
            
            // Original values are now set immediately when data is loaded
            
            return rows;
        }

        private async Task SaveToArchiveAsync(DateTime startDate, DateTime endDate, bool isPreview)
        {
            System.Diagnostics.Debug.WriteLine($"SaveToArchiveAsync: Saving data for {startDate} to {endDate}, isPreview={isPreview}");
            
            if (TableRows == null || TableRows.Count == 0)
            {
                System.Diagnostics.Debug.WriteLine("SaveToArchiveAsync: No data to save");
                return;
            }

            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                using (var cmd = new SqlCommand(@"INSERT INTO ASOKvartalenIzvestajArhiva
(DateCreated, UsernameCreated, StartDate, EndDate,
OsiguruvanjeOdNezgoda, ZdravstvenoOsiguruvanje, OsiguruvanjeNaPatnichkiVozilaKasko, OsiguruvanjeNaShinskiVozilaKasko, OsiguruvanjeNaVozduhoploviKasko, OsiguruvanjeNaPlovniObjektiKasko, OsiguruvanjeNaStokaVoPrevoz, OsiguruvanjeNaImotVoPozhar, OstanatiOsiguruvanjaNaImoti, OsiguruvanjeOdAvtomobilskaOdgovornost, OsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi, OsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti, OstanatiOsiguruvanjaOdOdgovornost, OsiguruvanjeNaKrediti, OsiguruvanjeNaGarantii, OsiguruvanjeOdFinansiskiZagubi, OsiguruvanjeNaPravnaZashtita, OsiguruvanjeNaTuristichkiUslugi, OsiguruvanjeNaZhivot, Osnovno, Dopolnitelno, Rentno, OsiguruvanjeNaBrakIliPoroduvanje, OsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi, OsiguruvanjeNaTontina, OsiguruvanjeNaSredstvaZaKapital,
PremijaOsiguruvanjeOdNezgoda, PremijaZdravstvenoOsiguruvanje, PremijaOsiguruvanjeNaPatnichkiVozilaKasko, PremijaOsiguruvanjeNaShinskiVozilaKasko, PremijaOsiguruvanjeNaVozduhoploviKasko, PremijaOsiguruvanjeNaPlovniObjektiKasko, PremijaOsiguruvanjeNaStokaVoPrevoz, PremijaOsiguruvanjeNaImotVoPozhar, PremijaOstanatiOsiguruvanjaNaImoti, PremijaOsiguruvanjeOdAvtomobilskaOdgovornost, PremijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi, PremijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti, PremijaOstanatiOsiguruvanjaOdOdgovornost, PremijaOsiguruvanjeNaKrediti, PremijaOsiguruvanjeNaGarantii, PremijaOsiguruvanjeOdFinansiskiZagubi, PremijaOsiguruvanjeNaPravnaZashtita, PremijaOsiguruvanjeNaTuristichkiUslugi, PremijaOsiguruvanjeNaZhivot, PremijaOsnovno, PremijaDopolnitelno, PremijaRentno, PremijaOsiguruvanjeNaBrakIliPoroduvanje, PremijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi, PremijaOsiguruvanjeNaTontina, PremijaOsiguruvanjeNaSredstvaZaKapital,
PresmetanaProvizijaOsiguruvanjeOdNezgoda, PresmetanaProvizijaZdravstvenoOsiguruvanje, PresmetanaProvizijaOsiguruvanjeNaPatnichkiVozilaKasko, PresmetanaProvizijaOsiguruvanjeNaShinskiVozilaKasko, PresmetanaProvizijaOsiguruvanjeNaVozduhoploviKasko, PresmetanaProvizijaOsiguruvanjeNaPlovniObjektiKasko, PresmetanaProvizijaOsiguruvanjeNaStokaVoPrevoz, PresmetanaProvizijaOsiguruvanjeNaImotVoPozhar, PresmetanaProvizijaOstanatiOsiguruvanjaNaImoti, PresmetanaProvizijaOsiguruvanjeOdAvtomobilskaOdgovornost, PresmetanaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi, PresmetanaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti, PresmetanaProvizijaOstanatiOsiguruvanjaOdOdgovornost, PresmetanaProvizijaOsiguruvanjeNaKrediti, PresmetanaProvizijaOsiguruvanjeNaGarantii, PresmetanaProvizijaOsiguruvanjeOdFinansiskiZagubi, PresmetanaProvizijaOsiguruvanjeNaPravnaZashtita, PresmetanaProvizijaOsiguruvanjeNaTuristichkiUslugi, PresmetanaProvizijaOsiguruvanjeNaZhivot, PresmetanaProvizijaOsnovno, PresmetanaProvizijaDopolnitelno, PresmetanaProvizijaRentno, PresmetanaProvizijaOsiguruvanjeNaBrakIliPoroduvanje, PresmetanaProvizijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi, PresmetanaProvizijaOsiguruvanjeNaTontina, PresmetanaProvizijaOsiguruvanjeNaSredstvaZaKapital,
RealiziranaProvizijaOsiguruvanjeOdNezgoda, RealiziranaProvizijaZdravstvenoOsiguruvanje, RealiziranaProvizijaOsiguruvanjeNaPatnichkiVozilaKasko, RealiziranaProvizijaOsiguruvanjeNaShinskiVozilaKasko, RealiziranaProvizijaOsiguruvanjeNaVozduhoploviKasko, RealiziranaProvizijaOsiguruvanjeNaPlovniObjektiKasko, RealiziranaProvizijaOsiguruvanjeNaStokaVoPrevoz, RealiziranaProvizijaOsiguruvanjeNaImotVoPozhar, RealiziranaProvizijaOstanatiOsiguruvanjaNaImoti, RealiziranaProvizijaOsiguruvanjeOdAvtomobilskaOdgovornost, RealiziranaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi, RealiziranaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti, RealiziranaProvizijaOstanatiOsiguruvanjaOdOdgovornost, RealiziranaProvizijaOsiguruvanjeNaKrediti, RealiziranaProvizijaOsiguruvanjeNaGarantii, RealiziranaProvizijaOsiguruvanjeOdFinansiskiZagubi, RealiziranaProvizijaOsiguruvanjeNaPravnaZashtita, RealiziranaProvizijaOsiguruvanjeNaTuristichkiUslugi, RealiziranaProvizijaOsiguruvanjeNaZhivot, RealiziranaProvizijaOsnovno, RealiziranaProvizijaDopolnitelno, RealiziranaProvizijaRentno, RealiziranaProvizijaOsiguruvanjeNaBrakIliPoroduvanje, RealiziranaProvizijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi, RealiziranaProvizijaOsiguruvanjeNaTontina, RealiziranaProvizijaOsiguruvanjeNaSredstvaZaKapital, VkupenBrojNaDogovori, VkupnaPremija, VkupnaProvizija, VkupnaRealiziranaProvizija)
VALUES
(GETDATE(), @UsernameCreated, @StartDate, @EndDate,
@OsiguruvanjeOdNezgoda, @ZdravstvenoOsiguruvanje, @OsiguruvanjeNaPatnichkiVozilaKasko, @OsiguruvanjeNaShinskiVozilaKasko, @OsiguruvanjeNaVozduhoploviKasko, @OsiguruvanjeNaPlovniObjektiKasko, @OsiguruvanjeNaStokaVoPrevoz, @OsiguruvanjeNaImotVoPozhar, @OstanatiOsiguruvanjaNaImoti, @OsiguruvanjeOdAvtomobilskaOdgovornost, @OsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi, @OsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti, @OstanatiOsiguruvanjaOdOdgovornost, @OsiguruvanjeNaKrediti, @OsiguruvanjeNaGarantii, @OsiguruvanjeOdFinansiskiZagubi, @OsiguruvanjeNaPravnaZashtita, @OsiguruvanjeNaTuristichkiUslugi, @OsiguruvanjeNaZhivot, @Osnovno, @Dopolnitelno, @Rentno, @OsiguruvanjeNaBrakIliPoroduvanje, @OsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi, @OsiguruvanjeNaTontina, @OsiguruvanjeNaSredstvaZaKapital,
@PremijaOsiguruvanjeOdNezgoda, @PremijaZdravstvenoOsiguruvanje, @PremijaOsiguruvanjeNaPatnichkiVozilaKasko, @PremijaOsiguruvanjeNaShinskiVozilaKasko, @PremijaOsiguruvanjeNaVozduhoploviKasko, @PremijaOsiguruvanjeNaPlovniObjektiKasko, @PremijaOsiguruvanjeNaStokaVoPrevoz, @PremijaOsiguruvanjeNaImotVoPozhar, @PremijaOstanatiOsiguruvanjaNaImoti, @PremijaOsiguruvanjeOdAvtomobilskaOdgovornost, @PremijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi, @PremijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti, @PremijaOstanatiOsiguruvanjaOdOdgovornost, @PremijaOsiguruvanjeNaKrediti, @PremijaOsiguruvanjeNaGarantii, @PremijaOsiguruvanjeOdFinansiskiZagubi, @PremijaOsiguruvanjeNaPravnaZashtita, @PremijaOsiguruvanjeNaTuristichkiUslugi, @PremijaOsiguruvanjeNaZhivot, @PremijaOsnovno, @PremijaDopolnitelno, @PremijaRentno, @PremijaOsiguruvanjeNaBrakIliPoroduvanje, @PremijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi, @PremijaOsiguruvanjeNaTontina, @PremijaOsiguruvanjeNaSredstvaZaKapital,
@PresmetanaProvizijaOsiguruvanjeOdNezgoda, @PresmetanaProvizijaZdravstvenoOsiguruvanje, @PresmetanaProvizijaOsiguruvanjeNaPatnichkiVozilaKasko, @PresmetanaProvizijaOsiguruvanjeNaShinskiVozilaKasko, @PresmetanaProvizijaOsiguruvanjeNaVozduhoploviKasko, @PresmetanaProvizijaOsiguruvanjeNaPlovniObjektiKasko, @PresmetanaProvizijaOsiguruvanjeNaStokaVoPrevoz, @PresmetanaProvizijaOsiguruvanjeNaImotVoPozhar, @PresmetanaProvizijaOstanatiOsiguruvanjaNaImoti, @PresmetanaProvizijaOsiguruvanjeOdAvtomobilskaOdgovornost, @PresmetanaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi, @PresmetanaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti, @PresmetanaProvizijaOstanatiOsiguruvanjaOdOdgovornost, @PresmetanaProvizijaOsiguruvanjeNaKrediti, @PresmetanaProvizijaOsiguruvanjeNaGarantii, @PresmetanaProvizijaOsiguruvanjeOdFinansiskiZagubi, @PresmetanaProvizijaOsiguruvanjeNaPravnaZashtita, @PresmetanaProvizijaOsiguruvanjeNaTuristichkiUslugi, @PresmetanaProvizijaOsiguruvanjeNaZhivot, @PresmetanaProvizijaOsnovno, @PresmetanaProvizijaDopolnitelno, @PresmetanaProvizijaRentno, @PresmetanaProvizijaOsiguruvanjeNaBrakIliPoroduvanje, @PresmetanaProvizijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi, @PresmetanaProvizijaOsiguruvanjeNaTontina, @PresmetanaProvizijaOsiguruvanjeNaSredstvaZaKapital,
@RealiziranaProvizijaOsiguruvanjeOdNezgoda, @RealiziranaProvizijaZdravstvenoOsiguruvanje, @RealiziranaProvizijaOsiguruvanjeNaPatnichkiVozilaKasko, @RealiziranaProvizijaOsiguruvanjeNaShinskiVozilaKasko, @RealiziranaProvizijaOsiguruvanjeNaVozduhoploviKasko, @RealiziranaProvizijaOsiguruvanjeNaPlovniObjektiKasko, @RealiziranaProvizijaOsiguruvanjeNaStokaVoPrevoz, @RealiziranaProvizijaOsiguruvanjeNaImotVoPozhar, @RealiziranaProvizijaOstanatiOsiguruvanjaNaImoti, @RealiziranaProvizijaOsiguruvanjeOdAvtomobilskaOdgovornost, @RealiziranaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi, @RealiziranaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti, @RealiziranaProvizijaOstanatiOsiguruvanjaOdOdgovornost, @RealiziranaProvizijaOsiguruvanjeNaKrediti, @RealiziranaProvizijaOsiguruvanjeNaGarantii, @RealiziranaProvizijaOsiguruvanjeOdFinansiskiZagubi, @RealiziranaProvizijaOsiguruvanjeNaPravnaZashtita, @RealiziranaProvizijaOsiguruvanjeNaTuristichkiUslugi, @RealiziranaProvizijaOsiguruvanjeNaZhivot, @RealiziranaProvizijaOsnovno, @RealiziranaProvizijaDopolnitelno, @RealiziranaProvizijaRentno, @RealiziranaProvizijaOsiguruvanjeNaBrakIliPoroduvanje, @RealiziranaProvizijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi, @RealiziranaProvizijaOsiguruvanjeNaTontina, @RealiziranaProvizijaOsiguruvanjeNaSredstvaZaKapital, @VkupenBrojNaDogovori, @VkupnaPremija, @VkupnaProvizija, @VkupnaRealiziranaProvizija)", connection))
                {
                    string username = isPreview ? GetPreviewMarker() : GetFinalMarker();
                    System.Diagnostics.Debug.WriteLine($"SaveToArchiveAsync: Username = {username}");
                    
                    cmd.Parameters.AddWithValue("@UsernameCreated", username);
                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);

                    // Add all the parameters for the data
                    cmd.Parameters.AddWithValue("@OsiguruvanjeOdNezgoda", TableRows[0].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@ZdravstvenoOsiguruvanje", TableRows[1].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaPatnichkiVozilaKasko", TableRows[2].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaShinskiVozilaKasko", TableRows[3].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaVozduhoploviKasko", TableRows[4].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaPlovniObjektiKasko", TableRows[5].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaStokaVoPrevoz", TableRows[6].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaImotVoPozhar", TableRows[7].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OstanatiOsiguruvanjaNaImoti", TableRows[8].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeOdAvtomobilskaOdgovornost", TableRows[9].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi", TableRows[10].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti", TableRows[11].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OstanatiOsiguruvanjaOdOdgovornost", TableRows[12].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaKrediti", TableRows[13].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaGarantii", TableRows[14].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeOdFinansiskiZagubi", TableRows[15].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaPravnaZashtita", TableRows[16].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaTuristichkiUslugi", TableRows[17].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaZhivot", TableRows[18].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@Osnovno", TableRows[19].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@Dopolnitelno", TableRows[20].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@Rentno", TableRows[21].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaBrakIliPoroduvanje", TableRows[22].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi", TableRows[23].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaTontina", TableRows[24].BrojDogovori ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsiguruvanjeNaSredstvaZaKapital", TableRows[25].BrojDogovori ?? (object)DBNull.Value);

                    // Add all the Premija parameters
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeOdNezgoda", TableRows[0].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaZdravstvenoOsiguruvanje", TableRows[1].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaPatnichkiVozilaKasko", TableRows[2].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaShinskiVozilaKasko", TableRows[3].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaVozduhoploviKasko", TableRows[4].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaPlovniObjektiKasko", TableRows[5].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaStokaVoPrevoz", TableRows[6].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaImotVoPozhar", TableRows[7].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOstanatiOsiguruvanjaNaImoti", TableRows[8].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeOdAvtomobilskaOdgovornost", TableRows[9].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi", TableRows[10].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti", TableRows[11].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOstanatiOsiguruvanjaOdOdgovornost", TableRows[12].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaKrediti", TableRows[13].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaGarantii", TableRows[14].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeOdFinansiskiZagubi", TableRows[15].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaPravnaZashtita", TableRows[16].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaTuristichkiUslugi", TableRows[17].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaZhivot", TableRows[18].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsnovno", TableRows[19].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaDopolnitelno", TableRows[20].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaRentno", TableRows[21].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaBrakIliPoroduvanje", TableRows[22].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi", TableRows[23].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaTontina", TableRows[24].Premija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaOsiguruvanjeNaSredstvaZaKapital", TableRows[25].Premija ?? (object)DBNull.Value);

                    // Add all the PresmetanaProvizija parameters
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeOdNezgoda", TableRows[0].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaZdravstvenoOsiguruvanje", TableRows[1].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaPatnichkiVozilaKasko", TableRows[2].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaShinskiVozilaKasko", TableRows[3].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaVozduhoploviKasko", TableRows[4].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaPlovniObjektiKasko", TableRows[5].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaStokaVoPrevoz", TableRows[6].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaImotVoPozhar", TableRows[7].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOstanatiOsiguruvanjaNaImoti", TableRows[8].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeOdAvtomobilskaOdgovornost", TableRows[9].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi", TableRows[10].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti", TableRows[11].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOstanatiOsiguruvanjaOdOdgovornost", TableRows[12].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaKrediti", TableRows[13].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaGarantii", TableRows[14].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeOdFinansiskiZagubi", TableRows[15].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaPravnaZashtita", TableRows[16].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaTuristichkiUslugi", TableRows[17].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaZhivot", TableRows[18].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsnovno", TableRows[19].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaDopolnitelno", TableRows[20].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaRentno", TableRows[21].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaBrakIliPoroduvanje", TableRows[22].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi", TableRows[23].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaTontina", TableRows[24].PresmetanaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PresmetanaProvizijaOsiguruvanjeNaSredstvaZaKapital", TableRows[25].PresmetanaProvizija ?? (object)DBNull.Value);

                    // Add all the RealiziranaProvizija parameters
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeOdNezgoda", TableRows[0].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaZdravstvenoOsiguruvanje", TableRows[1].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaPatnichkiVozilaKasko", TableRows[2].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaShinskiVozilaKasko", TableRows[3].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaVozduhoploviKasko", TableRows[4].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaPlovniObjektiKasko", TableRows[5].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaStokaVoPrevoz", TableRows[6].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaImotVoPozhar", TableRows[7].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOstanatiOsiguruvanjaNaImoti", TableRows[8].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeOdAvtomobilskaOdgovornost", TableRows[9].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaVozduhoplovi", TableRows[10].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeOdOdgovornostOdUpotrebaNaPlovniObjekti", TableRows[11].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOstanatiOsiguruvanjaOdOdgovornost", TableRows[12].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaKrediti", TableRows[13].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaGarantii", TableRows[14].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeOdFinansiskiZagubi", TableRows[15].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaPravnaZashtita", TableRows[16].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaTuristichkiUslugi", TableRows[17].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaZhivot", TableRows[18].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsnovno", TableRows[19].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaDopolnitelno", TableRows[20].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaRentno", TableRows[21].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaBrakIliPoroduvanje", TableRows[22].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaZhivotVoVrskaSoUdeliVoInvestFondovi", TableRows[23].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaTontina", TableRows[24].RealiziranaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RealiziranaProvizijaOsiguruvanjeNaSredstvaZaKapital", TableRows[25].RealiziranaProvizija ?? (object)DBNull.Value);

                    // Add VkupenBrojNaDogovori parameter
                    cmd.Parameters.AddWithValue("@VkupenBrojNaDogovori", TableRows.Sum(r => r.BrojDogovori ?? 0));
                    
                    // Add VkupnaPremija parameter
                    cmd.Parameters.AddWithValue("@VkupnaPremija", TableRows.Sum(r => r.Premija ?? 0));
                    
                    // Add VkupnaProvizija parameter
                    cmd.Parameters.AddWithValue("@VkupnaProvizija", TableRows.Sum(r => r.PresmetanaProvizija ?? 0));
                    
                    // Add VkupnaRealiziranaProvizija parameter
                    cmd.Parameters.AddWithValue("@VkupnaRealiziranaProvizija", TableRows.Sum(r => r.RealiziranaProvizija ?? 0));
                    
                    await cmd.ExecuteNonQueryAsync();
                    System.Diagnostics.Debug.WriteLine("SaveToArchiveAsync: Successfully saved to archive table");
                }
            }
        }

        private async Task TestStoredProcedureAsync()
        {
            System.Diagnostics.Debug.WriteLine("TestStoredProcedureAsync: Starting test");
            try
            {
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    System.Diagnostics.Debug.WriteLine("TestStoredProcedureAsync: Connection opened");
                    
                    // Test if the stored procedure exists
                    using (var cmd = new SqlCommand("SELECT COUNT(*) FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_NAME = 'ASO_KVARTALEN_OBD1_Display_PER_OSIGURITEL'", connection))
                    {
                        var count = await cmd.ExecuteScalarAsync();
                        System.Diagnostics.Debug.WriteLine($"TestStoredProcedureAsync: New stored procedure exists: {count}");
                    }
                    
                    // Test calling the stored procedure with simple parameters
                    var testStartDate = new DateTime(2025, 1, 1);
                    var testEndDate = new DateTime(2025, 6, 30);
                    
                    using (var cmd = new SqlCommand("EXEC dbo.ASO_KVARTALEN_OBD1_Display_PER_OSIGURITEL @StartDate, @EndDate, @KlientiIdOsiguritel", connection))
                    {
                        cmd.Parameters.AddWithValue("@StartDate", testStartDate);
                        cmd.Parameters.AddWithValue("@EndDate", testEndDate);
                        cmd.Parameters.AddWithValue("@KlientiIdOsiguritel", DBNull.Value); // Test with NULL for all Osiguriteli
                        
                        System.Diagnostics.Debug.WriteLine($"TestStoredProcedureAsync: Calling stored procedure with {testStartDate} to {testEndDate}");
                        
                        using (var reader = await cmd.ExecuteReaderAsync())
                        {
                            var rowCount = 0;
                            while (await reader.ReadAsync())
                            {
                                rowCount++;
                                if (rowCount == 1)
                                {
                                    // Log the first row's column names
                                    var columnNames = new List<string>();
                                    for (int i = 0; i < reader.FieldCount; i++)
                                    {
                                        columnNames.Add(reader.GetName(i));
                                    }
                                    System.Diagnostics.Debug.WriteLine($"TestStoredProcedureAsync: First row columns: {string.Join(", ", columnNames)}");
                                }
                                
                                // Log first few values from first row
                                if (rowCount == 1)
                                {
                                    for (int i = 0; i < Math.Min(5, reader.FieldCount); i++)
                                    {
                                        var value = reader[i];
                                        System.Diagnostics.Debug.WriteLine($"TestStoredProcedureAsync: Column {i} ({reader.GetName(i)}): {value}");
                                    }
                                }
                            }
                            System.Diagnostics.Debug.WriteLine($"TestStoredProcedureAsync: Total rows returned: {rowCount}");
                        }
                    }
                    
                    // Test with a broader date range to see if any data exists
                    var broadStartDate = new DateTime(2020, 1, 1);
                    var broadEndDate = new DateTime(2025, 12, 31);
                    
                    using (var cmd = new SqlCommand("EXEC dbo.ASO_KVARTALEN_OBD1_Display_PER_OSIGURITEL @StartDate, @EndDate, @KlientiIdOsiguritel", connection))
                    {
                        cmd.Parameters.AddWithValue("@StartDate", broadStartDate);
                        cmd.Parameters.AddWithValue("@EndDate", broadEndDate);
                        cmd.Parameters.AddWithValue("@KlientiIdOsiguritel", DBNull.Value); // Test with NULL for all Osiguriteli
                        
                        System.Diagnostics.Debug.WriteLine($"TestStoredProcedureAsync: Testing with broad date range {broadStartDate} to {broadEndDate}");
                        
                        using (var reader = await cmd.ExecuteReaderAsync())
                        {
                            var rowCount = 0;
                            while (await reader.ReadAsync())
                            {
                                rowCount++;
                            }
                            System.Diagnostics.Debug.WriteLine($"TestStoredProcedureAsync: Broad date range returned {rowCount} rows");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"TestStoredProcedureAsync: Exception: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"TestStoredProcedureAsync: Stack trace: {ex.StackTrace}");
            }
        }

        private void ProcessFormData()
        {
            if (TableRows != null)
            {
                foreach (var row in TableRows)
                {
                    // Save original values before rounding
                    if (row.Premija.HasValue)
                    {
                        row.OriginalPremija = row.Premija.Value;
                        row.Premija = Math.Round(row.Premija.Value, 0, MidpointRounding.AwayFromZero);
                    }
                    if (row.RealiziranaProvizija.HasValue)
                    {
                        row.OriginalRealiziranaProvizija = row.RealiziranaProvizija.Value;
                        row.RealiziranaProvizija = Math.Round(row.RealiziranaProvizija.Value, 0, MidpointRounding.AwayFromZero);
                    }
                    if (row.PresmetanaProvizija.HasValue)
                    {
                        row.OriginalPresmetanaProvizija = row.PresmetanaProvizija.Value;
                        row.PresmetanaProvizija = Math.Round(row.PresmetanaProvizija.Value, 0, MidpointRounding.AwayFromZero);
                    }
                }
            }
        }

        // Test method to verify rounding logic
        private void TestRoundingLogic()
        {
            decimal testValue = 755309.7m;
            var rounded = Math.Round(testValue, 0, MidpointRounding.AwayFromZero);
            System.Diagnostics.Debug.WriteLine($"TestRoundingLogic: {testValue} -> {rounded}, Should be 755310");
            
            // Test other values
            System.Diagnostics.Debug.WriteLine($"1.5 -> {Math.Round(1.5m, 0, MidpointRounding.AwayFromZero)}");
            System.Diagnostics.Debug.WriteLine($"1.4 -> {Math.Round(1.4m, 0, MidpointRounding.AwayFromZero)}");
            System.Diagnostics.Debug.WriteLine($"1.6 -> {Math.Round(1.6m, 0, MidpointRounding.AwayFromZero)}");
        }

        // Manual rounding function as backup
        private decimal RoundHalfUp(decimal value)
        {
            var fractionalPart = value - Math.Floor(value);
            if (fractionalPart >= 0.5m)
            {
                return Math.Ceiling(value);
            }
            else
            {
                return Math.Floor(value);
            }
        }

        // Process current form data for exports
        private void ProcessCurrentFormDataForExport()
        {
            if (TableRows != null)
            {
                foreach (var row in TableRows)
                {
                    // Save original values before any rounding
                    if (row.Premija.HasValue)
                    {
                        row.OriginalPremija = row.Premija.Value;
                    }
                    if (row.RealiziranaProvizija.HasValue)
                    {
                        row.OriginalRealiziranaProvizija = row.RealiziranaProvizija.Value;
                    }
                    if (row.PresmetanaProvizija.HasValue)
                    {
                        row.OriginalPresmetanaProvizija = row.PresmetanaProvizija.Value;
                    }
                }
            }
        }

        // Get current form data from request
        private List<OBD1Row> GetCurrentFormData()
        {
            var formData = new List<OBD1Row>();
            
            // Get form data from request
            var form = HttpContext.Request.Form;
            
            for (int i = 0; i < OBD1Classes.Count; i++)
            {
                var row = new OBD1Row
                {
                    Code = OBD1Classes[i].Code,
                    Name = OBD1Classes[i].Name
                };
                
                // Get values from form
                if (form.ContainsKey($"TableRows[{i}].BrojDogovori"))
                {
                    var brojDogovoriStr = form[$"TableRows[{i}].BrojDogovori"].ToString();
                    if (int.TryParse(brojDogovoriStr, out var brojDogovori))
                    {
                        row.BrojDogovori = brojDogovori;
                    }
                }
                
                if (form.ContainsKey($"TableRows[{i}].Premija"))
                {
                    var premijaStr = form[$"TableRows[{i}].Premija"].ToString();
                    if (decimal.TryParse(premijaStr, out var premija))
                    {
                        row.Premija = premija;
                        row.OriginalPremija = premija; // Save original for totals
                    }
                }
                
                if (form.ContainsKey($"TableRows[{i}].RealiziranaProvizija"))
                {
                    var realiziranaStr = form[$"TableRows[{i}].RealiziranaProvizija"].ToString();
                    if (decimal.TryParse(realiziranaStr, out var realizirana))
                    {
                        row.RealiziranaProvizija = realizirana;
                        row.OriginalRealiziranaProvizija = realizirana; // Save original for totals
                    }
                }
                
                if (form.ContainsKey($"TableRows[{i}].PresmetanaProvizija"))
                {
                    var presmetanaStr = form[$"TableRows[{i}].PresmetanaProvizija"].ToString();
                    if (decimal.TryParse(presmetanaStr, out var presmetana))
                    {
                        row.PresmetanaProvizija = presmetana;
                        row.OriginalPresmetanaProvizija = presmetana; // Save original for totals
                    }
                }
                
                formData.Add(row);
            }
            
            return formData;
        }

        // Calculate totals consistently across all formats
        private (decimal totalPremija, decimal totalRealizirana, decimal totalPresmetana) CalculateConsistentTotals(List<OBD1Row> rows)
        {
            var totalPremija = rows.Sum(r => r.OriginalPremija ?? r.Premija ?? 0);
            var totalRealizirana = rows.Sum(r => r.OriginalRealiziranaProvizija ?? r.RealiziranaProvizija ?? 0);
            var totalPresmetana = rows.Sum(r => r.OriginalPresmetanaProvizija ?? r.PresmetanaProvizija ?? 0);
            
            var roundedTotalPremija = RoundHalfUp(totalPremija);
            var roundedTotalRealizirana = RoundHalfUp(totalRealizirana);
            var roundedTotalPresmetana = RoundHalfUp(totalPresmetana);
            
            System.Diagnostics.Debug.WriteLine($"CalculateConsistentTotals - Raw: Premija={totalPremija}, Realizirana={totalRealizirana}, Presmetana={totalPresmetana}");
            System.Diagnostics.Debug.WriteLine($"CalculateConsistentTotals - Rounded: Premija={roundedTotalPremija}, Realizirana={roundedTotalRealizirana}, Presmetana={roundedTotalPresmetana}");
            
            return (roundedTotalPremija, roundedTotalRealizirana, roundedTotalPresmetana);
        }

        // Test method to verify totals calculation
        public async Task<IActionResult> OnPostTestTotalsAsync()
        {
            Console.WriteLine("=== TESTING TOTALS CALCULATION ===");
            
            // Get current form data
            var currentFormData = GetCurrentFormData();
            Console.WriteLine($"Current form data count: {currentFormData.Count}");
            
            // Calculate page totals
            var pageTotalPremija = TotalPremija;
            var pageTotalRealizirana = TotalRealiziranaProvizija;
            var pageTotalPresmetana = TotalPresmetanaProvizija;
            
            Console.WriteLine($"Page Totals: Premija={pageTotalPremija}, Realizirana={pageTotalRealizirana}, Presmetana={pageTotalPresmetana}");
            
            // Calculate consistent totals
            var (consistentTotalPremija, consistentTotalRealizirana, consistentTotalPresmetana) = CalculateConsistentTotals(currentFormData);
            
            Console.WriteLine($"Consistent Totals: Premija={consistentTotalPremija}, Realizirana={consistentTotalRealizirana}, Presmetana={consistentTotalPresmetana}");
            
            // Check if they match
            bool premijaMatch = pageTotalPremija == consistentTotalPremija;
            bool realiziranaMatch = pageTotalRealizirana == consistentTotalRealizirana;
            bool presmetanaMatch = pageTotalPresmetana == consistentTotalPresmetana;
            
            Console.WriteLine($"Matches: Premija={premijaMatch}, Realizirana={realiziranaMatch}, Presmetana={presmetanaMatch}");
            Console.WriteLine("=== END TESTING TOTALS CALCULATION ===");
            
            return Content($"Page Totals: {pageTotalPremija}, {pageTotalRealizirana}, {pageTotalPresmetana} | Consistent Totals: {consistentTotalPremija}, {consistentTotalRealizirana}, {consistentTotalPresmetana}", "text/plain");
        }
    }
}
