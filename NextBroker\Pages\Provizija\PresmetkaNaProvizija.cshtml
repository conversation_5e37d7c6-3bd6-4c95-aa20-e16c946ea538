@page
@model NextBroker.Pages.Provizija.PresmetkaNaProvizija
@{
    ViewData["Title"] = "Пресметка на провизија";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2>@ViewData["Title"]</h2>        
    </div>

    <div class="card">
        <div class="card-header">
            <h5>Пресметка на провизија</h5>
        </div>
        <div class="card-body">
            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success mb-3">
                    <i class="fas fa-check-circle me-2"></i> @TempData["SuccessMessage"]
                </div>
            }
            <form method="post">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="SellerSearch" class="form-label">Соработник/Вработен</label>
                            <div class="input-group mb-2">
                                <input type="text" id="SellerSearch" class="form-control" placeholder="Пребарај соработници...">
                                <button type="button" id="AddSeller" class="btn btn-secondary">Додади</button>
                                <button type="button" id="SelectAllSellers" class="btn btn-outline-primary">Избери сите</button>
                            </div>
                            <small class="form-text text-muted">Пребарајте и изберете соработник или вработен за пресметка на провизија.</small>
                            
                            <div id="selectedSellers" class="border p-2 rounded mt-2" style="min-height: 100px; max-height: 200px; overflow-y: auto;">
                                <!-- Selected sellers will be displayed here -->
                            </div>
                            
                            <input type="hidden" id="SellerIds" name="SellerIds" />
                            <span class="text-danger" id="SellerIdsValidation">@Html.ValidationMessageFor(m => m.SellerIds)</span>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label d-block">Избор на полиси</label>
                            <div class="form-check form-check-inline mb-2">
                                <input class="form-check-input" type="radio" name="policySelectionMode" id="manualPolicySelection" value="manual">
                                <label class="form-check-label" for="manualPolicySelection">
                                    Рачен избор на полиси
                                </label>
                            </div>
                            <div class="form-check form-check-inline mb-2">
                                <input class="form-check-input" type="radio" name="policySelectionMode" id="autoPolicySelection" value="auto" checked>
                                <label class="form-check-label" for="autoPolicySelection">
                                    Автоматски избор на полиси според уплати
                                </label>
                            </div>
                            
                            <div id="manualPolicySelectionContainer">
                                <label for="PolicySearch" class="form-label">Пребарај полиси (по број на полиса)</label>
                                <div class="input-group">
                                    <input type="text" id="PolicySearch" class="form-control" placeholder="Внесете број на полиса...">
                                    <button type="button" id="AddPolicy" class="btn btn-secondary">Додади</button>
                                </div>
                                <small class="form-text text-muted">Пребарајте ги и изберете ги полисите за пресметка на провизија.</small>
                                
                                <div id="selectedPolicies" class="border p-2 rounded mt-2" style="min-height: 100px; max-height: 200px; overflow-y: auto;">
                                    <!-- Selected policies will be displayed here -->
                                </div>
                            </div>
                            
                            <div id="autoPolicySelectionContainer" style="display: none;">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i> Полисите ќе бидат автоматски избрани според уплатите во внесениот датумски период.
                                </div>
                                
                                @if (Model.ProcessingComplete && Model.PolicySelectionMode == "auto")
                                {
                                    <div class="alert alert-success mt-2">
                                        <i class="fas fa-check-circle me-2"></i> Автоматски избрани <strong>@(Model.PolisiIds?.Split(',', StringSplitOptions.RemoveEmptyEntries).Length ?? 0)</strong> полиси со уплати за периодот.
                                    </div>
                                }
                            </div>
                            
                            <input type="hidden" id="PolisiIds" name="PolisiIds" />
                            <input type="hidden" id="PolicySelectionMode" name="PolicySelectionMode" value="auto" />
                            <span class="text-danger" id="PolisiIdsValidation">@Html.ValidationMessageFor(m => m.PolisiIds)</span>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group mb-3">
                            <label for="DatumOd" class="form-label">Датум од</label>
                            <input id="DatumOd" name="DatumOd" type="date" class="form-control" value="@Model.DatumOd.ToString("yyyy-MM-dd")" />
                            <span class="text-danger" id="DatumOdValidation"></span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group mb-3">
                            <label for="DatumDo" class="form-label">Датум до</label>
                            <input id="DatumDo" name="DatumDo" type="date" class="form-control" value="@Model.DatumDo.ToString("yyyy-MM-dd")" />
                            <span class="text-danger" id="DatumDoValidation"></span>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-calculator me-2"></i>Пресметај провизија
                        </button>
                        
                        @if (Model.ProcessingComplete && Model.SuccessfullyProcessed > 0)
                        {
                            <button type="button" id="previewExcelBtn" class="btn btn-success ms-2">
                                <i class="fas fa-file-excel me-1"></i> Преглед Excel
                            </button>
                        }
                    </div>
                </div>
            </form>

            @if (Model.ProcessingComplete)
            {
                <div class="mt-4">
                    @if (Model.SuccessfullyProcessed > 0)
                    {
                        <div class="alert alert-success">
                            <h5><i class="fas fa-check-circle me-2"></i>Провизијата е успешно калкулирана</h5>
                            <p class="mb-0">Можете да ја прегледате со копчето Преглед Excel или да ја преземете со копчето Excel или со извештај подоле.</p>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>Нема податоци</h5>
                            <p class="mb-0">Нема пронајдени податоци за пресметка во избраниот период.</p>
                        </div>
                    }
                </div>
            }
            
            <!-- Excel Preview Section (Inline) -->
            <div id="excelPreviewSection" class="mt-4" style="display: none;">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5>Преглед на Excel податоци</h5>
                        <a href="#" id="downloadExcelBtn" class="btn btn-primary">
                            <i class="fas fa-download me-1"></i> Преземи Excel
                        </a>
                    </div>
                    <div class="card-body">
                        <!-- Filter Options -->
                        <div class="mb-4">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">Филтрирај по тип на соработник</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input class="form-check-input filter-checkbox" type="checkbox" value="3" id="filterVraboten" checked>
                                                <label class="form-check-label" for="filterVraboten">
                                                    <i class="fas fa-user-tie me-1"></i> Вработен
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input class="form-check-input filter-checkbox" type="checkbox" value="2" id="filterSorabotnik" checked>
                                                <label class="form-check-label" for="filterSorabotnik">
                                                    <i class="fas fa-handshake me-1"></i> Соработник
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="filterNadreden">
                                                <label class="form-check-label" for="filterNadreden">
                                                    <i class="fas fa-user-tag me-1"></i> Надреден
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-2" id="nivoNaNadredenostContainer" style="display: none;">
                                        <div class="col-md-6">
                                            <label for="nivoNaNadredenost" class="form-label">Ниво на надреденост</label>
                                            <select id="nivoNaNadredenost" class="form-select">
                                                <option value="0" selected>Сите нивоа</option>
                                                <option value="1">Ниво 1</option>
                                                <option value="2">Ниво 2</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-12">
                                            <button type="button" id="applyFilters" class="btn btn-sm btn-primary">
                                                <i class="fas fa-filter me-1"></i> Примени филтри
                                            </button>
                                            <button type="button" id="clearFilters" class="btn btn-sm btn-outline-secondary ms-2">
                                                <i class="fas fa-times me-1"></i> Ресетирај филтри
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center p-5" id="previewLoading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Вчитување...</span>
                            </div>
                            <p class="mt-2">Вчитување на податоци...</p>
                        </div>
                        <div class="table-responsive" id="previewTableContainer" style="display: none;">
                            <table class="table table-striped table-bordered" id="previewTable">
                                <thead>
                                    <tr id="previewTableHeader"></tr>
                                </thead>
                                <tbody id="previewTableBody"></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            @* Removed basic table view and redundant message - only use Excel preview *@
        </div>
    </div>

    <!-- New section for "Провизија извештај" -->
    <div class="card mt-4">
        <div class="card-header">
            <h5>Провизија извештај</h5>
        </div>
        <div class="card-body">
            <form method="post" asp-page-handler="GenerateProvizionReport">
                @Html.AntiForgeryToken()
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="ReportSellerSearch" class="form-label">Соработник/Вработен</label>
                            <div class="input-group mb-2">
                                <input type="text" id="ReportSellerSearch" class="form-control" placeholder="Пребарај соработници...">
                                <button type="button" id="ReportAddSeller" class="btn btn-secondary">Додади</button>
                                <button type="button" id="ReportSelectAllSellers" class="btn btn-outline-primary">Избери сите</button>
                            </div>
                            <small class="form-text text-muted">Пребарајте и изберете соработник или вработен за извештај на провизија.</small>
                            
                            <div id="reportSelectedSellers" class="border p-2 rounded mt-2" style="min-height: 100px; max-height: 200px; overflow-y: auto;">
                                <!-- Selected sellers will be displayed here -->
                            </div>
                            
                            <input type="hidden" id="ReportSellerIds" name="ReportSellerIds" asp-for="ReportSellerIds" />
                            <span class="text-danger" id="ReportSellerIdsValidation" asp-validation-for="ReportSellerIds"></span>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group mb-3">
                            <label for="ReportDatumOd" class="form-label">Датум од</label>
                            <input id="ReportDatumOd" name="ReportDatumOd" type="date" class="form-control" asp-for="ReportDatumOd" />
                            <span class="text-danger" id="ReportDatumOdValidation" asp-validation-for="ReportDatumOd"></span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group mb-3">
                            <label for="ReportDatumDo" class="form-label">Датум до</label>
                            <input id="ReportDatumDo" name="ReportDatumDo" type="date" class="form-control" asp-for="ReportDatumDo" />
                            <span class="text-danger" id="ReportDatumDoValidation" asp-validation-for="ReportDatumDo"></span>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-12">
                        <button type="submit" id="generateReportBtn" class="btn btn-primary">
                            <i class="fas fa-file-excel me-2"></i>Генерирај извештај
                        </button>
                        
                        @if (Model.ReportGenerated)
                        {
                            <a href="?handler=DownloadProvizionReport&ReportDatumOd=@Model.ReportDatumOd.ToString("yyyy-MM-dd")&ReportDatumDo=@Model.ReportDatumDo.ToString("yyyy-MM-dd")&ReportSellerIds=@Model.ReportSellerIds" class="btn btn-success ms-2">
                                <i class="fas fa-download me-1"></i> Преземи Excel
                            </a>
                        }
                    </div>
                </div>
            </form>

            @if (Model.ReportGenerated)
            {
                <div class="mt-4">
                    <div class="alert alert-success">
                        <h5>Извештај генериран</h5>
                        <p>Вкупно ставки: @Model.ReportTotalItems</p>
                        <p>Период: @Model.ReportDatumOd.ToString("dd.MM.yyyy") - @Model.ReportDatumDo.ToString("dd.MM.yyyy")</p>
                        <p>Вкупно соработници: @(Model.ReportSellerIds?.Split(',').Length ?? 0)</p>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
    <script>
        $(document).ready(function() {
            // Get anti-forgery token for AJAX calls
            var antiForgeryToken = $('input[name="__RequestVerificationToken"]').val();
            
            // Initialize selected arrays
            let selectedPolicies = [];
            let selectedSellers = [];
            let reportSelectedSellers = []; // For the new report section
            
            // Store the full preview data
            let fullPreviewData = null;
            let filteredPreviewData = null;
            
            // Initialize the policy selection display based on the checked radio button
            function initializePolicySelectionDisplay() {
                const checkedMode = $('input[name="policySelectionMode"]:checked').val();
                if (checkedMode === 'manual') {
                    $('#manualPolicySelectionContainer').show();
                    $('#autoPolicySelectionContainer').hide();
                    $('#PolicySelectionMode').val('manual');
                } else {
                    $('#manualPolicySelectionContainer').hide();
                    $('#autoPolicySelectionContainer').show();
                    $('#PolicySelectionMode').val('auto');
                }
            }
            
            // Initialize display on page load
            initializePolicySelectionDisplay();
            
            // Toggle between manual and automatic policy selection
            $('input[name="policySelectionMode"]').change(function() {
                const mode = $(this).val();
                if (mode === 'manual') {
                    $('#manualPolicySelectionContainer').show();
                    $('#autoPolicySelectionContainer').hide();
                    $('#PolicySelectionMode').val('manual');
                } else {
                    $('#manualPolicySelectionContainer').hide();
                    $('#autoPolicySelectionContainer').show();
                    $('#PolicySelectionMode').val('auto');
                    // Clear the selected policies when switching to auto mode
                    selectedPolicies = [];
                    updatePolicyDisplay();
                }
            });
            
            // Toggle the hierarchy level dropdown based on the "Nadreden" checkbox
            $('#filterNadreden').change(function() {
                if ($(this).is(':checked')) {
                    $('#nivoNaNadredenostContainer').slideDown(200);
                } else {
                    $('#nivoNaNadredenostContainer').slideUp(200);
                }
            });
            
            // Function to apply filters and update table
            function applyFilters() {
                if (!fullPreviewData) return;
                
                // Get selected client types
                const selectedTypes = [];
                $('.filter-checkbox:checked').each(function() {
                    selectedTypes.push($(this).val());
                });
                
                // If no filters selected, show all data
                if (selectedTypes.length === 0) {
                    // Show a message that no filters are selected
                    $("#previewTableContainer").html('<div class="alert alert-warning">Изберете барем еден тип на соработник за филтрирање.</div>');
                    $("#previewTableContainer").show();
                    return;
                }
                
                // Find the client type column index
                const clientTypeColumnIndex = fullPreviewData.columns.findIndex(column => 
                    column.includes('Клиент за провизија')
                );
                
                // Find index of columns related to superior (nadreden)
                const nadredenStavkaIndex = fullPreviewData.columns.findIndex(column => 
                    column.includes('Ниво на надреденост')
                );
                
                const isNadredenChecked = $('#filterNadreden').is(':checked');
                const selectedNivo = $('#nivoNaNadredenost').val();
                
                if (clientTypeColumnIndex === -1) {
                    console.error("Не може да се најде колоната со тип на клиент");
                    // Still show all data if we can't find the column
                    filteredPreviewData = fullPreviewData;
                } else {
                    // Filter by client type
                    let filteredRows = fullPreviewData.rows.filter(row => {
                        const clientTypeValue = row[clientTypeColumnIndex];
                        
                        // Check if any selected type matches
                        return selectedTypes.some(type => {
                            if (type === "2" && (clientTypeValue === 2 || clientTypeValue === "2" || clientTypeValue === "Соработник")) {
                                return true;
                            }
                            if (type === "3" && (clientTypeValue === 3 || clientTypeValue === "3" || clientTypeValue === "Вработен")) {
                                return true;
                            }
                            return false;
                        });
                    });
                    
                    // Then filter by nadreden if checked
                    if (isNadredenChecked) {
                        if (nadredenStavkaIndex !== -1) {
                            filteredRows = filteredRows.filter(row => {
                                const nivoValue = row[nadredenStavkaIndex];
                                
                                // Convert to number if it's not already
                                const nivoNum = typeof nivoValue === 'number' ? 
                                    nivoValue : 
                                    (nivoValue ? parseFloat(nivoValue) : 0);
                                
                                // Check if value is greater than 0
                                const hasValue = nivoNum > 0;
                                
                                // If level is selected (not "all levels")
                                if (selectedNivo !== '0' && hasValue) {
                                    // Try to match the exact level
                                    return nivoNum == selectedNivo; // Intentional loose comparison
                                }
                                
                                // Otherwise just check if value is greater than 0
                                return hasValue;
                            });
                        } else {
                            console.warn("Не може да се најде колоната за ниво на надреденост");
                        }
                    }
                    
                    // Update filtered data
                    filteredPreviewData = {
                        columns: fullPreviewData.columns,
                        rows: filteredRows
                    };
                }
                
                // Update the table with filtered data
                updatePreviewTable(filteredPreviewData);
                
                // Update the download link with filter parameters
                updateDownloadLink();
            }
            
            // Function to update the preview table with data
            function updatePreviewTable(data) {
                // Clear previous table content
                $("#previewTableHeader").empty();
                $("#previewTableBody").empty();
                
                // Add header columns
                data.columns.forEach(function(column) {
                    $("#previewTableHeader").append('<th>' + column + '</th>');
                });
                
                if (data.rows.length === 0) {
                    // Show message when no data matches filters
                    $("#previewTableBody").append('<tr><td colspan="' + data.columns.length + '" class="text-center">Нема податоци што одговараат на избраните филтри</td></tr>');
                } else {
                    // Add data rows (limit to first 10 for performance)
                    var rowsToShow = data.rows.slice(0, 10);
                    rowsToShow.forEach(function(row) {
                        var tr = $("<tr>");
                        row.forEach(function(cell) {
                            // Convert cell to string properly to avoid [object Object]
                            var cellValue = '';
                            if (cell !== null && cell !== undefined) {
                                if (typeof cell === 'object') {
                                    // Check if it's an empty object
                                    if (Object.keys(cell).length === 0) {
                                        cellValue = '';
                                    } else {
                                        cellValue = JSON.stringify(cell);
                                    }
                                } else {
                                    cellValue = String(cell);
                                }
                            }
                            tr.append('<td>' + cellValue + '</td>');
                        });
                        $("#previewTableBody").append(tr);
                    });
                    
                    // Add note about preview limit if needed
                    $("#previewTableContainer").find(".preview-note").remove();
                    if (data.rows.length > 10) {
                        $("#previewTableContainer").append('<div class="text-muted mt-2 preview-note">Прикажани се само првите 10 редови. За целосен преглед, преземете Excel датотеката. Вкупен број на редови: ' + data.rows.length + '</div>');
                    }
                }
                
                // Show table
                $("#previewTableContainer").show();
            }
            
            // Function to update download link with filter parameters
            function updateDownloadLink() {
                var baseUrl = '?handler=ExportExcel&DatumOd=@Model.DatumOd.ToString("yyyy-MM-dd")&DatumDo=@Model.DatumDo.ToString("yyyy-MM-dd")';
                
                // Add client type filters
                const selectedTypes = [];
                $('.filter-checkbox:checked').each(function() {
                    selectedTypes.push($(this).val());
                });
                
                if (selectedTypes && selectedTypes.length > 0) {
                    baseUrl += '&ClientTypes=' + selectedTypes.join(',');
                }
                
                // Add nadreden filter if checked
                if ($('#filterNadreden').is(':checked')) {
                    baseUrl += '&HasNadreden=true';
                    
                    // Add level filter if not "all levels"
                    const selectedNivo = $('#nivoNaNadredenost').val();
                    if (selectedNivo !== '0') {
                        baseUrl += '&NadredenNivo=' + selectedNivo;
                    }
                }
                
                $("#downloadExcelBtn").attr("href", baseUrl);
            }
            
            // Function to show Excel preview
            function showExcelPreview() {
                console.log("Preview function called");
                
                // Show the preview section
                $("#excelPreviewSection").show();
                
                // Scroll to the preview section
                $('html, body').animate({
                    scrollTop: $("#excelPreviewSection").offset().top - 100
                }, 500);
                
                // Show loading and hide table
                $("#previewLoading").show();
                $("#previewTableContainer").hide();
                
                // Get selected client types
                const selectedTypes = [];
                $('.filter-checkbox:checked').each(function() {
                    selectedTypes.push($(this).val());
                });
                
                // Get nadreden filters
                const hasNadreden = $('#filterNadreden').is(':checked');
                const nadredenNivo = hasNadreden ? $('#nivoNaNadredenost').val() : null;
                
                // Set download link
                updateDownloadLink();
                
                // Fetch preview data
                $.ajax({
                    url: "?handler=PreviewExcel",
                    type: "GET",
                    data: { 
                        datumOd: '@Model.DatumOd.ToString("yyyy-MM-dd")', 
                        datumDo: '@Model.DatumDo.ToString("yyyy-MM-dd")',
                        clientTypes: selectedTypes.length > 0 ? selectedTypes.join(',') : null,
                        hasNadreden: hasNadreden,
                        nadredenNivo: nadredenNivo !== '0' ? nadredenNivo : null,
                        policySelectionMode: '@Model.PolicySelectionMode',
                        currentBatchId: @(Model.CurrentBatchId?.ToString() ?? "null")
                    },
                    contentType: "application/json",
                    dataType: "json",
                    headers: {
                        "RequestVerificationToken": antiForgeryToken
                    },
                    success: function(data) {
                        // Hide loading
                        $("#previewLoading").hide();
                        
                        if (data && data.columns && data.rows && data.rows.length > 0) {
                            // Store full data for filtering
                            fullPreviewData = data;
                            
                            // Apply initial filters (all checked by default)
                            applyFilters();
                        } else {
                            $("#previewTableContainer").html('<div class="alert alert-warning">Нема податоци за прикажување. Ве молиме проверете дали пресметката е успешна.</div>');
                            $("#previewTableContainer").show();
                            
                            // If we're in auto mode with a single policy, add extra guidance
                            if ('@Model.PolicySelectionMode' === 'auto' && @Model.TotalPolisi === 1) {
                                $("#previewTableContainer").append('<div class="alert alert-info mt-2"><i class="fas fa-info-circle me-2"></i>При автоматски избор на единечна полиса, податоците може да не се видливи. Пробајте повторно со рачен избор на полисата.</div>');
                            }
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("AJAX Error:", error);
                        console.log("Response:", xhr.responseText);
                        $("#previewLoading").hide();
                        $("#previewTableContainer").html('<div class="alert alert-danger">Грешка при вчитување на податоците за преглед: ' + error + '</div>');
                        $("#previewTableContainer").show();
                    }
                });
            }
            
            // Attach event to both preview buttons
            $(document).on('click', '#previewExcelBtn, #tablePreviewExcelBtn', function() {
                showExcelPreview();
            });
            
            // Apply filters button click handler
            $(document).on('click', '#applyFilters', function() {
                applyFilters();
            });
            
            // Clear filters button click handler
            $(document).on('click', '#clearFilters', function() {
                // Check all filter checkboxes
                $('.filter-checkbox').prop('checked', true);
                // Uncheck Nadreden
                $('#filterNadreden').prop('checked', false);
                // Reset dropdown
                $('#nivoNaNadredenost').val('0');
                // Hide the dropdown container
                $('#nivoNaNadredenostContainer').hide();
                // Apply filters
                applyFilters();
            });
            
            // Autocomplete for policy search
            $("#PolicySearch").autocomplete({
                source: function(request, response) {
                    $.ajax({
                        url: "?handler=SearchPolicies",
                        type: "GET",
                        data: { searchTerm: request.term },
                        contentType: "application/json",
                        dataType: "json",
                        headers: {
                            "RequestVerificationToken": antiForgeryToken
                        },
                        success: function(data) {
                            response(data);
                        },
                        error: function() {
                            response([]);
                        }
                    });
                },
                minLength: 2,
                select: function(event, ui) {
                    event.preventDefault();
                    $("#PolicySearch").val(ui.item.brojNaPolisa);
                    // Auto-add the selected policy
                    addPolicy(ui.item.brojNaPolisa);
                    return false;
                }
            }).autocomplete("instance")._renderItem = function(ul, item) {
                return $("<li>")
                    .append("<div><strong>" + item.brojNaPolisa + "</strong> - " + item.osiguritel + "</div>")
                    .appendTo(ul);
            };
            
            // Autocomplete for seller search
            $("#SellerSearch").autocomplete({
                source: function(request, response) {
                    $.ajax({
                        url: "?handler=SearchSellers",
                        type: "GET",
                        data: { searchTerm: request.term },
                        contentType: "application/json",
                        dataType: "json",
                        headers: {
                            "RequestVerificationToken": antiForgeryToken
                        },
                        success: function(data) {
                            response(data);
                        },
                        error: function() {
                            response([]);
                        }
                    });
                },
                minLength: 2,
                select: function(event, ui) {
                    event.preventDefault();
                    $("#SellerSearch").val(ui.item.displayName);
                    // Add the selected seller
                    addSeller(ui.item.id, ui.item.displayName);
                    return false;
                }
            }).autocomplete("instance")._renderItem = function(ul, item) {
                let icon = '';
                if (item.isVraboten) icon = '<i class="fas fa-user-tie me-1" title="Вработен"></i>';
                if (item.isSorabotnik) icon = '<i class="fas fa-handshake me-1" title="Соработник"></i>';
                if (item.isBroker) icon = '<i class="fas fa-building me-1" title="Брокер"></i>';
                
                return $("<li>")
                    .append("<div>" + icon + item.displayName + "</div>")
                    .appendTo(ul);
            };
            
            // Add policy button click handler
            $("#AddPolicy").click(function() {
                const policyNumber = $("#PolicySearch").val().trim();
                if (policyNumber === "") return;
                
                addPolicy(policyNumber);
            });
            
            // Add seller button click handler
            $("#AddSeller").click(function() {
                const sellerName = $("#SellerSearch").val().trim();
                if (sellerName === "") return;
                
                // Find seller by name
                $.ajax({
                    url: "?handler=GetSellerByName",
                    type: "GET",
                    data: { sellerName: sellerName },
                    contentType: "application/json",
                    dataType: "json",
                    headers: {
                        "RequestVerificationToken": antiForgeryToken
                    },
                    success: function(data) {
                        if (data.id) {
                            addSeller(data.id, data.displayName);
                            $("#SellerSearch").val("");
                        } else {
                            alert("Соработникот не е пронајден!");
                        }
                    },
                    error: function() {
                        alert("Грешка при пребарување на соработник!");
                    }
                });
            });
            
            // Select All Sellers button click handler
            $("#SelectAllSellers").click(function() {
                $.ajax({
                    url: "?handler=GetAllSellers",
                    type: "GET",
                    contentType: "application/json",
                    dataType: "json",
                    headers: {
                        "RequestVerificationToken": antiForgeryToken
                    },
                    success: function(data) {
                        if (data && data.length > 0) {
                            // Clear existing selections
                            selectedSellers = [];
                            
                            // Add all sellers
                            data.forEach(function(seller) {
                                selectedSellers.push({
                                    id: seller.id,
                                    displayName: seller.displayName,
                                    isVraboten: seller.isVraboten,
                                    isSorabotnik: seller.isSorabotnik,
                                    isBroker: seller.isBroker
                                });
                            });
                            
                            updateSellerDisplay();
                            $("#SellerIdsValidation").html("");
                        } else {
                            alert("Нема соработници за додавање!");
                        }
                    },
                    error: function() {
                        alert("Грешка при вчитување на сите соработници!");
                    }
                });
            });
            
            // Function to add a policy
            function addPolicy(policyNumber) {
                // Check if already in the list
                $.ajax({
                    url: "?handler=GetPolicyId",
                    type: "GET",
                    data: { policyNumber: policyNumber },
                    contentType: "application/json",
                    dataType: "json",
                    headers: {
                        "RequestVerificationToken": antiForgeryToken
                    },
                    success: function(data) {
                        if (data.id) {
                            // Check if already in the list
                            if (!selectedPolicies.some(p => p.id === data.id)) {
                                selectedPolicies.push({ 
                                    id: data.id, 
                                    brojNaPolisa: data.brojNaPolisa,
                                    osiguritel: data.osiguritel
                                });
                                updatePolicyDisplay();
                                // Clear validation error if policies are added
                                $("#PolisiIdsValidation").html("");
                            }
                            $("#PolicySearch").val("");
                        } else {
                            alert("Полисата не е пронајдена!");
                        }
                    },
                    error: function() {
                        alert("Грешка при пребарување на полиса!");
                    }
                });
            }
            
            // Function to add a seller
            function addSeller(id, displayName, isVraboten = false, isSorabotnik = false, isBroker = false) {
                // Check if already in the list
                if (!selectedSellers.some(s => s.id === id)) {
                    selectedSellers.push({
                        id: id,
                        displayName: displayName,
                        isVraboten: isVraboten,
                        isSorabotnik: isSorabotnik,
                        isBroker: isBroker
                    });
                    updateSellerDisplay();
                    // Clear validation error if sellers are added
                    $("#SellerIdsValidation").html("");
                }
            }
            
            // Function to update the policy display and hidden field
            function updatePolicyDisplay() {
                // Clear the display
                $("#selectedPolicies").empty();
                
                if (selectedPolicies.length === 0) {
                    $("#selectedPolicies").append("<div class='text-muted text-center p-3'>Нема избрани полиси</div>");
                } else {
                    // Add each policy
                    selectedPolicies.forEach(function(policy) {
                        const policyElement = $("<div class='mb-1 p-2 bg-light rounded d-flex justify-content-between align-items-center'>");
                        policyElement.append("<span><strong>" + policy.brojNaPolisa + "</strong> - " + policy.osiguritel + "</span>");
                        
                        // Add remove button
                        const removeBtn = $("<button type='button' class='btn btn-sm btn-danger'>×</button>");
                        removeBtn.on("click", function() {
                            selectedPolicies = selectedPolicies.filter(p => p.id !== policy.id);
                            updatePolicyDisplay();
                        });
                        
                        policyElement.append(removeBtn);
                        $("#selectedPolicies").append(policyElement);
                    });
                }
                
                // Update the hidden field with policy IDs
                $("#PolisiIds").val(selectedPolicies.map(p => p.id).join(','));
            }
            
            // Function to update the seller display and hidden field
            function updateSellerDisplay() {
                // Clear the display
                $("#selectedSellers").empty();
                
                if (selectedSellers.length === 0) {
                    $("#selectedSellers").append("<div class='text-muted text-center p-3'>Нема избрани соработници</div>");
                } else {
                    // Add each seller
                    selectedSellers.forEach(function(seller) {
                        const sellerElement = $("<div class='mb-1 p-2 bg-light rounded d-flex justify-content-between align-items-center'>");
                        
                        // Add proper icon based on seller type
                        let icon = '';
                        if (seller.isVraboten) icon = '<i class="fas fa-user-tie me-1" title="Вработен"></i>';
                        if (seller.isSorabotnik) icon = '<i class="fas fa-handshake me-1" title="Соработник"></i>';
                        if (seller.isBroker) icon = '<i class="fas fa-building me-1" title="Брокер"></i>';
                        
                        sellerElement.append("<span>" + icon + seller.displayName + "</span>");
                        
                        // Add remove button
                        const removeBtn = $("<button type='button' class='btn btn-sm btn-danger'>×</button>");
                        removeBtn.on("click", function() {
                            selectedSellers = selectedSellers.filter(s => s.id !== seller.id);
                            updateSellerDisplay();
                        });
                        
                        sellerElement.append(removeBtn);
                        $("#selectedSellers").append(sellerElement);
                    });
                    
                    // Show total count if more than a few sellers
                    if (selectedSellers.length > 5) {
                        $("#selectedSellers").append("<div class='text-info text-center mt-2'>Вкупно избрани: " + selectedSellers.length + "</div>");
                    }
                }
                
                // Update the hidden field with seller IDs
                $("#SellerIds").val(selectedSellers.map(s => s.id).join(','));
            }
            
            // Show initial empty states
            updatePolicyDisplay();
            updateSellerDisplay();
            
            // Autocomplete for report seller search
            $("#ReportSellerSearch").autocomplete({
                source: function(request, response) {
                    $.ajax({
                        url: "?handler=SearchSellers",
                        type: "GET",
                        data: { searchTerm: request.term },
                        contentType: "application/json",
                        dataType: "json",
                        headers: {
                            "RequestVerificationToken": antiForgeryToken
                        },
                        success: function(data) {
                            response(data);
                        },
                        error: function() {
                            response([]);
                        }
                    });
                },
                minLength: 2,
                select: function(event, ui) {
                    event.preventDefault();
                    $("#ReportSellerSearch").val(ui.item.displayName);
                    // Add the selected seller to report section
                    addReportSeller(ui.item.id, ui.item.displayName, ui.item.isVraboten, ui.item.isSorabotnik, ui.item.isBroker);
                    return false;
                }
            }).autocomplete("instance")._renderItem = function(ul, item) {
                let icon = '';
                if (item.isVraboten) icon = '<i class="fas fa-user-tie me-1" title="Вработен"></i>';
                if (item.isSorabotnik) icon = '<i class="fas fa-handshake me-1" title="Соработник"></i>';
                if (item.isBroker) icon = '<i class="fas fa-building me-1" title="Брокер"></i>';
                
                return $("<li>")
                    .append("<div>" + icon + item.displayName + "</div>")
                    .appendTo(ul);
            };
            
            // Add report seller button click handler
            $("#ReportAddSeller").click(function() {
                const sellerName = $("#ReportSellerSearch").val().trim();
                if (sellerName === "") return;
                
                // Find seller by name
                $.ajax({
                    url: "?handler=GetSellerByName",
                    type: "GET",
                    data: { sellerName: sellerName },
                    contentType: "application/json",
                    dataType: "json",
                    headers: {
                        "RequestVerificationToken": antiForgeryToken
                    },
                    success: function(data) {
                        if (data.id) {
                            addReportSeller(data.id, data.displayName, data.isVraboten, data.isSorabotnik, data.isBroker);
                            $("#ReportSellerSearch").val("");
                        } else {
                            console.log("Соработникот не е пронајден!");
                        }
                    },
                    error: function() {
                        console.log("Грешка при пребарување на соработник!");
                    }
                });
            });
            
            // Select All Sellers for report button click handler
            $("#ReportSelectAllSellers").click(function() {
                $.ajax({
                    url: "?handler=GetAllSellers",
                    type: "GET",
                    contentType: "application/json",
                    dataType: "json",
                    headers: {
                        "RequestVerificationToken": antiForgeryToken
                    },
                    success: function(data) {
                        if (data && data.length > 0) {
                            // Clear existing selections
                            reportSelectedSellers = [];
                            
                            // Add all sellers
                            data.forEach(function(seller) {
                                reportSelectedSellers.push({
                                    id: seller.id,
                                    displayName: seller.displayName,
                                    isVraboten: seller.isVraboten,
                                    isSorabotnik: seller.isSorabotnik,
                                    isBroker: seller.isBroker
                                });
                            });
                            
                            updateReportSellerDisplay();
                            $("#ReportSellerIdsValidation").html("");
                        } else {
                            console.log("Нема соработници за додавање!");
                        }
                    },
                    error: function() {
                        console.log("Грешка при вчитување на сите соработници!");
                    }
                });
            });
            
            // Initialize report sellers display
            updateReportSellerDisplay();
            
            // Updated main form submission handler
            $("form:not([asp-page-handler='GenerateProvizionReport'])").on("submit", function(e) {
                console.log("Main form submission");
                let valid = true;
                
                // Always attempt to set the seller IDs and policy IDs in hidden fields
                // even if they might be empty
                $("#SellerIds").val(selectedSellers.map(s => s.id).join(','));
                
                const policyMode = $('input[name="policySelectionMode"]:checked').val();
                if (policyMode === 'manual') {
                    $("#PolisiIds").val(selectedPolicies.map(p => p.id).join(','));
                }
                
                // Make client-side validation optional - just log warnings
                if (selectedSellers.length === 0) {
                    console.log("Warning: No sellers selected");
                }
                
                if (policyMode === 'manual' && selectedPolicies.length === 0) {
                    console.log("Warning: No policies selected in manual mode");
                }
                
                // Clear any validation errors
                $("#SellerIdsValidation").text("");
                $("#PolisiIdsValidation").text("");
                $("#DatumOdValidation").text("");
                $("#DatumDoValidation").text("");
                
                // Always allow form submission for maximum flexibility
                return true;
            });
            
            // Updated report form submission handler
            $("form[asp-page-handler='GenerateProvizionReport']").on("submit", function(e) {
                console.log("Report form submitted");
                
                // Always set the report seller IDs in the hidden field
                $("#ReportSellerIds").val(reportSelectedSellers.map(s => s.id).join(','));
                console.log("SellerIds for report:", $("#ReportSellerIds").val());
                
                // Make validation optional - just log warnings
                if (reportSelectedSellers.length === 0) {
                    console.log("Warning: No sellers selected for report");
                }
                
                if (!$("#ReportDatumOd").val()) {
                    console.log("Warning: No start date for report");
                }
                
                if (!$("#ReportDatumDo").val()) {
                    console.log("Warning: No end date for report");
                }
                
                // Clear any validation errors
                $("#ReportSellerIdsValidation").text("");
                $("#ReportDatumOdValidation").text("");
                $("#ReportDatumDoValidation").text("");
                
                // Always allow form submission
                return true;
            });
            
            // Function to add a seller to the report section
            function addReportSeller(id, displayName, isVraboten = false, isSorabotnik = false, isBroker = false) {
                // Check if already in the list
                if (!reportSelectedSellers.some(s => s.id === id)) {
                    reportSelectedSellers.push({
                        id: id,
                        displayName: displayName,
                        isVraboten: isVraboten,
                        isSorabotnik: isSorabotnik,
                        isBroker: isBroker
                    });
                    updateReportSellerDisplay();
                    // Clear validation error if sellers are added
                    $("#ReportSellerIdsValidation").html("");
                }
            }
            
            // Function to update the report seller display and hidden field
            function updateReportSellerDisplay() {
                // Clear the display
                $("#reportSelectedSellers").empty();
                
                if (reportSelectedSellers.length === 0) {
                    $("#reportSelectedSellers").append("<div class='text-muted text-center p-3'>Нема избрани соработници</div>");
                } else {
                    // Add each seller
                    reportSelectedSellers.forEach(function(seller) {
                        const sellerElement = $("<div class='mb-1 p-2 bg-light rounded d-flex justify-content-between align-items-center'>");
                        
                        // Add proper icon based on seller type
                        let icon = '';
                        if (seller.isVraboten) icon = '<i class="fas fa-user-tie me-1" title="Вработен"></i>';
                        if (seller.isSorabotnik) icon = '<i class="fas fa-handshake me-1" title="Соработник"></i>';
                        if (seller.isBroker) icon = '<i class="fas fa-building me-1" title="Брокер"></i>';
                        
                        sellerElement.append("<span>" + icon + seller.displayName + "</span>");
                        
                        // Add remove button
                        const removeBtn = $("<button type='button' class='btn btn-sm btn-danger'>×</button>");
                        removeBtn.on("click", function() {
                            reportSelectedSellers = reportSelectedSellers.filter(s => s.id !== seller.id);
                            updateReportSellerDisplay();
                        });
                        
                        sellerElement.append(removeBtn);
                        $("#reportSelectedSellers").append(sellerElement);
                    });
                    
                    // Show total count if more than a few sellers
                    if (reportSelectedSellers.length > 5) {
                        $("#reportSelectedSellers").append("<div class='text-info text-center mt-2'>Вкупно избрани: " + reportSelectedSellers.length + "</div>");
                    }
                }
                
                // Update the hidden field with seller IDs
                $("#ReportSellerIds").val(reportSelectedSellers.map(s => s.id).join(','));
            }
        });
    </script>
}