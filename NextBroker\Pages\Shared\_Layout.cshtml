@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor
@inject IConfiguration Configuration
@using Microsoft.Data.SqlClient
@using System.Data
@using RazorPortal.Services

@{
    var username = HttpContextAccessor.HttpContext.Session.GetString("Username");
    var accessiblePages = await SecurePageModel.GetUserAccessiblePages(username, Configuration);
    var currentLanguage = HttpContextAccessor.HttpContext.Session.GetString("Language") ?? "Mk";
    var infoPagesFolder = currentLanguage == "En" ? "InfoPagesEn" : "InfoPagesMk";

    // Dictionary for translations
    var translations = new Dictionary<string, string>
    {
        ["Home"] = currentLanguage == "En" ? "Home" : "Дома",
        ["Start"] = currentLanguage == "En" ? "Start" : "Почетна",
        ["About"] = currentLanguage == "En" ? "About" : "За нас",
        ["Services"] = currentLanguage == "En" ? "Services" : "Услуги",
        ["LifeInsurance"] = currentLanguage == "En" ? "Life Insurance" : "Животно осигурување",
        ["LegalEntities"] = currentLanguage == "En" ? "Legal Entities" : "Правни лица",
        ["NaturalPersons"] = currentLanguage == "En" ? "Natural Persons" : "Физички лица",
        ["Career"] = currentLanguage == "En" ? "Career" : "Кариера",
        ["Contact"] = currentLanguage == "En" ? "Contact" : "Контакт",
        ["Administration"] = currentLanguage == "En" ? "Administration" : "Администрација",
        ["Tools"] = currentLanguage == "En" ? "Tools" : "Алатки",
        ["Pages"] = currentLanguage == "En" ? "Pages" : "Страници",
        ["Municipalities"] = currentLanguage == "En" ? "Municipalities MK" : "Општини МК",
        ["AccessControl"] = currentLanguage == "En" ? "Access Control" : "Контрола на пристап",
        ["Activities"] = currentLanguage == "En" ? "List of Activities" : "Листа на дејности",
        ["InsuranceClasses"] = currentLanguage == "En" ? "Insurance Classes" : "Класи на осигурување",
        ["InsurersList"] = currentLanguage == "En" ? "Insurers List" : "Листа на осигурители",
        ["User"] = currentLanguage == "En" ? "User" : "Корисник",
        ["Login"] = currentLanguage == "En" ? "Login" : "Најава",
        ["Logout"] = currentLanguage == "En" ? "Logout" : "Одјава",
        ["Register"] = currentLanguage == "En" ? "Register" : "Регистрација",
        ["AskQuestion"] = currentLanguage == "En" ? "Ask a Question" : "Постави прашање",
        ["Branches"] = currentLanguage == "En" ? "Branches" : "Експозитури",
        ["Products"] = currentLanguage == "En" ? "Products" : "Продукти",
        ["Notifications"] = currentLanguage == "En" ? "Notifications" : "Известувања",
        ["Clients"] = currentLanguage == "En" ? "Clients" : "Клиенти",
        ["AddClient"] = currentLanguage == "En" ? "Add Client" : "Додај клиент",
        ["ClientsList"] = currentLanguage == "En" ? "Clients List" : "Листа на клиенти",
        ["Policies"] = currentLanguage == "En" ? "Policies" : "Полиси",
        ["AutoLiability"] = currentLanguage == "En" ? "Auto Liability" : "Авто Одговорност",
        ["AddPolicy"] = currentLanguage == "En" ? "Add Policy" : "Додај Полиса",
        ["PoliciesList"] = currentLanguage == "En" ? "Policies List" : "Листа на полиси",
        ["GreenCard"] = currentLanguage == "En" ? "Green Card" : "Зелен Картон",
        ["Finances"] = currentLanguage == "En" ? "Finances" : "Финансии",
        ["Statements"] = currentLanguage == "En" ? "Statements" : "Изводи",
        ["Payments"] = currentLanguage == "En" ? "Payments" : "Уплати",
        ["KasovIzvestaj"] = currentLanguage == "En" ? "Cash Report" : "Касов извештај",
        ["Reports"] = currentLanguage == "En" ? "Reports" : "Прегледи/Извештаи",
        ["DebtStatusByContractor"] = currentLanguage == "En" ? "Debt Status by Contractor" : "Состојба на долг по договорувач",
    };

    var systemPages = new[] { "KlasiOsiguruvanje", "Produkti", "SifrarnikBankiSmetki" };
    var clientPages = new[] { "ListaKlienti", "DodajKlient" };
    var autoLiabilityPages = new[] { "DodajPolisaAO", "DodajPolisaZK", "DodajPolisaGR", "DodajPolisaKasko", "ListaPolisi" };
    var finansiiPages = new[] { "Izvodi", "Uplati", "KasovIzvestaj", "PrenosNaPremijaSpecifikacii", "ListaFakturiKonOsiguritelProvizija" };
    var reportsPages = new[] { "SostojbaNaDolgPoDogovoruvac", "ASOMesecenIzvestaj", "ASOMesecenIzvestajSintetika", "IzvestajProvizijaBrokerMesecen", "IzvestajGeneriraniIzlezniFakturiKonKlient", "IzvestajPrenosNaNaplataVlezniFakturi" };
    var analysisPages = new[] { "AnalizaNaKlient", "Rizici" };
    var provizijaPages = new[] { "ProvizijaSetiranje", "PresmetkaNaProvizija" };
}

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - NextBroker</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />

    <!-- Custom CSS -->
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/RazorPortal.styles.css" asp-append-version="true" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <!-- DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.24/css/jquery.dataTables.css">

    <!-- SignalR -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/6.0.6/signalr.min.js"></script>

    @await RenderSectionAsync("Styles", required: false)

    <style>
        .notification-bell {
            position: relative;
        }
        
        .notification-badge {
            position: absolute;
            top: 0;
            right: -8px;
            font-size: 0.7rem;
            padding: 0.25em 0.6em;
        }
    </style>
</head>

<body>
    <header class="sticky-top">
        <nav class="navbar navbar-expand-sm navbar-light bg-light border-bottom box-shadow navbar-shadow mb-3">
            <div class="container-fluid">
                <div>
                    @{
                        var systemEnv = Context.Session.GetString("NextBrokerSystemEnvironment");
                        var logoImage = systemEnv == "dev" ? "nothing.png" : "nextbyte.png";
                    }
                    <img src="/images/logo/@logoImage" class="nextbyte_logo ms-3" alt="Next Byte International">
                </div>

                <a class="navbar-brand ms-3" asp-area="" asp-page="@(Context.Session.GetString("Username") != null ? (currentLanguage == "En" ? "/UserPortalHomeEn" : "/UserPortalHome") : (currentLanguage == "En" ? "/IndexEn" : "/Index"))"></a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-sm-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1">
                        @if (Context.Session.GetString("Username") == null)
                        {
                            <li class="nav-item ms-3">
                                <a class="nav-link" href="/@(currentLanguage == "En" ? "IndexEn" : "Index")">@translations["Home"]</a>
                            </li>
                        }
                        @if (Context.Session.GetString("Username") != null)
                        {
                            <li class="nav-item ms-3">
                                <a class="nav-link" href="/@(currentLanguage == "En" ? "UserPortalHomeEn" : "UserPortalHome")">@translations["Start"]</a>
                            </li>
                            
                            @if (Context.Session.GetString("Username") != null)
                            {
                                var toolPages = new[] { "OpstiniMk", "KontrolaPristap", "ListaDejnosti", "Ekspozituri" };
                                var hasAnyAccessiblePages = toolPages.Any(page => accessiblePages.Contains(page)) || 
                                                              systemPages.Any(page => accessiblePages.Contains(page));

                                @if (clientPages.Any(page => accessiblePages.Contains(page)))
                                {
                                    <li class="nav-item dropdown ms-3">
                                        <a class="nav-link dropdown" href="#" id="clientsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            @translations["Clients"]
                                        </a>
                                        <ul class="dropdown-menu" aria-labelledby="clientsDropdown">
                                            @if (accessiblePages.Contains("DodajKlient"))
                                            {
                                                <li><a class="dropdown-item" href="/Klienti/DodajKlient">@translations["AddClient"]</a></li>
                                            }
                                            @if (accessiblePages.Contains("ListaKlienti"))
                                            {
                                                <li><a class="dropdown-item" href="/Klienti/ListaKlienti">@translations["ClientsList"]</a></li>
                                            }
                                        </ul>
                                    </li>
                                }
                                @if (analysisPages.Any(page => accessiblePages.Contains(page)))
                                {
                                    <li class="nav-item dropdown ms-3">
                                        <a class="nav-link dropdown" href="#" id="analysisDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            Анализа на клиент
                                        </a>
                                        <ul class="dropdown-menu" aria-labelledby="analysisDropdown">
                                            @if (accessiblePages.Contains("AnalizaNaKlient"))
                                            {
                                                <li><a class="dropdown-item" href="/AnalizaNaKlient/AnalizaNaKlient">Анализа на клиент</a></li>
                                            }
                                            @if (accessiblePages.Contains("Rizici"))
                                            {
                                                <li><a class="dropdown-item" href="/AnalizaNaKlient/Rizici">Анализа на клиент - Управување</a></li>
                                            }
                                        </ul>
                                    </li>
                                }
                                @if (finansiiPages.Any(page => accessiblePages.Contains(page)))
                                {
                                    <li class="nav-item dropdown ms-3">
                                        <a class="nav-link dropdown" href="#" id="finansiiDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            @translations["Finances"]
                                        </a>
                                        <ul class="dropdown-menu" aria-labelledby="finansiiDropdown">
                                            @if (accessiblePages.Contains("Izvodi"))
                                            {
                                                <li><a class="dropdown-item" href="/Finansii/Izvodi">@translations["Statements"]</a></li>
                                            }
                                            @if (accessiblePages.Contains("Uplati"))
                                            {
                                                <li><a class="dropdown-item" href="/Finansii/Uplati">@translations["Payments"]</a></li>
                                            }
                                            @if (accessiblePages.Contains("KasovIzvestaj"))
                                            {
                                                <li><a class="dropdown-item" href="/Finansii/KasovIzvestaj">@translations["KasovIzvestaj"]</a></li>
                                            }
                                            @if (accessiblePages.Contains("PrenosNaNaplatenaPremijaListaSpecifikacii"))
                                            {
                                                <li><a class="dropdown-item" href="/Finansii/PrenosNaNaplatenaPremijaListaSpecifikacii">Пренос на наплатена премија - Сите влезни фактури</a></li>
                                            }
                                            @if (accessiblePages.Contains("ListaFakturiKonOsiguritelProvizija"))
                                            {
                                                <li><a class="dropdown-item" href="/Finansii/ListaFakturiKonOsiguritelProvizija">Листа на фактури за провизија кон осигурител</a></li>
                                            }
                                            @if (accessiblePages.Contains("GenFakturiProvizijaPoSpecifikacijaOdOsiguritel"))
                                            {
                                                <li><a class="dropdown-item" href="/Finansii/GenFakturiProvizijaPoSpecifikacijaOdOsiguritel">Генерирај фактури за провизија по спецификација од осигурител</a></li>
                                            }
                                            @if (accessiblePages.Contains("PreraspredelbaNaUplati"))
                                            {
                                                <li><a class="dropdown-item" href="/Finansii/PreraspredelbaNaUplati">Прераспределба на уплати</a></li>
                                            }
                                            @if (accessiblePages.Contains("OdobruvanjeZadolzuvanje"))
                                            {
                                                <li><a class="dropdown-item" href="/Finansii/OZLista">Одобрување/Задолжување</a></li>
                                            }
                                            @if (accessiblePages.Contains("BrokerskiSlip"))
                                            {
                                                <li><a class="dropdown-item" href="/Finansii/BrokerskiSlip">Брокерски Слип</a></li>
                                            } 
                                        </ul>
                                    </li>
                                }
                                @if (autoLiabilityPages.Any(page => accessiblePages.Contains(page)))
                                {
                                    <li class="nav-item dropdown ms-3">
                                        <a class="nav-link dropdown" href="#" id="policiesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            @translations["Policies"]
                                        </a>
                                        <ul class="dropdown-menu" aria-labelledby="policiesDropdown">
                                            @if (accessiblePages.Contains("ListaPolisi"))
                                            {
                                                <li><a class="dropdown-item" href="/Polisi/ListaPolisi">@translations["PoliciesList"]</a></li>
                                            }
                                            @if (accessiblePages.Contains("ZadolzuvanjeRazdolzuvanjePolisi"))
                                            {
                                                <li><a class="dropdown-item" href="/Polisi/ZadolzuvanjeRazdolzuvanjePolisi">Задолжување/Раздолжување</a></li>
                                            }
                                            @if (accessiblePages.Contains("ZadolzuvanjeRazdolzuvanjePolisiPersonal"))
                                            {
                                                <li><a class="dropdown-item" href="/Polisi/ZadolzuvanjeRazdolzuvanjePolisiPersonal">Задолжување/Раздолжување - Персонална</a></li>
                                            }
                                            @if (accessiblePages.Contains("DodajPolisaAO"))
                                            {
                                                <li class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">@translations["AutoLiability"]</a>
                                                    <ul class="dropdown-menu dropdown-menu-start">
                                                        <li><a class="dropdown-item" href="/Polisi/DodajPolisaAO">@translations["AddPolicy"]</a></li>
                                                        <li><a class="dropdown-item" href="/OCR/OCRAvtoOdgovornostText">Автоматско вчитување полиса</a></li>
                                                    </ul>
                                                </li>
                                            }
                                            @if (accessiblePages.Contains("DodajPolisaGR"))
                                            {
                                                <li class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">Граничко осигурување</a>
                                                    <ul class="dropdown-menu dropdown-menu-start">
                                                        <li><a class="dropdown-item" href="/Polisi/DodajPolisaGR">@translations["AddPolicy"]</a></li>
                                                    </ul>
                                                </li>
                                            }
                                            @if (accessiblePages.Contains("DodajPolisaCMR"))
                                            {
                                                <li class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">ЦМР</a>
                                                    <ul class="dropdown-menu dropdown-menu-start">
                                                        <li><a class="dropdown-item" href="/Polisi/DodajPolisaCMR">@translations["AddPolicy"]</a></li>
                                                    </ul>
                                                </li>
                                            }
                                            @if (accessiblePages.Contains("DodajPolisaAsistencija"))
                                            {
                                                <li class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">Асистенција - Класа 10</a>
                                                    <ul class="dropdown-menu dropdown-menu-start">
                                                        <li><a class="dropdown-item" href="/Polisi/DodajPolisaAsistencija">@translations["AddPolicy"]</a></li>
                                                    </ul>
                                                </li>
                                            }
                                            @if (accessiblePages.Contains("DodajPolisaZK"))
                                            {
                                                <li class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">@translations["GreenCard"]</a>
                                                    <ul class="dropdown-menu dropdown-menu-start">
                                                        <li><a class="dropdown-item" href="/Polisi/DodajPolisaZK">@translations["AddPolicy"]</a></li>
                                                    </ul>
                                                </li>
                                            }
                                            @if (accessiblePages.Contains("DodajPolisaKasko"))
                                            {
                                                <li class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">Каско осигурување</a>
                                                    <ul class="dropdown-menu dropdown-menu-start">
                                                        <li><a class="dropdown-item" href="/Polisi/DodajPolisaKasko">@translations["AddPolicy"]</a></li>
                                                    </ul>
                                                </li>
                                            }
                                            @if (accessiblePages.Contains("DodajPolisaKlasa1"))
                                            {
                                                <li class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">Незгода / несреќен случај</a>
                                                    <ul class="dropdown-menu dropdown-menu-start">
                                                        <li><a class="dropdown-item" href="/Polisi/DodajPolisaKlasa1">Додај полиса</a></li>
                                                    </ul>
                                                </li>
                                            }
                                            @if (accessiblePages.Contains("DodajPolisaKlasa18"))
                                            {
                                                <li class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">Патничко осигурување</a>
                                                    <ul class="dropdown-menu dropdown-menu-start">
                                                        <li><a class="dropdown-item" href="/Polisi/DodajPolisaKlasa18">Додај полиса</a></li>
                                                    </ul>
                                                </li>
                                            }
                                            @if (accessiblePages.Contains("DodajPolisaKlasa8"))
                                            {
                                                <li class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">Имот - пожар и природни непогоди</a>
                                                    <ul class="dropdown-menu dropdown-menu-start">
                                                        <li><a class="dropdown-item" href="/Polisi/DodajPolisaKlasa8">Додај полиса</a></li>
                                                    </ul>
                                                </li>
                                            }
                                            @if (accessiblePages.Contains("DodajPolisaKlasa9"))
                                            {
                                                <li class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">Имот - други осигурувања</a>
                                                    <ul class="dropdown-menu dropdown-menu-start">
                                                        <li><a class="dropdown-item" href="/Polisi/DodajPolisaKlasa9">Додај полиса</a></li>
                                                    </ul>
                                                </li>
                                            }
                                            @if (accessiblePages.Contains("DodajPolisaKlasa2"))
                                            {
                                                <li class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">Здравствено осигурување</a>
                                                    <ul class="dropdown-menu dropdown-menu-start">
                                                        <li><a class="dropdown-item" href="/Polisi/DodajPolisaKlasa2">Додај полиса</a></li>
                                                    </ul>
                                                </li>
                                            }
                                            @if (accessiblePages.Contains("DodajPolisaKlasa19"))
                                            {
                                                <li class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">Осигурување Живот</a>
                                                    <ul class="dropdown-menu dropdown-menu-start">
                                                        <li><a class="dropdown-item" href="/Polisi/DodajPolisaKlasa19">Додај полиса</a></li>
                                                    </ul>
                                                </li>
                                            }
                                            @if (accessiblePages.Contains("DodajPolisaKlasa4"))
                                            {
                                                <li class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">Осигурување на шински возила</a>
                                                    <ul class="dropdown-menu dropdown-menu-start">
                                                        <li><a class="dropdown-item" href="/Polisi/DodajPolisaKlasa4">Додај полиса</a></li>
                                                    </ul>
                                                </li>
                                            }
                                            @if (accessiblePages.Contains("DodajPolisaKlasa5"))
                                            {
                                                <li class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">Осигурување на воздухоплови</a>
                                                    <ul class="dropdown-menu dropdown-menu-start">
                                                        <li><a class="dropdown-item" href="/Polisi/DodajPolisaKlasa5">Додај полиса</a></li>
                                                    </ul>
                                                </li>
                                            }
                                            @if (accessiblePages.Contains("DodajPolisaKlasa6"))
                                            {
                                                <li class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">Осигурување на пловни објекти</a>
                                                    <ul class="dropdown-menu dropdown-menu-start">
                                                        <li><a class="dropdown-item" href="/Polisi/DodajPolisaKlasa6">Додај полиса</a></li>
                                                    </ul>
                                                </li>
                                            }
                                            @if (accessiblePages.Contains("DodajPolisaKlasa7"))
                                            {
                                                <li class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">Осигурување на стока во превоз</a>
                                                    <ul class="dropdown-menu dropdown-menu-start">
                                                        <li><a class="dropdown-item" href="/Polisi/DodajPolisaKlasa7">Додај полиса</a></li>
                                                    </ul>
                                                </li>
                                            }
                                            @if (accessiblePages.Contains("DodajPolisaKlasa11"))
                                            {
                                                <li class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">Осигурување од одговорност од употреба на воздухоплови</a>
                                                    <ul class="dropdown-menu dropdown-menu-start">
                                                        <li><a class="dropdown-item" href="/Polisi/DodajPolisaKlasa11">Додај полиса</a></li>
                                                    </ul>
                                                </li>
                                            }
                                            @if (accessiblePages.Contains("DodajPolisaKlasa12"))
                                            {
                                                <li class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">Осигурување од одговорност од употреба на пловни објекти</a>
                                                    <ul class="dropdown-menu dropdown-menu-start">
                                                        <li><a class="dropdown-item" href="/Polisi/DodajPolisaKlasa12">Додај полиса</a></li>
                                                    </ul>
                                                </li>
                                            }
                                            @if (accessiblePages.Contains("DodajPolisaKlasa13"))
                                            {
                                                <li class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">Општо осигурување од одговорност</a>
                                                    <ul class="dropdown-menu dropdown-menu-start">
                                                        <li><a class="dropdown-item" href="/Polisi/DodajPolisaKlasa13">Додај полиса</a></li>
                                                    </ul>
                                                </li>
                                            }
                                            @if (accessiblePages.Contains("DodajPolisaKlasa14"))
                                            {
                                                <li class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">Осигурување на кредити</a>
                                                    <ul class="dropdown-menu dropdown-menu-start">
                                                        <li><a class="dropdown-item" href="/Polisi/DodajPolisaKlasa14">Додај полиса</a></li>
                                                    </ul>
                                                </li>
                                            }
                                            @if (accessiblePages.Contains("DodajPolisaKlasa15"))
                                            {
                                                <li class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">Осигурување на гаранции</a>
                                                    <ul class="dropdown-menu dropdown-menu-start">
                                                        <li><a class="dropdown-item" href="/Polisi/DodajPolisaKlasa15">Додај полиса</a></li>
                                                    </ul>
                                                </li>
                                            }
                                            @if (accessiblePages.Contains("DodajPolisaKlasa16"))
                                            {
                                                <li class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">Осигурување од финансиски загуби</a>
                                                    <ul class="dropdown-menu dropdown-menu-start">
                                                        <li><a class="dropdown-item" href="/Polisi/DodajPolisaKlasa16">Додај полиса</a></li>
                                                    </ul>
                                                </li>
                                            }
                                            @if (accessiblePages.Contains("DodajPolisaKlasa17"))
                                            {
                                                <li class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">Осигурување на правна заштита</a>
                                                    <ul class="dropdown-menu dropdown-menu-start">
                                                        <li><a class="dropdown-item" href="/Polisi/DodajPolisaKlasa17">Додај полиса</a></li>
                                                    </ul>
                                                </li>
                                            }
                                            @if (accessiblePages.Contains("DodajPolisaKlasa21"))
                                            {
                                                <li class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">Осигурување на живот во врска со удели во инвестициони фондови</a>
                                                    <ul class="dropdown-menu dropdown-menu-start">
                                                        <li><a class="dropdown-item" href="/Polisi/DodajPolisaKlasa21">Додај полиса</a></li>
                                                    </ul>
                                                </li>
                                            }

                                        </ul>
                                    </li>
                                }
                                @if (reportsPages.Any(page => accessiblePages.Contains(page)))
                                {
                                    <li class="nav-item dropdown ms-3">
                                        <a class="nav-link dropdown" href="#" id="reportsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            @translations["Reports"]
                                        </a>
                                        <ul class="dropdown-menu" aria-labelledby="reportsDropdown">
                                            @if (accessiblePages.Contains("SostojbaNaDolgPoDogovoruvac"))
                                            {
                                                <li><a class="dropdown-item" href="/Pregledi/SostojbaNaDolgPoDogovoruvac">@translations["DebtStatusByContractor"]</a></li>
                                            }
                                            @if (accessiblePages.Contains("ASOMesecenIzvestaj"))
                                            {
                                                <li><a class="dropdown-item" href="/Pregledi/ASOMesecenIzvestaj">АСО Месечен Извештај</a></li>
                                            }
                                            @if (accessiblePages.Contains("ASOMesecenIzvestajSintetika"))
                                            {
                                                <li><a class="dropdown-item" href="/Pregledi/ASOMesecenIzvestajSintetika">АСО Месечен Извештај - Синтетика</a></li>
                                            }
                                            @if (accessiblePages.Contains("ASOKvartalenIzvestaj"))
                                            {
                                                <li><a class="dropdown-item" href="/Pregledi/ASOKvartalenIzvestaj">АСО Квартален Извештај</a></li>
                                            }
                                            @if (accessiblePages.Contains("ASORealiziranaProvizija"))
                                            {
                                                <li><a class="dropdown-item" href="/Pregledi/ASORealiziranaProvizija">АСО Реализирана Провизија</a></li>
                                            }
                                            @if (accessiblePages.Contains("ASOKvartalenOBD1"))
                                            {
                                                <li><a class="dropdown-item" href="/Pregledi/ASOKvartalenOBD1">АСО Квартален ОБД1</a></li>
                                            }
                                            @if (accessiblePages.Contains("ASOKvartalenOBD2"))
                                            {
                                                <li><a class="dropdown-item" href="/Pregledi/ASOKvartalenOBD2">АСО Квартален ОБД2</a></li>
                                            }
                                            @if (accessiblePages.Contains("IzvestajProvizijaBrokerMesecen"))
                                            {
                                                <li><a class="dropdown-item" href="/Pregledi/IzvestajProvizijaBrokerMesecen">Извештај за провизија за  брокер - влезни фактури кон брокер</a></li>
                                            }
                                            @if (accessiblePages.Contains("IzvestajProvizijaBrokerMesecen"))
                                            {
                                                <li><a class="dropdown-item" href="/Pregledi/IzvestajProvizijaBrokeriMesecenVlezniFakturiKonKlient">Извештај за провизија за  брокер - влезни фактури кон клиент</a></li>
                                            }
                                            @if (accessiblePages.Contains("IzvestajGeneriraniIzlezniFakturiKonKlient"))
                                            {
                                                <li><a class="dropdown-item" href="/Pregledi/IzvestajGeneriraniIzlezniFakturiKonKlient">Извештај за генерирани излезни фактури кон клиент</a></li>
                                            }
                                            @if (accessiblePages.Contains("IzvestajPrenosNaNaplataVlezniFakturi"))
                                            {
                                                <li><a class="dropdown-item" href="/Pregledi/IzvestajPrenosNaNaplataVlezniFakturi">Извештај пренос на наплатa - сите фактури</a></li>
                                            }
                                            @if (accessiblePages.Contains("IzvestajZaSkluceniPolisi"))
                                            {
                                                <li><a class="dropdown-item" href="/Pregledi/IzvestajZaSkluceniPolisi">Извештај за склучени полиси</a></li>
                                            }
                                            @if (accessiblePages.Contains("IzvestajBankarskaProvizija"))
                                            {
                                                <li><a class="dropdown-item" href="/Pregledi/IzvestajBankarskaProvizija">Извештај Банкарска Провизија</a></li>
                                            }
                                            @if (accessiblePages.Contains("IzvestajZaNepostoeckiSetiranjaProvizija"))
                                            {
                                                <li><a class="dropdown-item" href="/Pregledi/IzvestajZaNepostoeckiSetiranjaProvizija">Извештај за непостоечки сетирања за брокерска провизија</a></li>
                                            }
                                        </ul>
                                    </li>
                                }
                                @if (provizijaPages.Any(page => accessiblePages.Contains(page)))
                                {
                                    <li class="nav-item dropdown ms-3">
                                        <a class="nav-link dropdown" href="#" id="provizijaDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            Провизија
                                        </a>
                                        <ul class="dropdown-menu" aria-labelledby="provizijaDropdown">
                                            @if (accessiblePages.Contains("ProvizijaSetiranje"))
                                            {
                                                <li><a class="dropdown-item" href="/Provizija/ProvizijaSetiranje">Сетирање на провизија</a></li>
                                            }
                                            @if (accessiblePages.Contains("PresmetkaNaProvizija"))
                                            {
                                                <li><a class="dropdown-item" href="/Provizija/PresmetkaNaProvizija">Пресметка на провизија</a></li>
                                            }
                                        </ul>
                                    </li>
                                }
                                @if (hasAnyAccessiblePages)
                                {
                                    <li class="nav-item dropdown ms-3">
                                        <a class="nav-link dropdown" href="#" id="administrationDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            @translations["Administration"]
                                        </a>
                                        <ul class="dropdown-menu" aria-labelledby="administrationDropdown">
                                            @if (toolPages.Any(page => accessiblePages.Contains(page)))
                                            {
                                                <li class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">@translations["Tools"]</a>
                                                    <ul class="dropdown-menu dropdown-menu-start">
                                                        @if (accessiblePages.Contains("OpstiniMk"))
                                                        {
                                                            <li><a class="dropdown-item" href="/AdministrationPages/OpstiniMk">@translations["Municipalities"]</a></li>
                                                        }
                                                        @if (accessiblePages.Contains("KontrolaPristap"))
                                                        {
                                                            <li><a class="dropdown-item" href="/AdministrationPages/KontrolaPristap">@translations["AccessControl"]</a></li>
                                                        }
                                                        @if (accessiblePages.Contains("ListaDejnosti"))
                                                        {
                                                            <li><a class="dropdown-item" href="/AdministrationPages/ListaDejnosti">@translations["Activities"]</a></li>
                                                        }
                                                        @if (accessiblePages.Contains("Ekspozituri"))
                                                        {
                                                            <li><a class="dropdown-item" href="/AdministrationPages/Ekspozituri">@translations["Branches"]</a></li>
                                                        }
                                                    </ul>
                                                </li>
                                            }
                                            
                                            @if (systemPages.Any(page => accessiblePages.Contains(page)))
                                            {
                                                <li class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">@translations["Pages"]</a>
                                                    <ul class="dropdown-menu dropdown-menu-start">
                                                        @if (accessiblePages.Contains("KlasiOsiguruvanje"))
                                                        {
                                                            <li><a class="dropdown-item" href="/AdministrationPages/KlasiOsiguruvanje">@translations["InsuranceClasses"]</a></li>
                                                        }

                                                        @if (accessiblePages.Contains("Produkti"))
                                                        {
                                                            <li><a class="dropdown-item" href="/AdministrationPages/Produkti">@translations["Products"]</a></li>
                                                        }

                                                        @if (accessiblePages.Contains("SifrarnikBankiSmetki"))
                                                        {
                                                            <li><a class="dropdown-item" href="/AdministrationPages/SifrarnikBankiSmetki">Банки и сметки</a></li>
                                                        }
                                                    </ul>
                                                </li>
                                            }
                                        </ul>
                                    </li>
                                }
                            }
                        }
                        else
                        {
                           /* HERE WE CAN PUT LINKS THAT APPEAR ONLY WHEN USER IS NOT LOGGED IN TO THE SYSTEM!!!*/
                        }
                    </ul>
                    
                    <!-- Right-aligned authentication dropdown -->
                    <ul class="navbar-nav ms-auto">
                        <!-- Language switcher -->
                        <!--
                        <li class="nav-item me-3 d-flex align-items-center">
                            <a href="/LanguageSwitch?lang=Mk" class="language-link @(currentLanguage == "Mk" ? "active" : "")">
                                MK
                            </a>
                            <span class="mx-2">|</span>
                            <a href="/LanguageSwitch?lang=En" class="language-link @(currentLanguage == "En" ? "active" : "")">
                                EN
                            </a>
                        </li>
                        -->

                        <!-- Update the notification bell HTML -->
                        @if (Context.Session.GetString("Username") != null)
                        {
                            <li class="nav-item me-3 notification-bell">
                                <a class="nav-link" href="/Notifications/UserNotifications">
                                    <i class="fas fa-bell"></i>
                                    <span id="unreadNotificationsCount" class="notification-badge badge rounded-pill bg-danger d-none">
                                        0
                                    </span>
                                </a>
                            </li>
                        }

                        <!-- User dropdown -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown" href="#" id="authDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user me-1"></i>@(Context.Session.GetString("Username") ?? translations["User"])
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="authDropdown">
                                @if (Context.Session.GetString("Username") != null)
                                {
                                    <li><a class="dropdown-item" href="/Logout">@translations["Logout"]</a></li>
                                }
                                else
                                {
                                    <li><a class="dropdown-item" href="/@(currentLanguage == "En" ? "LoginEn" : "Login")">@translations["Login"]</a></li>
                                    <li><a class="dropdown-item" href="/@(currentLanguage == "En" ? "RegistrationEn" : "Registration")">@translations["Register"]</a></li>
                                }
                            </ul>
                        </li>
                    </ul>
                </div>                
            </div>
        </nav>
    </header>
    <main role="main">
        @RenderBody()
    </main>

    <div class="bottom-bar">
        &copy; @DateTime.Now.Year - NextBroker - Powered by NextByte 
        <!--<a class="footer-link" href="/@(currentLanguage == "En" ? "CestiPrasanjaEn/CestiPrasanjaEn" : "CestiPrasanjaMk/CestiPrasanja")">@translations["AskQuestion"]</a> -->
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- DataTables -->
    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.js"></script>

    <!-- js-cookie library -->
    <script src="https://cdn.jsdelivr.net/npm/js-cookie@3.0.1/dist/js.cookie.min.js"></script>

    <!-- Custom site scripts -->
    <script src="~/js/site.js" asp-append-version="true"></script>

    @await RenderSectionAsync("Scripts", required: false)

    <script>
        $(document).ready(function() {
            // Initialize Bootstrap dropdowns
            $('[data-bs-toggle="dropdown"]').dropdown();
        });
    </script>

    <script>
    document.addEventListener('DOMContentLoaded', function () {
        // Handle nested dropdowns
        document.querySelectorAll('.dropdown-submenu > a.dropdown-toggle').forEach(function(element) {
            element.addEventListener('click', function(e) {
                e.stopPropagation();
                e.preventDefault();
                
                // Close all other submenus at the same level
                let siblings = this.closest('.dropdown-menu').querySelectorAll('.dropdown-submenu .dropdown-menu');
                siblings.forEach(function(menu) {
                    if (menu !== e.target.nextElementSibling) {
                        menu.style.display = 'none';
                    }
                });

                // Toggle current submenu
                const submenu = this.nextElementSibling;
                if (submenu.style.display === 'block') {
                    submenu.style.display = 'none';
                } else {
                    submenu.style.display = 'block';
                }
               
                // Position the submenu
                if (submenu.classList.contains('dropdown-menu')) {
                    const rect = this.getBoundingClientRect();
                    submenu.style.top = '0';
                    submenu.style.left = '100%';
                }
            });
        });

        // Close submenus when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.dropdown-submenu')) {
                document.querySelectorAll('.dropdown-submenu .dropdown-menu').forEach(function(menu) {
                    menu.style.display = 'none';
                });
            }
        });
        
        // Add CSS for proper submenu positioning
        const style = document.createElement('style');
        style.textContent = `
            .dropdown-submenu {
                position: relative;
            }
            .dropdown-submenu > .dropdown-menu {
                position: absolute;
                top: 0;
                left: 100%;
                margin-top: 0;
            }
        `;
        document.head.appendChild(style);
    });
    </script>

    @if (Context.Session.GetString("Username") != null)
    {
        <script>
            // Set up SignalR connection for notifications
            const notificationConnection = new signalR.HubConnectionBuilder()
                .withUrl("/notificationHub")
                .withAutomaticReconnect()
                .build();

            async function updateUnreadCount() {
                try {
                    const response = await fetch('/Notifications/UserNotifications?handler=UnreadCount');
                    if (response.ok) {
                        const count = await response.json();
                        const countElement = document.getElementById('unreadNotificationsCount');
                        if (count > 0) {
                            countElement.textContent = count;
                            countElement.classList.remove('d-none');
                        } else {
                            countElement.classList.add('d-none');
                        }
                    }
                } catch (error) {
                    console.error('Error updating unread count:', error);
                }
            }

            notificationConnection.on("ReceiveNotifications", updateUnreadCount);

            notificationConnection.start()
                .then(() => {
                    console.log("Notification connection established");
                    updateUnreadCount();
                })
                .catch(err => console.error("Error establishing notification connection:", err));
        </script>
    }
</body>
</html>
