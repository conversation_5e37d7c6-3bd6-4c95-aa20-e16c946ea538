using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.ComponentModel.DataAnnotations;
using Microsoft.Data.SqlClient;
using System.Data;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.Text;

namespace NextBroker.Pages.Provizija
{
    public class PresmetkaNaProvizija : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public PresmetkaNaProvizija(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
            
            // Initialize default dates
            DatumOd = DateTime.Today.AddMonths(-1);
            DatumDo = DateTime.Today;
            ReportDatumOd = DateTime.Today.AddMonths(-1);
            ReportDatumDo = DateTime.Today;
        }

        // Properties to bind with form for Presmetka section
        [BindProperty]
        public string SellerIds { get; set; }

        [BindProperty]
        public string PolisiIds { get; set; }

        [BindProperty]
        public string PolicySelectionMode { get; set; } = "auto";

        [BindProperty]
        [DataType(DataType.Date)]
        public DateTime DatumOd { get; set; }

        [BindProperty]
        [DataType(DataType.Date)]
        public DateTime DatumDo { get; set; }
        
        // Properties for Report section
        [BindProperty]
        public string ReportSellerIds { get; set; }
        
        [BindProperty]
        [DataType(DataType.Date)]
        public DateTime ReportDatumOd { get; set; }
        
        [BindProperty]
        [DataType(DataType.Date)]
        public DateTime ReportDatumDo { get; set; }
        
        public bool ReportGenerated { get; set; } = false;
        public int ReportTotalItems { get; set; } = 0;

        // Properties for displaying results
        public bool ProcessingComplete { get; set; } = false;
        public int TotalPolisi { get; set; }
        public int TotalSellers { get; set; }
        public int SuccessfullyProcessed { get; set; }
        public bool HasErrors { get; set; }
        public string ErrorMessages { get; set; }
        public int? CurrentBatchId { get; set; } // Store the batch ID for the current calculation
        
        // Property for displaying the data table
        public DataTable ProvizijaPodatoci { get; set; }

        // List of sellers for dropdown (kept for backward compatibility)
        public List<SellerModel> SellersList { get; set; } = new List<SellerModel>();

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("PresmetkaNaProvizija"))
            {
                return RedirectToAccessDenied();
            }

            await LoadSellers();
            ReportGenerated = false;
            return Page();
        }

        public async Task<IActionResult> OnPost()
        {
            // Clear any previous validation errors for PolisiIds and ReportSellerIds
            ModelState.Remove("PolisiIds");
            ModelState.Remove("ReportSellerIds");

            if (!await HasPageAccess("PresmetkaNaProvizija"))
            {
                return RedirectToAccessDenied();
            }

            // Always load sellers to ensure the dropdown works
            await LoadSellers();
            
            // Reset report generation status
            ReportGenerated = false;

            // Only validate manually selected policies if in manual mode
            if (PolicySelectionMode == "manual")
            {
                if (string.IsNullOrWhiteSpace(PolisiIds))
                {
                    ModelState.AddModelError("PolisiIds", "Изберете барем една полиса");
                    return Page();
                }
            }
            else
            {
                // In auto mode, we'll fetch policies based on date range
                PolisiIds = await GetPoliciesByDateRange(DatumOd, DatumDo);
                
                if (string.IsNullOrWhiteSpace(PolisiIds))
                {
                    ModelState.AddModelError("PolisiIds", "Нема пронајдени полиси за избраниот период");
                    return Page();
                }
                
                // Log the number of policies found for debugging
                Console.WriteLine($"Auto mode: Found {PolisiIds.Split(',').Length} policies for period {DatumOd:yyyy-MM-dd} to {DatumDo:yyyy-MM-dd}");
            }

            if (string.IsNullOrWhiteSpace(SellerIds))
            {
                ModelState.AddModelError("SellerIds", "Изберете барем еден соработник");
                return Page();
            }

            if (!ModelState.IsValid)
            {
                return Page();
            }

            // Process the calculation
            await ProcessProvisionCalculation();
            
            // Add a small delay to ensure all DB operations have completed
            await Task.Delay(1000);
            
            // After processing, fetch the data for display
            await LoadProvizijaPodatoci();
            
            return Page();
        }
        
        public async Task<IActionResult> OnGetExportExcelAsync(DateTime DatumOd, DateTime DatumDo, string ClientTypes = null, bool HasNadreden = false, string NadredenNivo = null)
        {
            try
            {
                // Create DataTable to hold the report data
                DataTable exportData = new DataTable();
                
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    
                    // Base query for data
                    string baseQuery = @"
                        SELECT 
                            pps.BrojNaSpecifikacijaProvizija,
                            pps.KlientProvizijaId,
                            CASE 
                                WHEN klient_primac.Naziv IS NOT NULL THEN klient_primac.Naziv
                                ELSE CONCAT(klient_primac.Ime, ' ', klient_primac.Prezime)
                            END AS PrimacProvizija,
                            pps.EMBGMBPrimacProvizija,
                            CASE 
                                WHEN dogovoruvac.Naziv IS NOT NULL THEN dogovoruvac.Naziv
                                ELSE CONCAT(dogovoruvac.Ime, ' ', dogovoruvac.Prezime)
                            END AS DogovoruvacImePrezime,
                            pps.EMBGMBKlientProvizija,
                            CASE 
                                WHEN osiguritel.Naziv IS NOT NULL THEN osiguritel.Naziv
                                ELSE CONCAT(osiguritel.Ime, ' ', osiguritel.Prezime)
                            END AS OsiguritelNaziv,
                            pps.BrojNaPolisa,
                            klasa.KlasaIme AS KlasaImeOsiguruvanje,
                            produkt.Ime AS ProduktIme,
                            pps.DatumNaPresmetkaOd,
                            pps.DatumNaPresmetkaDo,
                            pps.DateCreated AS DatumNaPresmetkaNaPremija,
                            pps.SifrarnikNacinPlakanjeId,
                            pps.Godina,
                            nacin_presmetka.Nacin AS NacinNaPresmetka,
                            pps.IznosZaPresmetka,
                            pps.DatumNaPremijaZaPresmetka,
                            pps.BrojNaVleznaFaktura,
                            pps.IznosProvizijaBruto,
                            pps.ProcentDanok,
                            pps.IznosProvizijaNeto,
                            pps.StapkaZaProvizija,
                            CASE 
                                WHEN sorabotnik_prvo_nivo.Naziv IS NOT NULL THEN sorabotnik_prvo_nivo.Naziv
                                ELSE CONCAT(sorabotnik_prvo_nivo.Ime, ' ', sorabotnik_prvo_nivo.Prezime)
                            END AS SorabotnikPrvoNivo,
                            pps.NadredenStavka
                        FROM 
                            dbo.ProvizijaPresmetkaStavki pps
                        LEFT JOIN 
                            dbo.Klienti osiguritel ON pps.KlientiIdOsiguritel = osiguritel.Id
                        LEFT JOIN 
                            dbo.Klienti klient ON pps.KlientProvizijaId = klient.Id
                        LEFT JOIN
                            dbo.Klienti klient_primac ON pps.KlientiIdKlient = klient_primac.Id
                        LEFT JOIN
                            dbo.KlasiOsiguruvanje klasa ON pps.KlasiOsiguruvanjeIdKlasa = klasa.Id
                        LEFT JOIN
                            dbo.Produkti produkt ON pps.ProizvodId = produkt.Id
                        LEFT JOIN
                            dbo.SifrarnikNacinNaPresmetkaProvizija nacin_presmetka ON pps.SifrarnikNacinNaPresmetkaProvizijaId = nacin_presmetka.Id
                        LEFT JOIN
                            dbo.Klienti sorabotnik_prvo_nivo ON pps.KlientiIdSorabotnikPrvoNivoPolisa = sorabotnik_prvo_nivo.Id
                        LEFT JOIN
                            dbo.Klienti dogovoruvac ON pps.DogovoruvacIdKlient = dogovoruvac.Id
                        WHERE 1=1";
                    
                    // Add batch filter if available (prioritize current batch over time-based filter)
                    if (CurrentBatchId.HasValue)
                    {
                        string currentYear = DateTime.Now.Year.ToString();
                        baseQuery += $" AND pps.BrojNaSpecifikacijaProvizija LIKE '_/{CurrentBatchId.Value}/{currentYear}'";
                    }
                    else
                    {
                        // Fallback to time-based filter if no batch ID available
                        baseQuery += " AND pps.DateCreated >= DATEADD(minute, -60, GETDATE())";
                    }
                    
                    // Add client type filter if provided
                    string clientTypeFilter = string.Empty;
                    if (!string.IsNullOrEmpty(ClientTypes))
                    {
                        var clientTypesList = ClientTypes.Split(',').ToList();
                        if (clientTypesList.Any())
                        {
                            clientTypeFilter = " AND pps.KlientProvizijaId IN (";
                            for (int i = 0; i < clientTypesList.Count; i++)
                            {
                                if (i > 0) clientTypeFilter += ",";
                                clientTypeFilter += "@ClientType" + i;
                            }
                            clientTypeFilter += ")";
                            baseQuery += clientTypeFilter;
                        }
                    }
                    
                    // Add nadreden filter if requested
                    if (HasNadreden)
                    {
                        baseQuery += " AND pps.NadredenStavka > 0";
                        
                        // Add specific level filter if provided
                        if (!string.IsNullOrEmpty(NadredenNivo))
                        {
                            baseQuery += " AND pps.NadredenStavka = @NadredenNivo";
                        }
                    }
                    
                    baseQuery += @" ORDER BY 
                            pps.DateCreated DESC, pps.Id DESC";
                    
                    using (SqlCommand command = new SqlCommand(baseQuery, connection))
                    {
                        // Add client type parameters if needed
                        if (!string.IsNullOrEmpty(ClientTypes))
                        {
                            var clientTypesList = ClientTypes.Split(',').ToList();
                            for (int i = 0; i < clientTypesList.Count; i++)
                            {
                                command.Parameters.AddWithValue("@ClientType" + i, clientTypesList[i]);
                            }
                        }
                        
                        // Add nadreden level parameter if needed
                        if (HasNadreden && !string.IsNullOrEmpty(NadredenNivo))
                        {
                            command.Parameters.AddWithValue("@NadredenNivo", NadredenNivo);
                        }
                        
                        using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                        {
                            adapter.Fill(exportData);
                        }
                    }
                    
                                            // If no data found with date range, try with just the recent records
                        if (exportData.Rows.Count == 0)
                        {
                            string fallbackQuery = @"
                                SELECT 
                                    pps.BrojNaSpecifikacijaProvizija,
                                    pps.KlientProvizijaId,
                                    CASE 
                                        WHEN klient_primac.Naziv IS NOT NULL THEN klient_primac.Naziv
                                        ELSE CONCAT(klient_primac.Ime, ' ', klient_primac.Prezime)
                                    END AS PrimacProvizija,
                                    pps.EMBGMBPrimacProvizija,
                                    CASE 
                                        WHEN dogovoruvac.Naziv IS NOT NULL THEN dogovoruvac.Naziv
                                        ELSE CONCAT(dogovoruvac.Ime, ' ', dogovoruvac.Prezime)
                                    END AS DogovoruvacImePrezime,
                                    pps.EMBGMBKlientProvizija,
                                    CASE 
                                        WHEN osiguritel.Naziv IS NOT NULL THEN osiguritel.Naziv
                                        ELSE CONCAT(osiguritel.Ime, ' ', osiguritel.Prezime)
                                    END AS OsiguritelNaziv,
                                    pps.BrojNaPolisa,
                                    klasa.KlasaIme AS KlasaImeOsiguruvanje,
                                    produkt.Ime AS ProduktIme,
                                    pps.DatumNaPresmetkaOd,
                                    pps.DatumNaPresmetkaDo,
                                    pps.DateCreated AS DatumNaPresmetkaNaPremija,
                                    pps.SifrarnikNacinPlakanjeId,
                                    pps.Godina,
                                    nacin_presmetka.Nacin AS NacinNaPresmetka,
                                    pps.IznosZaPresmetka,
                                    pps.DatumNaPremijaZaPresmetka,
                                    pps.BrojNaVleznaFaktura,
                                    pps.IznosProvizijaBruto,
                                    pps.ProcentDanok,
                                    pps.IznosProvizijaNeto,
                                    pps.StapkaZaProvizija,
                                    CASE 
                                        WHEN sorabotnik_prvo_nivo.Naziv IS NOT NULL THEN sorabotnik_prvo_nivo.Naziv
                                        ELSE CONCAT(sorabotnik_prvo_nivo.Ime, ' ', sorabotnik_prvo_nivo.Prezime)
                                    END AS SorabotnikPrvoNivo,
                                    pps.NadredenStavka
                                FROM 
                                    dbo.ProvizijaPresmetkaStavki pps
                                LEFT JOIN 
                                    dbo.Klienti osiguritel ON pps.KlientiIdOsiguritel = osiguritel.Id
                                LEFT JOIN
                                    dbo.Klienti klient ON pps.KlientProvizijaId = klient.Id
                                LEFT JOIN
                                    dbo.Klienti klient_primac ON pps.KlientiIdKlient = klient_primac.Id
                                LEFT JOIN
                                    dbo.KlasiOsiguruvanje klasa ON pps.KlasiOsiguruvanjeIdKlasa = klasa.Id
                                LEFT JOIN
                                    dbo.Produkti produkt ON pps.ProizvodId = produkt.Id
                                LEFT JOIN
                                    dbo.SifrarnikNacinNaPresmetkaProvizija nacin_presmetka ON pps.SifrarnikNacinNaPresmetkaProvizijaId = nacin_presmetka.Id
                                LEFT JOIN
                                    dbo.Klienti sorabotnik_prvo_nivo ON pps.KlientiIdSorabotnikPrvoNivoPolisa = sorabotnik_prvo_nivo.Id
                                LEFT JOIN
                                    dbo.Klienti dogovoruvac ON pps.DogovoruvacIdKlient = dogovoruvac.Id
                                WHERE 
                                    pps.DateCreated >= DATEADD(minute, -60, GETDATE())
                                ORDER BY 
                                    pps.DateCreated DESC, pps.Id DESC";
                        
                        if (!string.IsNullOrEmpty(ClientTypes))
                        {
                            fallbackQuery += clientTypeFilter;
                        }
                        
                        if (HasNadreden)
                        {
                            fallbackQuery += " AND pps.NadredenStavka > 0";
                            
                            if (!string.IsNullOrEmpty(NadredenNivo))
                            {
                                fallbackQuery += " AND pps.NadredenStavka = @NadredenNivo";
                            }
                        }
                        
                        fallbackQuery += " ORDER BY pps.DateCreated DESC";
                        
                        using (SqlCommand fallbackCmd = new SqlCommand(fallbackQuery, connection))
                        {
                            // Add parameters
                            if (!string.IsNullOrEmpty(ClientTypes))
                            {
                                var clientTypesList = ClientTypes.Split(',').ToList();
                                for (int i = 0; i < clientTypesList.Count; i++)
                                {
                                    fallbackCmd.Parameters.AddWithValue("@ClientType" + i, clientTypesList[i]);
                                }
                            }
                            
                            if (HasNadreden && !string.IsNullOrEmpty(NadredenNivo))
                            {
                                fallbackCmd.Parameters.AddWithValue("@NadredenNivo", NadredenNivo);
                            }
                            
                            using (SqlDataAdapter adapter = new SqlDataAdapter(fallbackCmd))
                            {
                                adapter.Fill(exportData);
                            }
                        }
                    }
                }
                
                // Generate Excel file
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var package = new ExcelPackage())
                {
                    var worksheet = package.Workbook.Worksheets.Add("Пресметка на провизија");
                    
                    // Add header
                    worksheet.Cells["A1"].Value = "Пресметка на провизија";
                    worksheet.Cells["A1:F1"].Merge = true;
                    worksheet.Cells["A1"].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    worksheet.Cells["A1"].Style.Font.Bold = true;
                    worksheet.Cells["A1"].Style.Font.Size = 14;
                    
                    // Add date range
                    worksheet.Cells["A3"].Value = "Период:";
                    worksheet.Cells["B3"].Value = $"{DatumOd:dd.MM.yyyy} - {DatumDo:dd.MM.yyyy}";
                    
                    // Add filters info if applied
                    int infoRow = 4;
                    
                    if (!string.IsNullOrEmpty(ClientTypes))
                    {
                        var typeNames = new List<string>();
                        foreach (var type in ClientTypes.Split(','))
                        {
                            if (type == "1") typeNames.Add("Брокер");
                            if (type == "2") typeNames.Add("Соработник");
                            if (type == "3") typeNames.Add("Вработен");
                        }
                        worksheet.Cells[$"A{infoRow}"].Value = "Филтер по тип:";
                        worksheet.Cells[$"B{infoRow}"].Value = string.Join(", ", typeNames);
                        infoRow++;
                    }
                    
                    // Add nadreden filter info if applied
                    if (HasNadreden)
                    {
                        worksheet.Cells[$"A{infoRow}"].Value = "Филтер надреден:";
                        
                        if (!string.IsNullOrEmpty(NadredenNivo))
                        {
                            worksheet.Cells[$"B{infoRow}"].Value = $"Да (Ниво {NadredenNivo})";
                        }
                        else
                        {
                            worksheet.Cells[$"B{infoRow}"].Value = "Да";
                        }
                        infoRow++;
                    }
                    
                    // Add number of records
                    worksheet.Cells[$"A{infoRow}"].Value = "Вкупно записи:";
                    worksheet.Cells[$"B{infoRow}"].Value = exportData.Rows.Count;
                    infoRow++;
                    
                    // Add spacing before the table
                    int tableStartRow = infoRow + 2; // Add 2 rows of spacing
                    
                    if (exportData.Columns.Count > 0 && exportData.Rows.Count > 0)
                    {
                        // Add column headers with translated column names
                        var headerRow = tableStartRow;
                        
                        // Define columns with Macedonian names - only include the fields specified by the user
                        var columns = new Dictionary<string, string>
                        {
                            { "BrojNaSpecifikacijaProvizija", "Број на спецификација" },
                            { "KlientProvizijaId", "Клиент за провизија" },
                            { "PrimacProvizija", "Примач на провизија" },
                            { "EMBGMBPrimacProvizija", "ЕМБГ/МБ на примач на провизија" },
                            { "DogovoruvacImePrezime", "Договорувач" },
                            { "EMBGMBKlientProvizija", "ЕМБГ/МБ на клиент" },
                            { "OsiguritelNaziv", "Осигурител" },
                            { "BrojNaPolisa", "Број на полиса" },
                            { "KlasaImeOsiguruvanje", "Класа Осигурување" },
                            { "ProduktIme", "Производ" },
                            { "DatumNaPresmetkaOd", "Датум на почеток на полиса" },
                            { "DatumNaPresmetkaDo", "Датум на крај на полиса" },
                            { "DatumNaPresmetkaNaPremija", "Датум на пресметка на провизија" },
                            { "SifrarnikNacinPlakanjeId", "Начин на плаќање" },
                            { "Godina", "Година" },
                            { "NacinNaPresmetka", "Начин на пресметка" },
                            { "IznosZaPresmetka", "Износ за пресметка" },
                            { "DatumNaPremijaZaPresmetka", "Датум на наплата на премија" },
                            { "BrojNaVleznaFaktura", "Број на влезна фактура" },
                            { "IznosProvizijaBruto", "Износ провизија бруто" },
                            { "ProcentDanok", "Процент данок" },
                            { "IznosProvizijaNeto", "Износ провизија нето" },
                            { "StapkaZaProvizija", "Стапка за провизија" },
                            { "SorabotnikPrvoNivo", "Соработник нулто ниво" },
                            { "NadredenStavka", "Ниво на надреденост" }
                        };
                        
                        // Add headers
                        int colIndex = 1;
                        int clientTypeColumnIndex = -1;
                        
                        // Find KlientProvizijaId column index
                        for (int i = 0; i < exportData.Columns.Count; i++)
                        {
                            if (exportData.Columns[i].ColumnName == "KlientProvizijaId")
                            {
                                clientTypeColumnIndex = i + 1; // 1-based for Excel
                                break;
                            }
                        }
                        
                        // Add the original headers
                        foreach (DataColumn column in exportData.Columns)
                        {
                            string headerText = columns.ContainsKey(column.ColumnName) 
                                ? columns[column.ColumnName] 
                                : column.ColumnName;
                                
                            var cell = worksheet.Cells[headerRow, colIndex++];
                            cell.Value = headerText;
                            cell.Style.Font.Bold = true;
                            cell.Style.Fill.PatternType = ExcelFillStyle.Solid;
                            cell.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                            cell.Style.Border.BorderAround(ExcelBorderStyle.Thin);
                        }
                        
                        // Add data rows
                        int rowIndex = headerRow + 1;
                        foreach (DataRow row in exportData.Rows)
                        {
                            colIndex = 1;
                            foreach (var item in row.ItemArray)
                            {
                                var cell = worksheet.Cells[rowIndex, colIndex];
                                
                                // Replace client type ID with text
                                if (clientTypeColumnIndex > 0 && colIndex == clientTypeColumnIndex)
                                {
                                    if (item != null && item != DBNull.Value)
                                    {
                                        int clientTypeId;
                                        if (int.TryParse(item.ToString(), out clientTypeId))
                                        {
                                            switch (clientTypeId)
                                            {
                                                case 1:
                                                    cell.Value = "Брокерско Друштво";
                                                    break;
                                                case 2:
                                                    cell.Value = "Соработник";
                                                    break;
                                                case 3:
                                                    cell.Value = "Вработен";
                                                    break;
                                                default:
                                                    cell.Value = item;
                                                    break;
                                            }
                                        }
                                        else
                                        {
                                            cell.Value = item;
                                        }
                                    }
                                    else
                                    {
                                        cell.Value = item;
                                    }
                                }
                                else
                                {
                                    cell.Value = item;
                                }
                                
                                // Format based on data type
                                if (item is DateTime dateValue)
                                {
                                    cell.Style.Numberformat.Format = "dd/MM/yyyy HH:mm";
                                }
                                else if (item is decimal || item is double || item is float)
                                {
                                    cell.Style.Numberformat.Format = "#,##0.00";
                                }
                                else if (item is bool boolValue)
                                {
                                    cell.Value = boolValue ? "Да" : "Не";
                                }
                                
                                cell.Style.Border.BorderAround(ExcelBorderStyle.Thin);
                                colIndex++;
                            }
                            rowIndex++;
                        }
                        
                        // Auto-fit columns
                        worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
                    }
                    else
                    {
                        // If no data, add a message
                        worksheet.Cells["A" + tableStartRow].Value = "Нема податоци за прикажување во избраниот период.";
                        worksheet.Cells["A" + tableStartRow + ":F" + tableStartRow].Merge = true;
                        worksheet.Cells["A" + tableStartRow].Style.Font.Italic = true;
                        worksheet.Cells["A" + tableStartRow].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    }
                    
                    // Generate file
                    var content = package.GetAsByteArray();
                    var fileName = $"Presmetka_Na_Provizija_{DatumOd:yyyy-MM-dd}_{DatumDo:yyyy-MM-dd}.xlsx";
                    
                    return File(content, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                }
            }
            catch (Exception ex)
            {
                // Return error details as a text file for debugging
                var errorInfo = $"Error generating Excel: {ex.Message}\r\n";
                if (ex.InnerException != null)
                {
                    errorInfo += $"Inner exception: {ex.InnerException.Message}\r\n";
                }
                errorInfo += $"Stack trace: {ex.StackTrace}";
                
                var bytes = System.Text.Encoding.UTF8.GetBytes(errorInfo);
                return File(bytes, "text/plain", "error_log.txt");
            }
        }

        // Helper method to get column index by name
        private int GetColumnIndexByName(DataTable dataTable, string columnName)
        {
            // Return 1-based index (for Excel) or -1 if not found
            for (int i = 0; i < dataTable.Columns.Count; i++)
            {
                if (dataTable.Columns[i].ColumnName == columnName)
                {
                    return i + 1; // +1 because Excel is 1-based
                }
            }
            return -1;
        }

        public async Task<JsonResult> OnGetSearchPolicies(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm) || searchTerm.Length < 2)
            {
                return new JsonResult(Array.Empty<object>());
            }

            List<PolicySearchResult> results = new List<PolicySearchResult>();

            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    string query = @"
                        SELECT 
                            p.Id,
                            p.BrojNaPolisa,
                            CASE 
                                WHEN ko.Naziv IS NOT NULL THEN ko.Naziv
                                ELSE CONCAT(ko.Ime, ' ', ko.Prezime)
                            END AS Osiguritel
                        FROM 
                            dbo.Polisi p
                        INNER JOIN
                            dbo.Klienti ko ON p.KlientiIdOsiguritel = ko.Id
                        WHERE 
                            p.BrojNaPolisa LIKE @SearchTerm + '%'
                            AND ISNULL(p.Storno, 0) = 0
                        ORDER BY 
                            p.BrojNaPolisa";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@SearchTerm", searchTerm);
                        
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                results.Add(new PolicySearchResult
                                {
                                    id = reader.GetInt64(0),
                                    brojNaPolisa = reader.GetString(1),
                                    osiguritel = reader.GetString(2)
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log exception but return empty results
                Console.WriteLine("Error searching policies: " + ex.Message);
            }

            return new JsonResult(results);
        }

        public async Task<JsonResult> OnGetSearchSellers(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm) || searchTerm.Length < 2)
            {
                return new JsonResult(Array.Empty<object>());
            }

            List<SellerModel> results = new List<SellerModel>();

            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    string query = @"
                        SELECT
                            k.Id, 
                            CASE 
                                WHEN k.Naziv IS NOT NULL THEN k.Naziv
                                ELSE CONCAT(k.Ime, ' ', k.Prezime)
                            END AS DisplayName,
                            k.KlientVraboten,
                            k.KlientSorabotnik,
                            k.BrokerskoDrustvo
                        FROM 
                            dbo.Klienti k
                        WHERE 
                            (k.KlientVraboten = 1 OR k.KlientSorabotnik = 1 OR k.BrokerskoDrustvo = 1)
                            AND k.Id != 93
                            AND (
                                k.Ime LIKE @SearchTerm + '%' OR 
                                k.Prezime LIKE @SearchTerm + '%' OR 
                                k.Naziv LIKE @SearchTerm + '%' OR
                                CONCAT(k.Ime, ' ', k.Prezime) LIKE @SearchTerm + '%'
                            )
                        ORDER BY 
                            DisplayName";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@SearchTerm", searchTerm);
                        
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                results.Add(new SellerModel
                                {
                                    Id = reader.GetInt64(0),
                                    DisplayName = reader.GetString(1),
                                    IsVraboten = reader.GetBoolean(2),
                                    IsSorabotnik = reader.GetBoolean(3),
                                    IsBroker = reader.GetBoolean(4)
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log exception but return empty results
                Console.WriteLine("Error searching sellers: " + ex.Message);
            }

            return new JsonResult(results);
        }

        public async Task<JsonResult> OnGetGetAllSellers()
        {
            List<SellerModel> allSellers = new List<SellerModel>();

            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    string query = @"
                        SELECT 
                            k.Id, 
                            CASE 
                                WHEN k.Naziv IS NOT NULL THEN k.Naziv
                                ELSE CONCAT(k.Ime, ' ', k.Prezime)
                            END AS DisplayName,
                            k.KlientVraboten,
                            k.KlientSorabotnik,
                            k.BrokerskoDrustvo
                        FROM 
                            dbo.Klienti k
                        WHERE 
                            (k.KlientVraboten = 1 OR k.KlientSorabotnik = 1 OR k.BrokerskoDrustvo = 1)
                            AND k.Id != 93
                        ORDER BY 
                            DisplayName";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                allSellers.Add(new SellerModel
                                {
                                    Id = reader.GetInt64(0),
                                    DisplayName = reader.GetString(1),
                                    IsVraboten = reader.GetBoolean(2),
                                    IsSorabotnik = reader.GetBoolean(3),
                                    IsBroker = reader.GetBoolean(4)
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error getting all sellers: " + ex.Message);
            }

            return new JsonResult(allSellers);
        }

        public async Task<JsonResult> OnGetGetSellerByName(string sellerName)
        {
            if (string.IsNullOrWhiteSpace(sellerName))
            {
                return new JsonResult(new { id = 0, displayName = "" });
            }

            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    string query = @"
                        SELECT TOP 1
                            k.Id, 
                            CASE 
                                WHEN k.Naziv IS NOT NULL THEN k.Naziv
                                ELSE CONCAT(k.Ime, ' ', k.Prezime)
                            END AS DisplayName,
                            k.KlientVraboten,
                            k.KlientSorabotnik,
                            k.BrokerskoDrustvo
                        FROM 
                            dbo.Klienti k
                        WHERE 
                            (k.KlientVraboten = 1 OR k.KlientSorabotnik = 1 OR k.BrokerskoDrustvo = 1)
                            AND k.Id != 93
                            AND (
                                k.Naziv = @SellerName OR
                                CONCAT(k.Ime, ' ', k.Prezime) = @SellerName
                            )";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@SellerName", sellerName);
                        
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                return new JsonResult(new SellerModel
                                {
                                    Id = reader.GetInt64(0),
                                    DisplayName = reader.GetString(1),
                                    IsVraboten = reader.GetBoolean(2),
                                    IsSorabotnik = reader.GetBoolean(3),
                                    IsBroker = reader.GetBoolean(4)
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error getting seller by name: " + ex.Message);
            }

            return new JsonResult(new { id = 0, displayName = "" });
        }

        public async Task<JsonResult> OnGetGetPolicyId(string policyNumber)
        {
            if (string.IsNullOrWhiteSpace(policyNumber))
            {
                return new JsonResult(new { id = 0, brojNaPolisa = "", osiguritel = "" });
            }

            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    string query = @"
                        SELECT 
                            p.Id,
                            p.BrojNaPolisa,
                            CASE 
                                WHEN ko.Naziv IS NOT NULL THEN ko.Naziv
                                ELSE CONCAT(ko.Ime, ' ', ko.Prezime)
                            END AS Osiguritel
                        FROM 
                            dbo.Polisi p
                        INNER JOIN
                            dbo.Klienti ko ON p.KlientiIdOsiguritel = ko.Id
                        WHERE 
                            p.BrojNaPolisa = @PolicyNumber
                            AND ISNULL(p.Storno, 0) = 0";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@PolicyNumber", policyNumber);
                        
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                return new JsonResult(new PolicySearchResult
                                {
                                    id = reader.GetInt64(0),
                                    brojNaPolisa = reader.GetString(1),
                                    osiguritel = reader.GetString(2)
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log exception
                Console.WriteLine("Error getting policy ID: " + ex.Message);
            }

            return new JsonResult(new { id = 0, brojNaPolisa = "", osiguritel = "" });
        }

        private async Task LoadSellers()
        {
            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    string query = @"
                        SELECT 
                            k.Id, 
                            CASE 
                                WHEN k.Naziv IS NOT NULL THEN k.Naziv
                                ELSE CONCAT(k.Ime, ' ', k.Prezime)
                            END AS DisplayName,
                            k.KlientVraboten,
                            k.KlientSorabotnik,
                            k.BrokerskoDrustvo
                        FROM 
                            dbo.Klienti k
                        WHERE 
                            (k.KlientVraboten = 1 OR k.KlientSorabotnik = 1 OR k.BrokerskoDrustvo = 1)
                            AND k.Id != 93
                        ORDER BY 
                            DisplayName";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                SellersList.Add(new SellerModel
                                {
                                    Id = reader.GetInt64(0),
                                    DisplayName = reader.GetString(1),
                                    IsVraboten = reader.GetBoolean(2),
                                    IsSorabotnik = reader.GetBoolean(3),
                                    IsBroker = reader.GetBoolean(4)
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ModelState.AddModelError(string.Empty, "Грешка при вчитување на соработниците: " + ex.Message);
            }
        }

        private async Task ProcessProvisionCalculation()
        {
            ProcessingComplete = true;
            TotalPolisi = 0;
            TotalSellers = 0;
            SuccessfullyProcessed = 0;
            HasErrors = false;
            ErrorMessages = string.Empty;

            try
            {
                // Parse the comma-separated policy IDs
                List<long> polisiIdList = new List<long>();
                
                try
                {
                    if (!string.IsNullOrWhiteSpace(PolisiIds))
                    {
                        polisiIdList = PolisiIds.Split(',')
                            .Where(s => !string.IsNullOrWhiteSpace(s))
                            .Select(s => long.Parse(s.Trim()))
                            .ToList();
                    }
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("PolisiIds", "Невалидни ID броеви на полиси: " + ex.Message);
                    HasErrors = true;
                    ErrorMessages = "Невалидни ID броеви на полиси: " + ex.Message;
                    return;
                }

                // Parse the comma-separated seller IDs
                List<long> sellerIdList = new List<long>();
                
                try
                {
                    sellerIdList = SellerIds.Split(',')
                        .Where(s => !string.IsNullOrWhiteSpace(s))
                        .Select(s => long.Parse(s.Trim()))
                        .ToList();
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("SellerIds", "Невалидни ID броеви на соработници: " + ex.Message);
                    HasErrors = true;
                    ErrorMessages = "Невалидни ID броеви на соработници: " + ex.Message;
                    return;
                }

                TotalPolisi = polisiIdList.Count;
                TotalSellers = sellerIdList.Count;

                if (TotalPolisi == 0)
                {
                    ModelState.AddModelError("PolisiIds", "Нема внесено валидни ID броеви на полиси");
                    HasErrors = true;
                    ErrorMessages = "Нема внесено валидни ID броеви на полиси";
                    return;
                }

                if (TotalSellers == 0)
                {
                    ModelState.AddModelError("SellerIds", "Нема внесено валидни ID броеви на соработници");
                    HasErrors = true;
                    ErrorMessages = "Нема внесено валидни ID броеви на соработници";
                    return;
                }

                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    // Create the table-valued parameters
                    DataTable polisiTable = new DataTable();
                    polisiTable.Columns.Add("PolisaId", typeof(long));

                    foreach (var polisaId in polisiIdList)
                    {
                        polisiTable.Rows.Add(polisaId);
                    }

                    DataTable sellerTable = new DataTable();
                    sellerTable.Columns.Add("Value", typeof(long));

                    foreach (var sellerId in sellerIdList)
                    {
                        sellerTable.Rows.Add(sellerId);
                    }

                    // Call the batch processing stored procedure
                    using (SqlCommand command = new SqlCommand("dbo.BatchCreateProvizijaPresmetkaStavki", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.CommandTimeout = 300; // Set a longer timeout (5 minutes) for multiple sellers

                        // Create and add the table-valued parameters
                        SqlParameter polisiParam = command.Parameters.AddWithValue("@PolisiIds", polisiTable);
                        polisiParam.SqlDbType = SqlDbType.Structured;
                        polisiParam.TypeName = "dbo.PolisiIdTableType";

                        SqlParameter sellerParam = command.Parameters.AddWithValue("@SellerIds", sellerTable);
                        sellerParam.SqlDbType = SqlDbType.Structured;
                        sellerParam.TypeName = "dbo.BigIntTableType";

                        command.Parameters.AddWithValue("@DatumOd", DatumOd);
                        command.Parameters.AddWithValue("@DatumDo", DatumDo);
                        command.Parameters.AddWithValue("@Debug", false); // Don't show debug info

                        // Capture the output messages
                        List<string> messages = new List<string>();
                        connection.InfoMessage += (sender, e) => {
                            messages.Add(e.Message);
                            // Count as successful if explicitly mentioned as successful
                            if (e.Message.Contains("Successfully processed policy ID:"))
                            {
                                SuccessfullyProcessed++;
                            }
                            // Count as successful if no hierarchy entries found (this is okay)
                            else if (e.Message.Contains("No superior records were inserted") || 
                                    e.Message.Contains("No base row found for SellerIdMatch"))
                            {
                                SuccessfullyProcessed++;
                            }
                            // Only count as error if there's an actual error processing
                            else if (e.Message.Contains("Error processing policy ID:") && 
                                    !e.Message.Contains("No superior records were inserted") &&
                                    !e.Message.Contains("No base row found for SellerIdMatch"))
                            {
                                HasErrors = true;
                            }
                        };

                        try
                        {
                            await command.ExecuteNonQueryAsync();
                            
                            // If no successful processed count was incremented via messages,
                            // but we didn't get any errors, assume success for all policies
                            if (SuccessfullyProcessed == 0 && !HasErrors && TotalPolisi > 0)
                            {
                                SuccessfullyProcessed = TotalPolisi;
                            }
                            
                            // Capture the current batch ID for filtering preview results
                            await CaptureBatchId(connection);
                        }
                        catch (SqlException sqlEx)
                        {
                            HasErrors = true;
                            ErrorMessages = "SQL грешка при пресметка на провизија: " + sqlEx.Message;
                            if (sqlEx.Number == -2) // Timeout error
                            {
                                ErrorMessages = "Операцијата истече - пробајте со помал број на полиси или соработници.";
                            }
                            return;
                        }

                        // Build the error messages string
                        var realErrors = messages.Where(m => 
                            m.Contains("Error processing policy ID:") && 
                            !m.Contains("No superior records were inserted") &&
                            !m.Contains("No base row found for SellerIdMatch")
                        ).ToList();
                        
                        if (realErrors.Any())
                        {
                            HasErrors = true;
                            ErrorMessages = string.Join(Environment.NewLine, realErrors);
                        }
                        else
                        {
                            HasErrors = false;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                HasErrors = true;
                ErrorMessages = "Грешка при пресметка на провизија: " + ex.Message;
                if (ex.InnerException != null)
                {
                    ErrorMessages += Environment.NewLine + "Inner Exception: " + ex.InnerException.Message;
                }
            }
        }
        
        private async Task CaptureBatchId(SqlConnection connection)
        {
            try
            {
                string currentYear = DateTime.Now.Year.ToString();
                string query = @"
                    SELECT TOP 1 
                        TRY_CAST(
                            SUBSTRING(
                                BrojNaSpecifikacijaProvizija, 
                                3,  -- Start after 'X/'
                                CHARINDEX('/', BrojNaSpecifikacijaProvizija, 3) - 3  -- Length until next '/'
                            ) AS INT
                        ) AS BatchId
                    FROM dbo.ProvizijaPresmetkaStavki
                    WHERE BrojNaSpecifikacijaProvizija LIKE '_/_/' + @CurrentYear
                      AND CHARINDEX('/', BrojNaSpecifikacijaProvizija, 3) > 3
                      AND DateCreated >= DATEADD(minute, -5, GETDATE())  -- Only records from last 5 minutes
                    ORDER BY DateCreated DESC";
                
                using (SqlCommand command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@CurrentYear", currentYear);
                    
                    var result = await command.ExecuteScalarAsync();
                    if (result != null && result != DBNull.Value)
                    {
                        CurrentBatchId = Convert.ToInt32(result);
                    }
                }
            }
            catch (Exception ex)
            {
                // If we can't capture the batch ID, just continue without it
                // The preview will fall back to showing recent records
                CurrentBatchId = null;
            }
        }
        
        private async Task LoadProvizijaPodatoci()
        {
            try
            {
                // Parse the policy IDs for direct query
                List<long> polisiIdList = new List<long>();
                
                if (!string.IsNullOrWhiteSpace(PolisiIds))
                {
                    try
                    {
                        polisiIdList = PolisiIds.Split(',')
                            .Where(s => !string.IsNullOrWhiteSpace(s))
                            .Select(s => long.Parse(s.Trim()))
                            .ToList();
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine("Error parsing policy IDs: " + ex.Message);
                    }
                }
                
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    
                    // If using automatic policy selection, we might need a different approach
                    if (PolicySelectionMode == "auto" && polisiIdList.Count == 1)
                    {
                        // In case of auto mode with a single policy, use a more inclusive query
                        string query = @"
                            SELECT 
                                pps.Id,
                                pps.DatumNaPresmetkaOd,
                                pps.DatumNaPresmetkaDo,
                                pps.DateCreated AS DatumNaPresmetka,
                                p.BrojNaPolisa,
                                CASE 
                                    WHEN osiguritel.Naziv IS NOT NULL THEN osiguritel.Naziv
                                    ELSE CONCAT(osiguritel.Ime, ' ', osiguritel.Prezime)
                                END AS Osiguritel,
                                CASE 
                                    WHEN ks.Naziv IS NOT NULL THEN ks.Naziv
                                    ELSE CONCAT(ks.Ime, ' ', ks.Prezime)
                                END AS Sorabotnik,
                                pps.IznosZaPresmetka AS Premija,
                                pps.ProcentDanok AS ProvizijaProcentOsnovica,
                                pps.IznosZaPresmetka AS ProvizijaOsnovica,
                                pps.IznosProvizijaBruto AS ProvizijaIznos,
                                pps.IznosProvizijaNeto AS ProvizijaIznosNaPatuvanje,
                                nacin_presmetka.Nacin AS NacinNaPresmetka
                            FROM 
                                dbo.ProvizijaPresmetkaStavki pps
                            INNER JOIN 
                                dbo.Polisi p ON pps.PolisiIdPolisa = p.Id
                            INNER JOIN 
                                dbo.Klienti osiguritel ON p.KlientiIdOsiguritel = osiguritel.Id
                            INNER JOIN 
                                dbo.Klienti ks ON pps.KlientiIdKlient = ks.Id
                            LEFT JOIN
                                dbo.SifrarnikNacinNaPresmetkaProvizija nacin_presmetka ON pps.SifrarnikNacinNaPresmetkaProvizijaId = nacin_presmetka.Id
                            WHERE 
                                pps.DateCreated >= DATEADD(minute, -15, GETDATE())
                            ORDER BY 
                                pps.Id DESC";
                        
                        using (SqlCommand command = new SqlCommand(query, connection))
                        {
                            using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                            {
                                ProvizijaPodatoci = new DataTable();
                                adapter.Fill(ProvizijaPodatoci);
                            }
                        }
                        
                        // If we still don't have data, try a direct approach with the policy ID
                        if (ProvizijaPodatoci.Rows.Count == 0)
                        {
                            string directQuery = @"
                                SELECT 
                                    pps.Id,
                                    pps.DatumNaPresmetkaOd,
                                    pps.DatumNaPresmetkaDo,
                                    pps.DateCreated AS DatumNaPresmetka,
                                    p.BrojNaPolisa,
                                    CASE 
                                        WHEN osiguritel.Naziv IS NOT NULL THEN osiguritel.Naziv
                                        ELSE CONCAT(osiguritel.Ime, ' ', osiguritel.Prezime)
                                    END AS Osiguritel,
                                    CASE 
                                        WHEN ks.Naziv IS NOT NULL THEN ks.Naziv
                                        ELSE CONCAT(ks.Ime, ' ', ks.Prezime)
                                    END AS Sorabotnik,
                                    pps.IznosZaPresmetka AS Premija,
                                    pps.ProcentDanok AS ProvizijaProcentOsnovica,
                                    pps.IznosZaPresmetka AS ProvizijaOsnovica,
                                    pps.IznosProvizijaBruto AS ProvizijaIznos,
                                    pps.IznosProvizijaNeto AS ProvizijaIznosNaPatuvanje,
                                    nacin_presmetka.Nacin AS NacinNaPresmetka
                                FROM 
                                    dbo.ProvizijaPresmetkaStavki pps
                                INNER JOIN 
                                    dbo.Polisi p ON pps.PolisiIdPolisa = p.Id
                                INNER JOIN 
                                    dbo.Klienti osiguritel ON p.KlientiIdOsiguritel = osiguritel.Id
                                INNER JOIN 
                                    dbo.Klienti ks ON pps.KlientProvizijaId = ks.Id
                                LEFT JOIN
                                    dbo.SifrarnikNacinNaPresmetkaProvizija nacin_presmetka ON pps.SifrarnikNacinNaPresmetkaProvizijaId = nacin_presmetka.Id
                                WHERE 
                                    pps.PolisiIdPolisa = @PolisaId
                                ORDER BY 
                                    pps.Id DESC";
                            
                            using (SqlCommand directCmd = new SqlCommand(directQuery, connection))
                            {
                                directCmd.Parameters.AddWithValue("@PolisaId", polisiIdList[0]);
                                
                                using (SqlDataAdapter adapter = new SqlDataAdapter(directCmd))
                                {
                                    ProvizijaPodatoci = new DataTable();
                                    adapter.Fill(ProvizijaPodatoci);
                                }
                            }
                        }
                    }
                    else
                    {
                        // Original approach for multiple policies or manual selection
                        
                        // Direct query approach focusing on the PolisiIdPolisa directly (changed from PolisaId)
                        string query = @"
                            SELECT 
                                pps.Id,
                                pps.DatumNaPresmetkaOd,
                                pps.DatumNaPresmetkaDo,
                                pps.DateCreated AS DatumNaPresmetka,
                                p.BrojNaPolisa,
                                CASE 
                                    WHEN osiguritel.Naziv IS NOT NULL THEN osiguritel.Naziv
                                    ELSE CONCAT(osiguritel.Ime, ' ', osiguritel.Prezime)
                                END AS Osiguritel,
                                CASE 
                                    WHEN ks.Naziv IS NOT NULL THEN ks.Naziv
                                    ELSE CONCAT(ks.Ime, ' ', ks.Prezime)
                                END AS Sorabotnik,
                                pps.IznosZaPresmetka AS Premija,
                                pps.ProcentDanok AS ProvizijaProcentOsnovica,
                                pps.IznosZaPresmetka AS ProvizijaOsnovica,
                                pps.IznosProvizijaBruto AS ProvizijaIznos,
                                pps.IznosProvizijaNeto AS ProvizijaIznosNaPatuvanje,
                                nacin_presmetka.Nacin AS NacinNaPresmetka
                            FROM 
                                dbo.ProvizijaPresmetkaStavki pps
                            INNER JOIN 
                                dbo.Polisi p ON pps.PolisiIdPolisa = p.Id
                            INNER JOIN 
                                dbo.Klienti osiguritel ON p.KlientiIdOsiguritel = osiguritel.Id
                            INNER JOIN 
                                dbo.Klienti ks ON pps.KlientiIdKlient = ks.Id
                            LEFT JOIN
                                dbo.SifrarnikNacinNaPresmetkaProvizija nacin_presmetka ON pps.SifrarnikNacinNaPresmetkaProvizijaId = nacin_presmetka.Id
                            WHERE 
                                pps.PolisiIdPolisa = @PolisaId
                            ORDER BY 
                                pps.Id DESC";
                                
                        // Create a combined DataTable to hold all results
                        ProvizijaPodatoci = new DataTable();
                        
                        // Try for each policy ID
                        foreach (var polisaId in polisiIdList)
                        {
                            using (SqlCommand command = new SqlCommand(query, connection))
                            {
                                command.Parameters.AddWithValue("@PolisaId", polisaId);
                                
                                using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                                {
                                    // Use a temporary table for this policy
                                    DataTable tempTable = new DataTable();
                                    adapter.Fill(tempTable);
                                    
                                    // If this is the first policy with data, use its schema
                                    if (ProvizijaPodatoci.Columns.Count == 0 && tempTable.Rows.Count > 0)
                                    {
                                        ProvizijaPodatoci = tempTable.Clone();
                                    }
                                    
                                    // Merge data if we have a valid schema
                                    if (ProvizijaPodatoci.Columns.Count > 0)
                                    {
                                        foreach (DataRow row in tempTable.Rows)
                                        {
                                            ProvizijaPodatoci.ImportRow(row);
                                        }
                                    }
                                }
                            }
                        }
                        
                        // If still no data, try a different approach using the recent calculation
                        if (ProvizijaPodatoci.Rows.Count == 0)
                        {
                            // Use a time-based approach to find the most recently created rows
                            string timeBasedQuery = @"
                                SELECT 
                                    pps.Id,
                                    pps.DatumNaPresmetkaOd,
                                    pps.DatumNaPresmetkaDo,
                                    pps.DateCreated AS DatumNaPresmetka,
                                    p.BrojNaPolisa,
                                    CASE 
                                        WHEN osiguritel.Naziv IS NOT NULL THEN osiguritel.Naziv
                                        ELSE CONCAT(osiguritel.Ime, ' ', osiguritel.Prezime)
                                    END AS Osiguritel,
                                    CASE 
                                        WHEN ks.Naziv IS NOT NULL THEN ks.Naziv
                                        ELSE CONCAT(ks.Ime, ' ', ks.Prezime)
                                    END AS Sorabotnik,
                                    pps.IznosZaPresmetka AS Premija,
                                    pps.ProcentDanok AS ProvizijaProcentOsnovica,
                                    pps.IznosZaPresmetka AS ProvizijaOsnovica,
                                    pps.IznosProvizijaBruto AS ProvizijaIznos,
                                    pps.IznosProvizijaNeto AS ProvizijaIznosNaPatuvanje,
                                    nacin_presmetka.Nacin AS NacinNaPresmetka
                                FROM 
                                    dbo.ProvizijaPresmetkaStavki pps
                                INNER JOIN 
                                    dbo.Polisi p ON pps.PolisiIdPolisa = p.Id
                                INNER JOIN 
                                    dbo.Klienti osiguritel ON p.KlientiIdOsiguritel = osiguritel.Id
                                INNER JOIN 
                                    dbo.Klienti ks ON pps.KlientiIdKlient = ks.Id
                                LEFT JOIN
                                    dbo.SifrarnikNacinNaPresmetkaProvizija nacin_presmetka ON pps.SifrarnikNacinNaPresmetkaProvizijaId = nacin_presmetka.Id
                                WHERE 
                                    pps.DateCreated >= DATEADD(minute, -15, GETDATE())
                                ORDER BY 
                                    pps.DateCreated DESC, pps.Id DESC";
                            
                            using (SqlCommand timeBasedCmd = new SqlCommand(timeBasedQuery, connection))
                            {
                                using (SqlDataAdapter adapter = new SqlDataAdapter(timeBasedCmd))
                                {
                                    ProvizijaPodatoci = new DataTable();
                                    adapter.Fill(ProvizijaPodatoci);
                                }
                            }
                        }
                    }
                }
                
                // If we have data, rename the columns
                if (ProvizijaPodatoci.Columns.Count > 0)
                {
                    // Rename columns for better display
                    if (ProvizijaPodatoci.Columns.Contains("Id"))
                        ProvizijaPodatoci.Columns["Id"].ColumnName = "ИД";
                    
                    if (ProvizijaPodatoci.Columns.Contains("DatumNaPresmetkaOd"))
                        ProvizijaPodatoci.Columns["DatumNaPresmetkaOd"].ColumnName = "Датум на почеток на полиса";
                        
                    if (ProvizijaPodatoci.Columns.Contains("DatumNaPresmetkaDo"))
                        ProvizijaPodatoci.Columns["DatumNaPresmetkaDo"].ColumnName = "Датум на крај на полиса";
                        
                    if (ProvizijaPodatoci.Columns.Contains("DatumNaPresmetka"))
                        ProvizijaPodatoci.Columns["DatumNaPresmetka"].ColumnName = "Датум на пресметка";
                        
                    if (ProvizijaPodatoci.Columns.Contains("BrojNaPolisa"))
                        ProvizijaPodatoci.Columns["BrojNaPolisa"].ColumnName = "Број на полиса";
                        
                    if (ProvizijaPodatoci.Columns.Contains("Osiguritel"))
                        ProvizijaPodatoci.Columns["Osiguritel"].ColumnName = "Осигурител";
                        
                    if (ProvizijaPodatoci.Columns.Contains("Sorabotnik"))
                        ProvizijaPodatoci.Columns["Sorabotnik"].ColumnName = "Соработник";
                        
                    if (ProvizijaPodatoci.Columns.Contains("Premija"))
                        ProvizijaPodatoci.Columns["Premija"].ColumnName = "Премија";
                        
                    if (ProvizijaPodatoci.Columns.Contains("ProvizijaProcentOsnovica"))
                        ProvizijaPodatoci.Columns["ProvizijaProcentOsnovica"].ColumnName = "% на основица";
                        
                    if (ProvizijaPodatoci.Columns.Contains("ProvizijaOsnovica"))
                        ProvizijaPodatoci.Columns["ProvizijaOsnovica"].ColumnName = "Основица";
                        
                    if (ProvizijaPodatoci.Columns.Contains("ProvizijaIznos"))
                        ProvizijaPodatoci.Columns["ProvizijaIznos"].ColumnName = "Износ на провизија";
                        
                    if (ProvizijaPodatoci.Columns.Contains("ProvizijaIznosNaPatuvanje"))
                        ProvizijaPodatoci.Columns["ProvizijaIznosNaPatuvanje"].ColumnName = "Провизија патување";

                    if (ProvizijaPodatoci.Columns.Contains("NacinNaPresmetka"))
                        ProvizijaPodatoci.Columns["NacinNaPresmetka"].ColumnName = "Начин на пресметка";
                }
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine("Error loading provizija data: " + ex.Message);
                
                // Create empty table if there's an error
                ProvizijaPodatoci = new DataTable();
            }
        }

        public async Task<JsonResult> OnGetPreviewExcelAsync(DateTime datumOd, DateTime datumDo, string clientTypes = null, bool hasNadreden = false, string nadredenNivo = null, string policySelectionMode = null, int? currentBatchId = null)
        {
            try
            {
                // Create DataTable to hold the report data
                DataTable exportData = new DataTable();
                
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    
                    // Base query for data - modified to retrieve more recent data for auto mode
                    string baseQuery = @"
                        SELECT 
                            pps.BrojNaSpecifikacijaProvizija,
                            pps.KlientProvizijaId,
                            CASE 
                                WHEN klient_primac.Naziv IS NOT NULL THEN klient_primac.Naziv
                                ELSE CONCAT(klient_primac.Ime, ' ', klient_primac.Prezime)
                            END AS PrimacProvizija,
                            pps.EMBGMBPrimacProvizija,
                            CASE 
                                WHEN dogovoruvac.Naziv IS NOT NULL THEN dogovoruvac.Naziv
                                ELSE CONCAT(dogovoruvac.Ime, ' ', dogovoruvac.Prezime)
                            END AS DogovoruvacImePrezime,
                            pps.EMBGMBKlientProvizija,
                            CASE 
                                WHEN osiguritel.Naziv IS NOT NULL THEN osiguritel.Naziv
                                ELSE CONCAT(osiguritel.Ime, ' ', osiguritel.Prezime)
                            END AS OsiguritelNaziv,
                            pps.BrojNaPolisa,
                            klasa.KlasaIme AS KlasaImeOsiguruvanje,
                            produkt.Ime AS ProduktIme,
                            pps.DatumNaPresmetkaOd,
                            pps.DatumNaPresmetkaDo,
                            pps.DateCreated AS DatumNaPresmetkaNaPremija,
                            pps.SifrarnikNacinPlakanjeId,
                            pps.Godina,
                            nacin_presmetka.Nacin AS NacinNaPresmetka,
                            pps.IznosZaPresmetka,
                            pps.DatumNaPremijaZaPresmetka,
                            pps.BrojNaVleznaFaktura,
                            pps.IznosProvizijaBruto,
                            pps.ProcentDanok,
                            pps.IznosProvizijaNeto,
                            pps.StapkaZaProvizija,
                            CASE 
                                WHEN sorabotnik_prvo_nivo.Naziv IS NOT NULL THEN sorabotnik_prvo_nivo.Naziv
                                ELSE CONCAT(sorabotnik_prvo_nivo.Ime, ' ', sorabotnik_prvo_nivo.Prezime)
                            END AS SorabotnikPrvoNivo,
                            pps.NadredenStavka
                        FROM 
                            dbo.ProvizijaPresmetkaStavki pps
                        LEFT JOIN 
                            dbo.Klienti osiguritel ON pps.KlientiIdOsiguritel = osiguritel.Id
                        LEFT JOIN 
                            dbo.Klienti klient ON pps.KlientProvizijaId = klient.Id
                        LEFT JOIN
                            dbo.Klienti klient_primac ON pps.KlientiIdKlient = klient_primac.Id
                        LEFT JOIN
                            dbo.KlasiOsiguruvanje klasa ON pps.KlasiOsiguruvanjeIdKlasa = klasa.Id
                        LEFT JOIN
                            dbo.Produkti produkt ON pps.ProizvodId = produkt.Id
                        LEFT JOIN
                            dbo.SifrarnikNacinNaPresmetkaProvizija nacin_presmetka ON pps.SifrarnikNacinNaPresmetkaProvizijaId = nacin_presmetka.Id
                        LEFT JOIN
                            dbo.Klienti sorabotnik_prvo_nivo ON pps.KlientiIdSorabotnikPrvoNivoPolisa = sorabotnik_prvo_nivo.Id
                        LEFT JOIN
                            dbo.Klienti dogovoruvac ON pps.DogovoruvacIdKlient = dogovoruvac.Id
                        WHERE 1=1";
                    
                    // Add batch filter if available (prioritize current batch over time-based filter)
                    // Use passed parameter first, then fall back to class property
                    int? batchIdToUse = currentBatchId ?? CurrentBatchId;
                    
                    if (batchIdToUse.HasValue)
                    {
                        string currentYear = DateTime.Now.Year.ToString();
                        baseQuery += $" AND pps.BrojNaSpecifikacijaProvizija LIKE '_/{batchIdToUse.Value}/{currentYear}'";
                    }
                    else
                    {
                        // Fallback to time-based filter if no batch ID available
                        baseQuery += " AND pps.DateCreated >= DATEADD(minute, -60, GETDATE())";
                    }
                    
                    // Add client type filter if provided
                    string clientTypeFilter = string.Empty;
                    if (!string.IsNullOrEmpty(clientTypes))
                    {
                        var clientTypesList = clientTypes.Split(',').ToList();
                        if (clientTypesList.Any())
                        {
                            clientTypeFilter = " AND pps.KlientProvizijaId IN (";
                            for (int i = 0; i < clientTypesList.Count; i++)
                            {
                                if (i > 0) clientTypeFilter += ",";
                                clientTypeFilter += "@ClientType" + i;
                            }
                            clientTypeFilter += ")";
                            baseQuery += clientTypeFilter;
                        }
                    }
                    
                    // Add nadreden filter if requested
                    if (hasNadreden)
                    {
                        baseQuery += " AND pps.NadredenStavka > 0";
                        
                        // Add specific level filter if provided
                        if (!string.IsNullOrEmpty(nadredenNivo))
                        {
                            baseQuery += " AND pps.NadredenStavka = @NadredenNivo";
                        }
                    }
                    
                    baseQuery += @" ORDER BY 
                            pps.DateCreated DESC, pps.Id DESC";
                    
                    using (SqlCommand command = new SqlCommand(baseQuery, connection))
                    {
                        // Add client type parameters if needed
                        if (!string.IsNullOrEmpty(clientTypes))
                        {
                            var clientTypesList = clientTypes.Split(',').ToList();
                            for (int i = 0; i < clientTypesList.Count; i++)
                            {
                                command.Parameters.AddWithValue("@ClientType" + i, clientTypesList[i]);
                            }
                        }
                        
                        // Add nadreden level parameter if needed
                        if (hasNadreden && !string.IsNullOrEmpty(nadredenNivo))
                        {
                            command.Parameters.AddWithValue("@NadredenNivo", nadredenNivo);
                        }
                        
                        using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                        {
                            adapter.Fill(exportData);
                        }
                    }
                    
                    // If no data found with date range, try with just the recent records
                    if (exportData.Rows.Count == 0)
                    {
                        string fallbackQuery = @"
                            SELECT 
                                pps.BrojNaSpecifikacijaProvizija,
                                pps.KlientProvizijaId,
                                CASE 
                                    WHEN klient_primac.Naziv IS NOT NULL THEN klient_primac.Naziv
                                    ELSE CONCAT(klient_primac.Ime, ' ', klient_primac.Prezime)
                                END AS PrimacProvizija,
                                pps.EMBGMBPrimacProvizija,
                                CASE 
                                    WHEN dogovoruvac.Naziv IS NOT NULL THEN dogovoruvac.Naziv
                                    ELSE CONCAT(dogovoruvac.Ime, ' ', dogovoruvac.Prezime)
                                END AS DogovoruvacImePrezime,
                                pps.EMBGMBKlientProvizija,
                                CASE 
                                    WHEN osiguritel.Naziv IS NOT NULL THEN osiguritel.Naziv
                                    ELSE CONCAT(osiguritel.Ime, ' ', osiguritel.Prezime)
                                END AS OsiguritelNaziv,
                                pps.BrojNaPolisa,
                                klasa.KlasaIme AS KlasaImeOsiguruvanje,
                                produkt.Ime AS ProduktIme,
                                pps.DatumNaPresmetkaOd,
                                pps.DatumNaPresmetkaDo,
                                pps.DateCreated AS DatumNaPresmetkaNaPremija,
                                pps.SifrarnikNacinPlakanjeId,
                                pps.Godina,
                                nacin_presmetka.Nacin AS NacinNaPresmetka,
                                pps.IznosZaPresmetka,
                                pps.DatumNaPremijaZaPresmetka,
                                pps.BrojNaVleznaFaktura,
                                pps.IznosProvizijaBruto,
                                pps.ProcentDanok,
                                pps.IznosProvizijaNeto,
                                pps.StapkaZaProvizija,
                                CASE 
                                    WHEN sorabotnik_prvo_nivo.Naziv IS NOT NULL THEN sorabotnik_prvo_nivo.Naziv
                                    ELSE CONCAT(sorabotnik_prvo_nivo.Ime, ' ', sorabotnik_prvo_nivo.Prezime)
                                END AS SorabotnikPrvoNivo,
                                pps.NadredenStavka
                            FROM 
                                dbo.ProvizijaPresmetkaStavki pps
                            LEFT JOIN 
                                dbo.Klienti osiguritel ON pps.KlientiIdOsiguritel = osiguritel.Id
                            LEFT JOIN
                                dbo.Klienti klient ON pps.KlientProvizijaId = klient.Id
                            LEFT JOIN
                                dbo.Klienti klient_primac ON pps.KlientiIdKlient = klient_primac.Id
                            LEFT JOIN
                                dbo.KlasiOsiguruvanje klasa ON pps.KlasiOsiguruvanjeIdKlasa = klasa.Id
                            LEFT JOIN
                                dbo.Produkti produkt ON pps.ProizvodId = produkt.Id
                            LEFT JOIN
                                dbo.SifrarnikNacinNaPresmetkaProvizija nacin_presmetka ON pps.SifrarnikNacinNaPresmetkaProvizijaId = nacin_presmetka.Id
                            LEFT JOIN
                                dbo.Klienti sorabotnik_prvo_nivo ON pps.KlientiIdSorabotnikPrvoNivoPolisa = sorabotnik_prvo_nivo.Id
                            LEFT JOIN
                                dbo.Klienti dogovoruvac ON pps.DogovoruvacIdKlient = dogovoruvac.Id
                            WHERE 
                                pps.DateCreated >= DATEADD(minute, -60, GETDATE())
                            ORDER BY 
                                pps.DateCreated DESC, pps.Id DESC";
                        
                        if (!string.IsNullOrEmpty(clientTypes))
                        {
                            fallbackQuery += clientTypeFilter;
                        }
                        
                        if (hasNadreden)
                        {
                            fallbackQuery += " AND pps.NadredenStavka > 0";
                            
                            if (!string.IsNullOrEmpty(nadredenNivo))
                            {
                                fallbackQuery += " AND pps.NadredenStavka = @NadredenNivo";
                            }
                        }
                        
                        fallbackQuery += " ORDER BY pps.DateCreated DESC";
                        
                        using (SqlCommand fallbackCmd = new SqlCommand(fallbackQuery, connection))
                        {
                            // Add parameters
                            if (!string.IsNullOrEmpty(clientTypes))
                            {
                                var clientTypesList = clientTypes.Split(',').ToList();
                                for (int i = 0; i < clientTypesList.Count; i++)
                                {
                                    fallbackCmd.Parameters.AddWithValue("@ClientType" + i, clientTypesList[i]);
                                }
                            }
                            
                            if (hasNadreden && !string.IsNullOrEmpty(nadredenNivo))
                            {
                                fallbackCmd.Parameters.AddWithValue("@NadredenNivo", nadredenNivo);
                            }
                            
                            using (SqlDataAdapter adapter = new SqlDataAdapter(fallbackCmd))
                            {
                                adapter.Fill(exportData);
                            }
                        }
                    }
                }
                
                                        // Define columns with Macedonian names - same as in ExportExcel
                        var columns = new Dictionary<string, string>
                        {
                            { "BrojNaSpecifikacijaProvizija", "Број на спецификација" },
                            { "KlientProvizijaId", "Клиент за провизија" },
                            { "PrimacProvizija", "Примач на провизија" },
                            { "EMBGMBPrimacProvizija", "ЕМБГ/МБ на примач на провизија" },
                            { "DogovoruvacImePrezime", "Договорувач" },
                            { "EMBGMBKlientProvizija", "ЕМБГ/МБ на клиент" },
                            { "OsiguritelNaziv", "Осигурител" },
                            { "BrojNaPolisa", "Број на полиса" },
                            { "KlasaImeOsiguruvanje", "Класа Осигурување" },
                            { "ProduktIme", "Производ" },
                            { "DatumNaPresmetkaOd", "Датум на почеток на полиса" },
                            { "DatumNaPresmetkaDo", "Датум на крај на полиса" },
                            { "DatumNaPresmetkaNaPremija", "Датум на пресметка на провизија" },
                            { "SifrarnikNacinPlakanjeId", "Начин на плаќање" },
                            { "Godina", "Година" },
                            { "NacinNaPresmetka", "Начин на пресметка" },
                            { "IznosZaPresmetka", "Износ за пресметка" },
                            { "DatumNaPremijaZaPresmetka", "Датум на наплата на премија" },
                            { "BrojNaVleznaFaktura", "Број на влезна фактура" },
                            { "IznosProvizijaBruto", "Износ провизија бруто" },
                            { "ProcentDanok", "Процент данок" },
                            { "IznosProvizijaNeto", "Износ провизија нето" },
                            { "StapkaZaProvizija", "Стапка за провизија" },
                            { "SorabotnikPrvoNivo", "Соработник нулто ниво" },
                            { "NadredenStavka", "Ниво на надреденост" }
                        };
                
                // Prepare data for JSON
                List<string> columnHeaders = new List<string>();
                List<List<object>> rows = new List<List<object>>();
                
                // Add translated column headers
                foreach (DataColumn column in exportData.Columns)
                {
                    string headerText = columns.ContainsKey(column.ColumnName) 
                        ? columns[column.ColumnName] 
                        : column.ColumnName;
                    columnHeaders.Add(headerText);
                }
                
                // Add data rows
                foreach (DataRow row in exportData.Rows)
                {
                    List<object> formattedRow = new List<object>();
                    
                    // Find the client type column index (if not already found)
                    int clientTypeColumnIndex = -1;
                    for (int i = 0; i < exportData.Columns.Count; i++)
                    {
                        if (exportData.Columns[i].ColumnName == "KlientProvizijaId")
                        {
                            clientTypeColumnIndex = i;
                            break;
                        }
                    }
                    
                    // Process each item in the row
                    for (int i = 0; i < row.ItemArray.Length; i++)
                    {
                        var item = row.ItemArray[i];
                        
                        // Format based on data type
                        object formattedValue = item;
                        
                        // Convert client type ID to text
                        if (i == clientTypeColumnIndex && item != null && item != DBNull.Value)
                        {
                            int clientTypeId;
                            
                            if (int.TryParse(item.ToString(), out clientTypeId))
                            {
                                switch (clientTypeId)
                                {
                                    case 1:
                                        formattedValue = "Брокерско Друштво";
                                        break;
                                    case 2:
                                        formattedValue = "Соработник";
                                        break;
                                    case 3:
                                        formattedValue = "Вработен";
                                        break;
                                    default:
                                        formattedValue = item.ToString();
                                        break;
                                }
                            }
                            else
                            {
                                formattedValue = item.ToString();
                            }
                        }
                        else if (item is DateTime dateValue)
                        {
                            formattedValue = dateValue.ToString("dd/MM/yyyy");
                        }
                        else if (item is decimal decimalValue)
                        {
                            formattedValue = decimalValue.ToString("N2");
                        }
                        else if (item is double doubleValue)
                        {
                            formattedValue = doubleValue.ToString("N2");
                        }
                        else if (item is float floatValue)
                        {
                            formattedValue = floatValue.ToString("N2");
                        }
                        
                        // Add the formatted value
                        formattedRow.Add(formattedValue);
                    }
                    
                    rows.Add(formattedRow);
                }
                
                return new JsonResult(new 
                { 
                    columns = columnHeaders, 
                    rows = rows,
                    totalCount = exportData.Rows.Count
                });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { error = ex.Message });
            }
        }

        // New method to fetch policies based on date range
        private async Task<string> GetPoliciesByDateRange(DateTime dateFrom, DateTime dateTo)
        {
            List<long> policyIds = new List<long>();
            
            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    
                    string query = @"
                        SELECT DISTINCT p.Id
                        FROM dbo.Polisi p
                        INNER JOIN dbo.Uplati u ON p.BrojNaPolisa = u.PolisaBroj
                        WHERE 
                            u.UplataDatum >= @DateFrom AND u.UplataDatum <= @DateTo
                            AND (u.Storno IS NULL OR u.Storno = 0)
                            AND (u.PreraspredelenaUplata IS NULL OR u.PreraspredelenaUplata != 1)
                            AND ISNULL(p.Storno, 0) = 0";
                    
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@DateFrom", dateFrom);
                        command.Parameters.AddWithValue("@DateTo", dateTo);
                        
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                policyIds.Add(reader.GetInt64(0));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the error
                Console.WriteLine("Error fetching policies by payment date range: " + ex.Message);
            }
            
            // Return the comma-separated IDs
            return string.Join(",", policyIds);
        }

        public async Task<IActionResult> OnPostGenerateProvizionReport()
        {
            Console.WriteLine("OnPostGenerateProvizionReport called");
            Console.WriteLine($"ReportSellerIds: {ReportSellerIds}");
            Console.WriteLine($"ReportDatumOd: {ReportDatumOd:yyyy-MM-dd}");
            Console.WriteLine($"ReportDatumDo: {ReportDatumDo:yyyy-MM-dd}");
            
            if (!await HasPageAccess("PresmetkaNaProvizija"))
            {
                Console.WriteLine("Access denied");
                return RedirectToAccessDenied();
            }
            
            // Always load sellers to ensure the dropdown works
            await LoadSellers();
            
            // We need to set ReportGenerated to false initially
            ReportGenerated = false;
            
            // Clear any model errors for the main form fields to prevent blocking
            ModelState.Remove("SellerIds");
            ModelState.Remove("PolisiIds");
            ModelState.Remove("DatumOd");
            ModelState.Remove("DatumDo");
            
            // Only validate ReportSellerIds for the report section
            if (string.IsNullOrWhiteSpace(ReportSellerIds))
            {
                Console.WriteLine("No sellers selected for report");
                ModelState.AddModelError("ReportSellerIds", "Изберете барем еден соработник за извештај");
                return Page();
            }
            
            // Clear any existing validation errors that might interfere
            foreach (var key in new List<string>(ModelState.Keys))
            {
                if (key != "ReportSellerIds" && key != "ReportDatumOd" && key != "ReportDatumDo")
                {
                    ModelState.Remove(key);
                }
            }
            
            try
            {
                Console.WriteLine("Getting item count");
                // Get the count of items for the report without loading all data
                int itemCount = await GetProvizionReportItemCount(ReportSellerIds, ReportDatumOd, ReportDatumDo);
                Console.WriteLine($"Found {itemCount} items for report");
                
                // Set properties for display
                ReportGenerated = true;
                ReportTotalItems = itemCount;
                
                // Use TempData to persist the message across redirects
                TempData["SuccessMessage"] = $"Извештај успешно генериран. Пронајдени {itemCount} ставки.";
                
                Console.WriteLine("Report generated successfully");
                // Return to the page showing the report is generated
                return Page();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating report: {ex.Message}");
                ModelState.AddModelError(string.Empty, "Грешка при генерирање на извештајот: " + ex.Message);
                return Page();
            }
        }
        
        public async Task<IActionResult> OnGetDownloadProvizionReport(DateTime ReportDatumOd, DateTime ReportDatumDo, string ReportSellerIds)
        {
            if (!await HasPageAccess("PresmetkaNaProvizija"))
            {
                return RedirectToAccessDenied();
            }
            
            if (string.IsNullOrWhiteSpace(ReportSellerIds))
            {
                // Return an Excel file with an error message instead of BadRequest
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var package = new ExcelPackage())
                {
                    var worksheet = package.Workbook.Worksheets.Add("Грешка");
                    worksheet.Cells["A1"].Value = "Грешка при генерирање на извештај";
                    worksheet.Cells["A3"].Value = "Изберете барем еден соработник";
                    
                    var content = package.GetAsByteArray();
                    return File(content, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "Provizija_Izvestaj_Greska.xlsx");
                }
            }
            
            try
            {
                // Create DataTable to hold the report data
                DataTable reportData = await GetProvizionReportData(ReportSellerIds, ReportDatumOd, ReportDatumDo);
                
                // Generate Excel file
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var package = new ExcelPackage())
                {
                    var worksheet = package.Workbook.Worksheets.Add("Провизија извештај");
                    
                    // Add header
                    worksheet.Cells["A1"].Value = "Провизија извештај";
                    worksheet.Cells["A1:F1"].Merge = true;
                    worksheet.Cells["A1"].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    worksheet.Cells["A1"].Style.Font.Bold = true;
                    worksheet.Cells["A1"].Style.Font.Size = 14;
                    
                    // Add date range
                    worksheet.Cells["A3"].Value = "Период:";
                    worksheet.Cells["B3"].Value = $"{ReportDatumOd:dd.MM.yyyy} - {ReportDatumDo:dd.MM.yyyy}";
                    
                    // Add number of records
                    worksheet.Cells["A4"].Value = "Вкупно записи:";
                    worksheet.Cells["B4"].Value = reportData.Rows.Count;
                    
                    // Add number of sellers
                    worksheet.Cells["A5"].Value = "Вкупно соработници:";
                    worksheet.Cells["B5"].Value = ReportSellerIds.Split(',').Length;
                    
                    // Add spacing before the table
                    int tableStartRow = 7;
                    
                    if (reportData.Columns.Count > 0 && reportData.Rows.Count > 0)
                    {
                        // Add column headers with translated column names
                        var headerRow = tableStartRow;
                        
                        // Define columns with Macedonian names
                        var columns = new Dictionary<string, string>
                        {
                            { "BrojNaSpecifikacijaProvizija", "Број на спецификација" },
                            { "KlientProvizijaId", "Клиент за провизија" },
                            { "PrimacProvizija", "Примач на провизија" },
                            { "EMBGMBPrimacProvizija", "ЕМБГ/МБ на примач на провизија" },
                            { "DogovoruvacImePrezime", "Договорувач" },
                            { "EMBGMBKlientProvizija", "ЕМБГ/МБ на клиент" },
                            { "OsiguritelNaziv", "Осигурител" },
                            { "BrojNaPolisa", "Број на полиса" },
                            { "KlasaImeOsiguruvanje", "Класа Осигурување" },
                            { "ProduktIme", "Производ" },
                            { "DatumNaPresmetkaOd", "Датум на почеток на полиса" },
                            { "DatumNaPresmetkaDo", "Датум на крај на полиса" },
                            { "DatumNaPresmetkaNaPremija", "Датум на пресметка на провизија" },
                            { "SifrarnikNacinPlakanjeId", "Начин на плаќање" },
                            { "Godina", "Година" },
                            { "NacinNaPresmetka", "Начин на пресметка" },
                            { "IznosZaPresmetka", "Износ за пресметка" },
                            { "DatumNaPremijaZaPresmetka", "Датум на наплата на премија" },
                            { "BrojNaVleznaFaktura", "Број на влезна фактура" },
                            { "IznosProvizijaBruto", "Износ провизија бруто" },
                            { "ProcentDanok", "Процент данок" },
                            { "IznosProvizijaNeto", "Износ провизија нето" },
                            { "StapkaZaProvizija", "Стапка за провизија" },
                            { "SorabotnikPrvoNivo", "Соработник нулто ниво" },
                            { "NadredenStavka", "Ниво на надреденост" }
                        };
                        
                        // Add headers
                        int colIndex = 1;
                        int clientTypeColumnIndex = -1;
                        
                        // Find KlientProvizijaId column index
                        for (int i = 0; i < reportData.Columns.Count; i++)
                        {
                            if (reportData.Columns[i].ColumnName == "KlientProvizijaId")
                            {
                                clientTypeColumnIndex = i + 1; // 1-based for Excel
                                break;
                            }
                        }
                        
                        // Add the original headers
                        foreach (DataColumn column in reportData.Columns)
                        {
                            string headerText = columns.ContainsKey(column.ColumnName) 
                                ? columns[column.ColumnName] 
                                : column.ColumnName;
                                
                            var cell = worksheet.Cells[headerRow, colIndex++];
                            cell.Value = headerText;
                            cell.Style.Font.Bold = true;
                            cell.Style.Fill.PatternType = ExcelFillStyle.Solid;
                            cell.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                            cell.Style.Border.BorderAround(ExcelBorderStyle.Thin);
                        }
                        
                        // Add data rows
                        int rowIndex = headerRow + 1;
                        foreach (DataRow row in reportData.Rows)
                        {
                            colIndex = 1;
                            foreach (var item in row.ItemArray)
                            {
                                var cell = worksheet.Cells[rowIndex, colIndex];
                                
                                // Replace client type ID with text
                                if (clientTypeColumnIndex > 0 && colIndex == clientTypeColumnIndex)
                                {
                                    if (item != null && item != DBNull.Value)
                                    {
                                        int clientTypeId;
                                        
                                        if (int.TryParse(item.ToString(), out clientTypeId))
                                        {
                                            switch (clientTypeId)
                                            {
                                                case 1:
                                                    cell.Value = "Брокерско Друштво";
                                                    break;
                                                case 2:
                                                    cell.Value = "Соработник";
                                                    break;
                                                case 3:
                                                    cell.Value = "Вработен";
                                                    break;
                                                default:
                                                    cell.Value = item;
                                                    break;
                                            }
                                        }
                                        else
                                        {
                                            cell.Value = item;
                                        }
                                    }
                                    else
                                    {
                                        cell.Value = item;
                                    }
                                }
                                else
                                {
                                    cell.Value = item;
                                }
                                
                                // Format based on data type
                                if (item is DateTime dateValue)
                                {
                                    cell.Style.Numberformat.Format = "dd/MM/yyyy HH:mm";
                                }
                                else if (item is decimal || item is double || item is float)
                                {
                                    cell.Style.Numberformat.Format = "#,##0.00";
                                }
                                else if (item is bool boolValue)
                                {
                                    cell.Value = boolValue ? "Да" : "Не";
                                }
                                
                                cell.Style.Border.BorderAround(ExcelBorderStyle.Thin);
                                colIndex++;
                            }
                            rowIndex++;
                        }
                        
                        // Auto-fit columns
                        worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
                    }
                    else
                    {
                        // If no data, add a message
                        worksheet.Cells["A" + tableStartRow].Value = "Нема податоци за прикажување во избраниот период.";
                        worksheet.Cells["A" + tableStartRow + ":F" + tableStartRow].Merge = true;
                        worksheet.Cells["A" + tableStartRow].Style.Font.Italic = true;
                        worksheet.Cells["A" + tableStartRow].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    }
                    
                    // Generate file
                    var content = package.GetAsByteArray();
                    var fileName = $"Provizija_Izvestaj_{ReportDatumOd:yyyy-MM-dd}_{ReportDatumDo:yyyy-MM-dd}.xlsx";
                    
                    return File(content, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                }
            }
            catch (Exception ex)
            {
                // Return error details as a text file for debugging
                var errorInfo = $"Error generating Excel: {ex.Message}\r\n";
                if (ex.InnerException != null)
                {
                    errorInfo += $"Inner exception: {ex.InnerException.Message}\r\n";
                }
                errorInfo += $"Stack trace: {ex.StackTrace}";
                
                var bytes = System.Text.Encoding.UTF8.GetBytes(errorInfo);
                return File(bytes, "text/plain", "error_log.txt");
            }
        }
        
        private async Task<int> GetProvizionReportItemCount(string sellerIds, DateTime datumOd, DateTime datumDo)
        {
            int count = 0;
            
            try
            {
                // Parse the comma-separated seller IDs
                List<long> sellerIdList = sellerIds.Split(',')
                    .Where(s => !string.IsNullOrWhiteSpace(s))
                    .Select(s => long.Parse(s.Trim()))
                    .ToList();
                    
                if (sellerIdList.Count == 0)
                    return 0;
                
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    
                    // Create parameters for the IN clause
                    string sellerIdsParam = string.Join(",", sellerIdList);
                    
                    // Query to count items - CHANGED to use DateCreated and include the full end date
                    string query = @"
                        SELECT COUNT(*)
                        FROM dbo.ProvizijaPresmetkaStavki pps
                        WHERE pps.KlientiIdKlient IN (" + sellerIdsParam + @")
                        AND pps.DateCreated >= @DatumOd 
                        AND pps.DateCreated < @DatumDoPlusOne";
                    
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@DatumOd", datumOd);
                        // Add one day to include the entire end date
                        command.Parameters.AddWithValue("@DatumDoPlusOne", datumDo.AddDays(1));
                        
                        // Execute count query
                        count = (int)await command.ExecuteScalarAsync();
                        
                        // Debug info
                      
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Грешка при броење на ставки за извештај: " + ex.Message);
            }
            
            return count;
        }
        
        private async Task<DataTable> GetProvizionReportData(string sellerIds, DateTime datumOd, DateTime datumDo)
        {
            DataTable reportData = new DataTable();
            
            try
            {
                // Parse the comma-separated seller IDs
                List<long> sellerIdList = sellerIds.Split(',')
                    .Where(s => !string.IsNullOrWhiteSpace(s))
                    .Select(s => long.Parse(s.Trim()))
                    .ToList();
                    
                if (sellerIdList.Count == 0)
                    return reportData;
                
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    
                    // Create parameters for the IN clause
                    string sellerIdsParam = string.Join(",", sellerIdList);
                    
                    // Query to get report data - CHANGED to use DateCreated and include the full end date
                    string query = @"
                        SELECT 
                            pps.BrojNaSpecifikacijaProvizija,
                            pps.KlientProvizijaId,
                            CASE 
                                WHEN klient_primac.Naziv IS NOT NULL THEN klient_primac.Naziv
                                ELSE CONCAT(klient_primac.Ime, ' ', klient_primac.Prezime)
                            END AS PrimacProvizija,
                            pps.EMBGMBPrimacProvizija,
                            CASE 
                                WHEN dogovoruvac.Naziv IS NOT NULL THEN dogovoruvac.Naziv
                                ELSE CONCAT(dogovoruvac.Ime, ' ', dogovoruvac.Prezime)
                            END AS DogovoruvacImePrezime,
                            pps.EMBGMBKlientProvizija,
                            CASE 
                                WHEN osiguritel.Naziv IS NOT NULL THEN osiguritel.Naziv
                                ELSE CONCAT(osiguritel.Ime, ' ', osiguritel.Prezime)
                            END AS OsiguritelNaziv,
                            pps.BrojNaPolisa,
                            klasa.KlasaIme AS KlasaImeOsiguruvanje,
                            produkt.Ime AS ProduktIme,
                            pps.DatumNaPresmetkaOd,
                            pps.DatumNaPresmetkaDo,
                            pps.DateCreated AS DatumNaPresmetkaNaPremija,
                            pps.SifrarnikNacinPlakanjeId,
                            pps.Godina,
                            nacin_presmetka.Nacin AS NacinNaPresmetka,
                            pps.IznosZaPresmetka,
                            pps.DatumNaPremijaZaPresmetka,
                            pps.BrojNaVleznaFaktura,
                            pps.IznosProvizijaBruto,
                            pps.ProcentDanok,
                            pps.IznosProvizijaNeto,
                            pps.StapkaZaProvizija,
                            CASE 
                                WHEN sorabotnik_prvo_nivo.Naziv IS NOT NULL THEN sorabotnik_prvo_nivo.Naziv
                                ELSE CONCAT(sorabotnik_prvo_nivo.Ime, ' ', sorabotnik_prvo_nivo.Prezime)
                            END AS SorabotnikPrvoNivo,
                            pps.NadredenStavka,
                            pps.DateCreated
                        FROM 
                            dbo.ProvizijaPresmetkaStavki pps
                        LEFT JOIN 
                            dbo.Klienti osiguritel ON pps.KlientiIdOsiguritel = osiguritel.Id
                        LEFT JOIN
                            dbo.Klienti klient ON pps.KlientProvizijaId = klient.Id
                        LEFT JOIN
                            dbo.Klienti klient_primac ON pps.KlientiIdKlient = klient_primac.Id
                        LEFT JOIN
                            dbo.KlasiOsiguruvanje klasa ON pps.KlasiOsiguruvanjeIdKlasa = klasa.Id
                        LEFT JOIN
                            dbo.Produkti produkt ON pps.ProizvodId = produkt.Id
                        LEFT JOIN
                            dbo.SifrarnikNacinNaPresmetkaProvizija nacin_presmetka ON pps.SifrarnikNacinNaPresmetkaProvizijaId = nacin_presmetka.Id
                        LEFT JOIN
                            dbo.Klienti sorabotnik_prvo_nivo ON pps.KlientiIdSorabotnikPrvoNivoPolisa = sorabotnik_prvo_nivo.Id
                        LEFT JOIN
                            dbo.Klienti dogovoruvac ON pps.DogovoruvacIdKlient = dogovoruvac.Id
                        WHERE 
                            pps.KlientiIdKlient IN (" + sellerIdsParam + @")
                            AND pps.DateCreated >= @DatumOd 
                            AND pps.DateCreated < @DatumDoPlusOne
                        ORDER BY 
                            pps.DateCreated DESC, pps.Id DESC";
                    
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@DatumOd", datumOd);
                        // Add one day to include the entire end date
                        command.Parameters.AddWithValue("@DatumDoPlusOne", datumDo.AddDays(1));
                        
                        // Debug info
                        Console.WriteLine($"Data query date range: {datumOd:yyyy-MM-dd} to {datumDo.AddDays(1):yyyy-MM-dd}");
                        
                        using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                        {
                            adapter.Fill(reportData);
                        }
                        
                        Console.WriteLine($"Data query found {reportData.Rows.Count} records");
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Грешка при вчитување на податоци за извештај: " + ex.Message);
            }
            
            return reportData;
        }
    }

    public class SellerModel
    {
        public long Id { get; set; }
        public string DisplayName { get; set; }
        public bool IsVraboten { get; set; }
        public bool IsSorabotnik { get; set; }
        public bool IsBroker { get; set; }
    }
    
    public class PolicySearchResult
    {
        public long id { get; set; }
        public string brojNaPolisa { get; set; }
        public string osiguritel { get; set; }
    }
}


