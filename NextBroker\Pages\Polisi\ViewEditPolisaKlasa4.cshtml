@page "{id:long}"
@model NextBroker.Pages.Polisi.ViewEditPolisaKlasa4Model
@{
    ViewData["Title"] = "Преглед/Измена на полиса Класа 4";
    var formClass = Model.HasAdminAccess ? "" : "form-disabled";
}

<h1 style='text-align: center;'>@ViewData["Title"]</h1>


<div class="container-fluid mt-4 px-4">
    <!-- Success Message -->
    @if (TempData["SuccessMessage"] != null)
    {
        <div id="successMessage" class="alert alert-success alert-dismissible fade show" role="alert">
            <strong>Успешно!</strong> @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (!Model.HasAdminAccess)
    {
        <style>
            .form-disabled input:not([type="hidden"]),
            .form-disabled select,
            .form-disabled textarea {
                pointer-events: none;
                background-color: #e9ecef;
                opacity: 1;
            }
        </style>
    }

    <!-- Add custom styles for read-only selects -->
    <style>
        .select-readonly {
            pointer-events: none;
            background-color: #e9ecef !important;
            opacity: 1;
        }
    </style>

    <form method="post" class="@formClass">
        @Html.AntiForgeryToken()

        <div class="text-end mb-3">
    <button type="button" class="btn btn-outline-secondary btn-sm" id="collapseAllBtn">
        <i class="bi bi-chevron-up"></i> Затвори ги сите секции
    </button>
</div>

        
        <!-- Add validation summary -->
        <div asp-validation-summary="All" class="text-danger"></div>

        <!-- Non-editable Information Card -->
        <div class="card mb-3">
            <div class="card-header" role="button" data-bs-toggle="collapse" data-bs-target="#systemInfo" aria-expanded="false" aria-controls="systemInfo" style="cursor: pointer;">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Системски информации</h5>
                    <i class="bi bi-chevron-down"></i>
                </div>
            </div>
            <div class="collapse show" id="systemInfo">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2 mb-3">
                            <label class="form-label">ID</label>
                            <input type="text" class="form-control" value="@Model.Input.Id" readonly />
                            <input type="hidden" asp-for="Input.Id" />
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">Датум на креирање</label>
                            <input type="text" class="form-control" value="@(Model.Input.DateCreated?.ToString("dd.MM.yyyy HH:mm:ss"))" readonly />
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">Креирано од</label>
                            <input type="text" class="form-control" value="@Model.Input.UsernameCreated" readonly />
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">Последна промена</label>
                            <input type="text" class="form-control" value="@(Model.Input.DateModified?.ToString("dd.MM.yyyy HH:mm:ss"))" readonly />
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">Променето од</label>
                            <input type="text" class="form-control" value="@Model.Input.UsernameModified" readonly />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Basic Information Card -->
        <div class="card mb-4">
            <div class="card-header" role="button" data-bs-toggle="collapse" data-bs-target="#basicInfo" aria-expanded="false" aria-controls="basicInfo" style="cursor: pointer;">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Основни информации</h5>
                    <i class="bi bi-chevron-down"></i>
                </div>
            </div>
            <div class="collapse show" id="basicInfo">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Осигурител</label>
                            <select asp-for="Input.KlientiIdOsiguritel" 
                                    asp-items="Model.Osiguriteli" 
                                    class="form-select select-readonly">
                                <option value="">-- Избери осигурител --</option>
                            </select>
                            <span asp-validation-for="Input.KlientiIdOsiguritel" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Класа на осигурување</label>
                            <select asp-for="Input.KlasiOsiguruvanjeIdKlasa" 
                                    asp-items="Model.KlasiOsiguruvanje" 
                                    class="form-select" disabled>
                                <option value="">-- Избери класа --</option>
                            </select>
                            <input type="hidden" asp-for="Input.KlasiOsiguruvanjeIdKlasa" />
                            <span asp-validation-for="Input.KlasiOsiguruvanjeIdKlasa" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Продукт</label>
                            <select asp-for="Input.ProduktiIdProizvod" 
                                    asp-items="Model.Produkti" 
                                    class="form-select select-readonly">
                                <option value="">-- Избери продукт --</option>
                            </select>
                            <span asp-validation-for="Input.ProduktiIdProizvod" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.BrojNaPolisa" class="form-label">Број на полиса</label>
                            <input asp-for="Input.BrojNaPolisa" class="form-control" readonly />
                            <span asp-validation-for="Input.BrojNaPolisa" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3 d-none">
                            <label asp-for="Input.BrojNaPonuda" class="form-label">Број на понуда</label>
                            <input asp-for="Input.BrojNaPonuda" class="form-control" type="number" />
                            <span asp-validation-for="Input.BrojNaPonuda" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-10 mb-3">
                            <label class="form-label">Договорувач (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                            <div class="input-group">
                                <input type="text" id="dogovoruvacMBSearch" class="form-control" 
                                       autocomplete="off"
                                       placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..."
                                       value="@ViewData["DogovoruvacNaziv"]" readonly />
                                <input type="hidden" asp-for="Input.KlientiIdDogovoruvac" id="KlientiIdDogovoruvac" />
                            </div>
                            <div id="dogovoruvacSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                                 style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-10 mb-3">
                            <label class="form-label">Осигуреник (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                            <div class="input-group">
                                <input type="text" id="osigurenikMBSearch" class="form-control" 
                                       autocomplete="off"
                                       placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..."
                                       value="@ViewData["OsigurenikNaziv"]" readonly />
                                <input type="hidden" asp-for="Input.KlientiIdOsigurenik" id="KlientiIdOsigurenik" />
                            </div>
                            <div id="osigurenikSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                                 style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-10 mb-3">
                            <label class="form-label">Соработник/вработен (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                            <div class="input-group">
                                <input type="text" id="sorabotnikMBSearch" class="form-control" 
                                       autocomplete="off"
                                       placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..."
                                       value="@ViewData["SorabotnikNaziv"]" />
                                <button type="button" class="btn btn-outline-danger" 
                                        id="nullifySorabotnik" 
                                        style="font-size: 0.75rem; padding: 0px 8px; height: 31px; line-height: 29px;">
                                    Анулирај
                                </button>
                                <input type="hidden" asp-for="Input.KlientiIdSorabotnik" id="KlientiIdSorabotnik" />
                            </div>
                            <div id="sorabotnikSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                                 style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3 d-none">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" asp-for="Input.Kolektivna" id="kolektivna" readonly>
                                <label class="form-check-label" for="kolektivna">
                                    Колективна
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3 d-none">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" asp-for="Input.********************************" id="kolektivnaNeodredenBr" readonly>
                                <label class="form-check-label" for="kolektivnaNeodredenBr">
	                                    Колективна со неодреден број на осигуреници
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row" id="neodredenBrZabeleskaContainer">
                        <div class="col-md-12 mb-3" style="display:none;">
                            <label asp-for="Input.NeodredenBrOsigureniciZabeleska" class="form-label">Забелешка за неодреден број осигуреници</label>
                            <textarea asp-for="Input.NeodredenBrOsigureniciZabeleska" class="form-control" readonly></textarea>
                            <span asp-validation-for="Input.NeodredenBrOsigureniciZabeleska" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.DatumVaziOd" class="form-label">Важи од</label>
                            <input asp-for="Input.DatumVaziOd" class="form-control datepicker" type="date" readonly />
                            <span asp-validation-for="Input.DatumVaziOd" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.DatumVaziDo" class="form-label">Важи до</label>
                            <input asp-for="Input.DatumVaziDo" class="form-control datepicker" type="date" readonly />
                            <span asp-validation-for="Input.DatumVaziDo" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.DatumNaIzdavanje" class="form-label">Датум на издавање</label>
                            <input asp-for="Input.DatumNaIzdavanje" class="form-control datepicker" type="date" readonly />
                            <span asp-validation-for="Input.DatumNaIzdavanje" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3 d-none">
                            <label asp-for="Input.VremetraenjeNaPolisa" class="form-label">Времетраење на полиса (месеци)</label>
                            <input asp-for="Input.VremetraenjeNaPolisa" class="form-control" type="number" min="0" />
                            <span asp-validation-for="Input.VremetraenjeNaPolisa" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3 d-none">
                            <label asp-for="Input.PeriodNaUplata" class="form-label">Период на уплата (месеци)</label>
                            <input asp-for="Input.PeriodNaUplata" class="form-control" type="number" min="0" />
                            <span asp-validation-for="Input.PeriodNaUplata" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.SifrarnikValutiIdValuta" class="form-label">Валута</label>
                            <select asp-for="Input.SifrarnikValutiIdValuta" 
                                    asp-items="Model.Valuti" 
                                    class="form-select" disabled>
                                <option value="">-- Избери валута --</option>
                            </select>
                            <input type="hidden" asp-for="Input.SifrarnikValutiIdValuta" />
                            <span asp-validation-for="Input.SifrarnikValutiIdValuta" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" asp-for="Input.Faktoring" id="faktoring">
                                <label class="form-check-label" for="faktoring">
                                    Факторинг
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.SifrarnikValutiIdFranshizaValuta" class="form-label">Валута на франшиза</label>
                            <select asp-for="Input.SifrarnikValutiIdFranshizaValuta" 
                                    asp-items="Model.Valuti" 
                                    class="form-select">
                                <option value="">-- Избери валута --</option>
                            </select>
                            <span asp-validation-for="Input.SifrarnikValutiIdFranshizaValuta" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.ProcentFranshiza" class="form-label">Процент на франшиза (%)</label>
                            <input asp-for="Input.ProcentFranshiza" class="form-control" type="number" step="0.01" min="0" max="100" />
                            <span asp-validation-for="Input.ProcentFranshiza" class="text-danger"></span>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.FranshizaIznos" class="form-label">Износ на франшиза</label>
                            <input asp-for="Input.FranshizaIznos" class="form-control" type="number" step="0.0001" />
                            <span asp-validation-for="Input.FranshizaIznos" class="text-danger"></span>
                        </div>

                        <div class="col-md-4 mb-3 d-none">
                            <label asp-for="Input.ProcentFinansiski" class="form-label">Процент финансиски (%)</label>
                            <input asp-for="Input.ProcentFinansiski" class="form-control" type="number" step="0.01" min="0" max="100" />
                            <span asp-validation-for="Input.ProcentFinansiski" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.KoregiranaStapkaNaProvizija" class="form-label">Корегирана стапка на провизија (%)</label>
                            <input asp-for="Input.KoregiranaStapkaNaProvizija" class="form-control" type="number" step="0.0001" min="0" max="100" />
                            <span asp-validation-for="Input.KoregiranaStapkaNaProvizija" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.SifrarnikNacinNaPlakjanjeId" class="form-label">Начин на плаќање</label>
                            <select asp-for="Input.SifrarnikNacinNaPlakjanjeId" 
                                    asp-items="Model.NaciniNaPlakjanje" 
                                    class="form-select" disabled>
                                <option value="">-- Избери начин на плаќање --</option>
                            </select>
                            <input type="hidden" asp-for="Input.SifrarnikNacinNaPlakjanjeId" />
                            <span asp-validation-for="Input.SifrarnikNacinNaPlakjanjeId" class="text-danger"></span>
                        </div>
                    </div>
                    
                    <div class="row d-none">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.SifrarnikTipNaPlakanjeId" class="form-label">Тип на плаќање</label>
                            <select asp-for="Input.SifrarnikTipNaPlakanjeId" 
                                    asp-items="Model.TipoviNaPlakanje" 
                                    class="form-select">
                                <option value="">-- Избери тип на плаќање --</option>
                            </select>
                            <span asp-validation-for="Input.SifrarnikTipNaPlakanjeId" class="text-danger"></span>
                        </div>
                    </div>
                    <input type="hidden" asp-for="Input.SifrarnikTipNaPlakanjeId" value="1" />
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.TipNaFaktura" class="form-label">Тип на фактура</label>
                            <input asp-for="Input.TipNaFaktura" class="form-control" disabled />
                            <input type="hidden" asp-for="Input.TipNaFaktura" />
                            <span asp-validation-for="Input.TipNaFaktura" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.BrojNaFakturaVlezna" class="form-label">Број на влезна фактура</label>
                            <input asp-for="Input.BrojNaFakturaVlezna" class="form-control" readonly/>
                            <input type="hidden" asp-for="Input.BrojNaFakturaVlezna" />
                            <span asp-validation-for="Input.BrojNaFakturaVlezna" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.DatumNaFakturaVlezna" class="form-label">Датум на влезна фактура</label>
                            <input asp-for="Input.DatumNaFakturaVlezna" class="form-control datepicker" type="date" readonly/>
                            <input type="hidden" asp-for="Input.DatumNaFakturaVlezna" />
                            <span asp-validation-for="Input.DatumNaFakturaVlezna" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.RokNaPlakjanjeFakturaVlezna" class="form-label">Рок на плаќање на влезна фактура</label>
                            <input asp-for="Input.RokNaPlakjanjeFakturaVlezna" class="form-control datepicker" type="date" readonly/>
                            <input type="hidden" asp-for="Input.RokNaPlakjanjeFakturaVlezna" />
                            <span asp-validation-for="Input.RokNaPlakjanjeFakturaVlezna" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row d-none">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.SifrarnikBankiIdBanka" class="form-label">Банка</label>
                            <select asp-for="Input.SifrarnikBankiIdBanka" 
                                    asp-items="Model.Banki" 
                                    class="form-select">
                                <option value="">-- Избери банка --</option>
                            </select>
                            <span asp-validation-for="Input.SifrarnikBankiIdBanka" class="text-danger"></span>
                        </div>
                    </div>
                    <input type="hidden" asp-for="Input.SifrarnikBankiIdBanka" />
                    <div class="row" style="display: @(Model.Input.GeneriranaFakturaIzlezna ? "block" : "none")">
                        <div class="col-md-3 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" asp-for="Input.GeneriranaFakturaIzlezna" id="generiranaFakturaIzlezna" disabled>
                                <input type="hidden" asp-for="Input.GeneriranaFakturaIzlezna" />
                                <label class="form-check-label" for="generiranaFakturaIzlezna">
                                    Генерирана излезна фактура
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row" style="display: @(Model.Input.GeneriranaFakturaIzlezna ? "block" : "none")">
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.BrojNaFakturaIzlezna" class="form-label">Број на излезна фактура</label>
                            <input asp-for="Input.BrojNaFakturaIzlezna" class="form-control" readonly />
                            <span asp-validation-for="Input.BrojNaFakturaIzlezna" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row" style="display: @(Model.Input.GeneriranaFakturaIzlezna ? "block" : "none")">
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.DatumNaIzleznaFaktura" class="form-label">Датум на излезна фактура</label>
                            <input asp-for="Input.DatumNaIzleznaFaktura" class="form-control datepicker" type="date" readonly />
                            <span asp-validation-for="Input.DatumNaIzleznaFaktura" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.RokNaPlakjanjeFakturaIzlezna" class="form-label">Рок на плаќање на излезна фактура</label>
                            <input asp-for="Input.RokNaPlakjanjeFakturaIzlezna" class="form-control datepicker" type="date" readonly />
                            <span asp-validation-for="Input.RokNaPlakjanjeFakturaIzlezna" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" asp-for="Input.Storno" id="storno" disabled="@(!Model.HasStornoAccess)">
                                <label class="form-check-label" for="storno">
                                    Сторно
                                </label>
                                <input type="hidden" asp-for="Input.Storno" />
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label asp-for="Input.PricinaZaStorno" class="form-label">Причина за сторно</label>
                            <textarea asp-for="Input.PricinaZaStorno" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Input.PricinaZaStorno" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label asp-for="Input.Zabeleska" class="form-label">Забелешка</label>
                            <textarea asp-for="Input.Zabeleska" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Input.Zabeleska" class="text-danger"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Kasko Card -->
        <div class="card mb-4">
            <div class="card-header" role="button" data-bs-toggle="collapse" data-bs-target="#kaskoInfo" aria-expanded="false" aria-controls="kaskoInfo" style="cursor: pointer;">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Класа 4</h5>
                    <i class="bi bi-chevron-down"></i>
                </div>
            </div>
            <div class="collapse show" id="kaskoInfo">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label class="form-label">Датум на креирање</label>
                            <input type="text" class="form-control" value="@(Model.KaskoInput?.DateCreated?.ToString("dd.MM.yyyy HH:mm:ss"))" readonly />
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">Креирано од</label>
                            <input type="text" class="form-control" value="@Model.KaskoInput?.UsernameCreated" readonly />
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">Последна промена</label>
                            <input type="text" class="form-control" value="@(Model.KaskoInput?.DateModified?.ToString("dd.MM.yyyy HH:mm:ss"))" readonly />
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">Променето од</label>
                            <input type="text" class="form-control" value="@Model.KaskoInput?.UsernameModified" readonly />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="KaskoInput.OsigurenaVrednost" class="form-label">Вредност</label>
                            <input asp-for="KaskoInput.OsigurenaVrednost" class="form-control" type="number" step="0.0001" readonly />
                            <span asp-validation-for="KaskoInput.OsigurenaVrednost" class="text-danger"></span>
                        </div>
                        
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="KaskoInput.Opis" class="form-label">Опис</label>
                            <textarea asp-for="KaskoInput.Opis" class="form-control" readonly></textarea>
                            <span asp-validation-for="KaskoInput.Opis" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="KaskoInput.VkupnaPremija" class="form-label">Вкупна премија</label>
                            <input asp-for="KaskoInput.VkupnaPremija" class="form-control" type="number" step="0.0001" readonly />
                            <span asp-validation-for="KaskoInput.VkupnaPremija" class="text-danger"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Popusti Card -->
        <div class="card mb-4">
            <div class="card-header" role="button" data-bs-toggle="collapse" data-bs-target="#popustiInfo" aria-expanded="false" aria-controls="popustiInfo" style="cursor: pointer;">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Попусти</h5>
                    <i class="bi bi-chevron-down"></i>
                </div>
            </div>
            <div class="collapse show" id="popustiInfo">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="KaskoInput.ProcentNaPopustZaFakturaVoRok" class="form-label">Процент на попуст за фактура во рок</label>
                            <input asp-for="KaskoInput.ProcentNaPopustZaFakturaVoRok" class="form-control" type="number" step="0.01" readonly />
                            <span asp-validation-for="KaskoInput.ProcentNaPopustZaFakturaVoRok" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="KaskoInput.IznosZaPlakjanjeVoRok" class="form-label">Износ за плаќање во рок</label>
                            <input asp-for="KaskoInput.IznosZaPlakjanjeVoRok" class="form-control" type="number" step="0.0001" readonly />
                            <span asp-validation-for="KaskoInput.IznosZaPlakjanjeVoRok" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="KaskoInput.ProcentKomercijalenPopust" class="form-label">Процент комерцијален попуст</label>
                            <input asp-for="KaskoInput.ProcentKomercijalenPopust" class="form-control" type="number" step="0.01" readonly />
                            <span asp-validation-for="KaskoInput.ProcentKomercijalenPopust" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="KaskoInput.ProcentFinansiski" class="form-label">Процент финансиски</label>
                            <input asp-for="KaskoInput.ProcentFinansiski" class="form-control" type="number" step="0.01" readonly />
                            <span asp-validation-for="KaskoInput.ProcentFinansiski" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="KaskoInput.PremijaZaNaplata" class="form-label">Премија за наплата</label>
                            <input asp-for="KaskoInput.PremijaZaNaplata" class="form-control" type="number" step="0.0001" readonly />
                            <span asp-validation-for="KaskoInput.PremijaZaNaplata" class="text-danger"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Soobrakajna card -->
        <div class="card mb-4">
            <div class="card-header" role="button" data-bs-toggle="collapse" data-bs-target="#soobrakjajnaInfo" aria-expanded="false" aria-controls="soobrakjajnaInfo" style="cursor: pointer;">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Сообраќајна</h5>
                    <i class="bi bi-chevron-down"></i>
                </div>
            </div>
            <div class="collapse show" id="soobrakjajnaInfo">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label class="form-label">Датум на креирање</label>
                            <input type="text" class="form-control" value="@(Model.SoobrakjajnaInput.DateCreated?.ToString("dd.MM.yyyy HH:mm:ss"))" readonly />
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">Креирано од</label>
                            <input type="text" class="form-control" value="@Model.SoobrakjajnaInput.UsernameCreated" readonly />
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">Последна промена</label>
                            <input type="text" class="form-control" value="@(Model.SoobrakjajnaInput.DateModified?.ToString("dd.MM.yyyy HH:mm:ss"))" readonly />
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">Променето од</label>
                            <input type="text" class="form-control" value="@Model.SoobrakjajnaInput.UsernameModified" readonly />
                        </div>
                    </div>

<h5 class="mb-3">Група 1 - Основни податоци</h5>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.RegisterskaOznaka" class="form-label">Регистарска ознака</label>
                            <input asp-for="SoobrakjajnaInput.RegisterskaOznaka" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.RegisterskaOznaka" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.Marka" class="form-label">Марка</label>
                            <input asp-for="SoobrakjajnaInput.Marka" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.Marka" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.SifrarnikTipNaVozilo" class="form-label">Тип на возило</label>
                            <select asp-for="SoobrakjajnaInput.SifrarnikTipNaVozilo" 
                                    asp-items="Model.TipoviNaVozilo" 
                                    class="form-select">
                                <option value="">-- Избери тип на возило --</option>
                            </select>
                            <span asp-validation-for="SoobrakjajnaInput.SifrarnikTipNaVozilo" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.KomercijalnaOznaka" class="form-label">Комерцијална ознака</label>
                            <input asp-for="SoobrakjajnaInput.KomercijalnaOznaka" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.KomercijalnaOznaka" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.Shasija" class="form-label">Шасија</label>
                            <input asp-for="SoobrakjajnaInput.Shasija" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.Shasija" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.GodinaNaProizvodstvo" class="form-label">Година на производство</label>
                            <input asp-for="SoobrakjajnaInput.GodinaNaProizvodstvo" class="form-control" type="number" />
                            <span asp-validation-for="SoobrakjajnaInput.GodinaNaProizvodstvo" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.ZafatninaNaMotorotcm3" class="form-label">Зафатнина на моторот (cm³)</label>
                            <input asp-for="SoobrakjajnaInput.ZafatninaNaMotorotcm3" class="form-control" type="number" />
                            <span asp-validation-for="SoobrakjajnaInput.ZafatninaNaMotorotcm3" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.SilinaNaMotorotKW" class="form-label">Силина на моторот (kW)</label>
                            <input asp-for="SoobrakjajnaInput.SilinaNaMotorotKW" class="form-control" type="number" />
                            <span asp-validation-for="SoobrakjajnaInput.SilinaNaMotorotKW" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.BrojNaSedista" class="form-label">Број на седишта</label>
                            <input asp-for="SoobrakjajnaInput.BrojNaSedista" class="form-control" type="number" />
                            <span asp-validation-for="SoobrakjajnaInput.BrojNaSedista" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.BojaNaVoziloto" class="form-label">Боја на возилото</label>
                            <input asp-for="SoobrakjajnaInput.BojaNaVoziloto" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.BojaNaVoziloto" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.NosivostKG" class="form-label">Носивост (KG)</label>
                            <input asp-for="SoobrakjajnaInput.NosivostKG" class="form-control" type="number" />
                            <span asp-validation-for="SoobrakjajnaInput.NosivostKG" class="text-danger"></span>
                        </div>

<h5 class="mb-3">Група 2 - Дополнителни податоци</h5>

                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.DatumNaRegistracija" class="form-label">Датум на регистрација</label>
                            <input asp-for="SoobrakjajnaInput.DatumNaRegistracija" class="form-control" type="date" />
                            <span asp-validation-for="SoobrakjajnaInput.DatumNaRegistracija" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.BrojNaVpisot" class="form-label">Број на впис</label>
                            <input asp-for="SoobrakjajnaInput.BrojNaVpisot" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.BrojNaVpisot" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.DatumNaPrvataRegistracija" class="form-label">Датум на првата регистрација</label>
                            <input asp-for="SoobrakjajnaInput.DatumNaPrvataRegistracija" class="form-control" type="date" />
                            <span asp-validation-for="SoobrakjajnaInput.DatumNaPrvataRegistracija" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="SoobrakjajnaInput.PrezimeNazivNaKorisnikot" class="form-label">Презиме/Назив на корисникот</label>
                            <input asp-for="SoobrakjajnaInput.PrezimeNazivNaKorisnikot" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.PrezimeNazivNaKorisnikot" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="SoobrakjajnaInput.Ime" class="form-label">Име</label>
                            <input asp-for="SoobrakjajnaInput.Ime" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.Ime" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="SoobrakjajnaInput.AdresaNaPostojanoZivealiste" class="form-label">Адреса на постојано живеалиште</label>
                            <input asp-for="SoobrakjajnaInput.AdresaNaPostojanoZivealiste" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.AdresaNaPostojanoZivealiste" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="SoobrakjajnaInput.EMBNaKorisnikot" class="form-label">ЕМБГ на корисникот</label>
                            <input asp-for="SoobrakjajnaInput.EMBNaKorisnikot" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.EMBNaKorisnikot" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
	                            <label asp-for="SoobrakjajnaInput.DatumNaPrvaRegistracijaVoRSM" class="form-label">Датум на прва регистрација во РСМ</label>
                            <input asp-for="SoobrakjajnaInput.DatumNaPrvaRegistracijaVoRSM" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.DatumNaPrvaRegistracijaVoRSM" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
	                            <label asp-for="SoobrakjajnaInput.DozvolataJaIzdal" class="form-label">Дозволата ја издал</label>
                            <input asp-for="SoobrakjajnaInput.DozvolataJaIzdal" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.DozvolataJaIzdal" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.OznakaNaOdobrenie" class="form-label">Ознака на одобрение</label>
                            <input asp-for="SoobrakjajnaInput.OznakaNaOdobrenie" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.OznakaNaOdobrenie" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
	                            <label asp-for="SoobrakjajnaInput.BrojNAEUPotvrdaZaSoobraznost" class="form-label">Број на ЕУ потврда за сообразност</label>
                            <input asp-for="SoobrakjajnaInput.BrojNAEUPotvrdaZaSoobraznost" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.BrojNAEUPotvrdaZaSoobraznost" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.PrezimeNazivNaSopstvenikot" class="form-label">Презиме/Назив на сопственикот</label>
                            <input asp-for="SoobrakjajnaInput.PrezimeNazivNaSopstvenikot" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.PrezimeNazivNaSopstvenikot" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.ImeSopstvenik" class="form-label">Име на сопственикот</label>
                            <input asp-for="SoobrakjajnaInput.ImeSopstvenik" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.ImeSopstvenik" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="SoobrakjajnaInput.AdresaNaPostojanoZivealisteSediste" class="form-label">Адреса на постојано живеалиште/седиште</label>
                            <input asp-for="SoobrakjajnaInput.AdresaNaPostojanoZivealisteSediste" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.AdresaNaPostojanoZivealisteSediste" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="SoobrakjajnaInput.EMBNaFizickoLiceEMBNaPravnoLice" class="form-label">ЕМБГ на физичко лице/ЕМБС на правно лице</label>
                            <input asp-for="SoobrakjajnaInput.EMBNaFizickoLiceEMBNaPravnoLice" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.EMBNaFizickoLiceEMBNaPravnoLice" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="SoobrakjajnaInput.KategorijaIVidNaVoziloto" class="form-label">Категорија и вид на возилото</label>
                            <input asp-for="SoobrakjajnaInput.KategorijaIVidNaVoziloto" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.KategorijaIVidNaVoziloto" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="SoobrakjajnaInput.OblikINamenaNaKaroserijata" class="form-label">Облик и намена на каросеријата</label>
                            <input asp-for="SoobrakjajnaInput.OblikINamenaNaKaroserijata" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.OblikINamenaNaKaroserijata" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.TipNaMotorot" class="form-label">Тип на моторот</label>
                            <input asp-for="SoobrakjajnaInput.TipNaMotorot" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.TipNaMotorot" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.VidNaGorivo" class="form-label">Вид на гориво</label>
                            <input asp-for="SoobrakjajnaInput.VidNaGorivo" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.VidNaGorivo" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.BrojNaVrtezi" class="form-label">Број на вртежи</label>
                            <input asp-for="SoobrakjajnaInput.BrojNaVrtezi" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.BrojNaVrtezi" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
	                            <label asp-for="SoobrakjajnaInput.IdentifikacionenBrojNaMotorot" class="form-label">Идентификационен број на моторот</label>
                            <input asp-for="SoobrakjajnaInput.IdentifikacionenBrojNaMotorot" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.IdentifikacionenBrojNaMotorot" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label asp-for="SoobrakjajnaInput.MaksimalnaBrzinaKM" class="form-label">Максимална брзина (km/h)</label>
                            <input asp-for="SoobrakjajnaInput.MaksimalnaBrzinaKM" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.MaksimalnaBrzinaKM" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label asp-for="SoobrakjajnaInput.OdnosSilinaMasa" class="form-label">Однос силина/маса</label>
                            <input asp-for="SoobrakjajnaInput.OdnosSilinaMasa" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.OdnosSilinaMasa" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
	                            <label asp-for="SoobrakjajnaInput.MasaNaVoziloto" class="form-label">Маса на возилото</label>
                            <input asp-for="SoobrakjajnaInput.MasaNaVoziloto" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.MasaNaVoziloto" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG" class="form-label">Најголема конструктивна вкупна маса на возилото (kg)</label>
                            <input asp-for="SoobrakjajnaInput.NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG" class="form-label">Најголема легална вкупна маса при регистрација (kg)</label>
                            <input asp-for="SoobrakjajnaInput.NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="SoobrakjajnaInput.NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG" class="form-label">Најголема легална вкупна маса на група возила при регистрација (kg)</label>
                            <input asp-for="SoobrakjajnaInput.NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="SoobrakjajnaInput.BrojNaOski" class="form-label">Број на оски</label>
                            <input asp-for="SoobrakjajnaInput.BrojNaOski" class="form-control" type="number" />
                            <span asp-validation-for="SoobrakjajnaInput.BrojNaOski" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="SoobrakjajnaInput.RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka" class="form-label">Распределба на најголемата конструктивна вкупна маса по оски (kg) и на приклучната точка</label>
                            <input asp-for="SoobrakjajnaInput.RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="SoobrakjajnaInput.NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka" class="form-label">Најголемо конструктивно осно оптоварување (kg) и на приклучната точка</label>
                            <input asp-for="SoobrakjajnaInput.NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.Dolzhina" class="form-label">Должина (mm)</label>
                            <input asp-for="SoobrakjajnaInput.Dolzhina" class="form-control" type="number" />
                            <span asp-validation-for="SoobrakjajnaInput.Dolzhina" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.Visina" class="form-label">Висина (mm)</label>
                            <input asp-for="SoobrakjajnaInput.Visina" class="form-control" type="number" />
                            <span asp-validation-for="SoobrakjajnaInput.Visina" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG" class="form-label">Најголема конструктивна вкупна маса на кочена приколка (kg)</label>
                            <input asp-for="SoobrakjajnaInput.NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG" class="form-label">Најголема конструктивна вкупна маса на некочена приколка (kg)</label>
                            <input asp-for="SoobrakjajnaInput.NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.BrojNaMestaZaStoenje" class="form-label">Број на места за стоење</label>
                            <input asp-for="SoobrakjajnaInput.BrojNaMestaZaStoenje" class="form-control" type="number" />
                            <span asp-validation-for="SoobrakjajnaInput.BrojNaMestaZaStoenje" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="SoobrakjajnaInput.DozvoleniPnevmaticiINaplatki" class="form-label">Дозволени пневматици и наплатки</label>
                            <input asp-for="SoobrakjajnaInput.DozvoleniPnevmaticiINaplatki" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.DozvoleniPnevmaticiINaplatki" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label asp-for="SoobrakjajnaInput.BrojNaMestazaLezenje" class="form-label">Број на места за лежење</label>
                            <input asp-for="SoobrakjajnaInput.BrojNaMestazaLezenje" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.BrojNaMestazaLezenje" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label asp-for="SoobrakjajnaInput.CO2" class="form-label">CO₂</label>
                            <input asp-for="SoobrakjajnaInput.CO2" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.CO2" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label asp-for="SoobrakjajnaInput.NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka" class="form-label">Најголемо конструктивно оптоварување на приклучокот за приколка</label>
                            <input asp-for="SoobrakjajnaInput.NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label asp-for="SoobrakjajnaInput.StacionarnaBucavost" class="form-label">Стационарна бучавост</label>
                            <input asp-for="SoobrakjajnaInput.StacionarnaBucavost" class="form-control" />
                            <span asp-validation-for="SoobrakjajnaInput.StacionarnaBucavost" class="text-danger"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Kartica Card -->
        <div class="card mb-4">
            <div class="card-header" role="button" data-bs-toggle="collapse" data-bs-target="#karticaInfo" aria-expanded="false" aria-controls="karticaInfo" style="cursor: pointer;">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Картица</h5>
                    <i class="bi bi-chevron-down"></i>
                </div>
            </div>
            <div class="collapse show" id="karticaInfo">
                <div class="card-body">
                    @if (Model.KarticaData != null && Model.KarticaData.Rows.Count > 0)
                    {
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        @foreach (System.Data.DataColumn col in Model.KarticaData.Columns)
                                        {
                                            <th>@col.ColumnName</th>
                                        }
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (System.Data.DataRow row in Model.KarticaData.Rows)
                                    {
                                        <tr>
                                            <td>@row["ID"]</td>
                                            <td>@row["Рата број"]</td>
                                            <td>@(row["Датум на доспевање"] != DBNull.Value ? Convert.ToDateTime(row["Датум на доспевање"]).ToString("dd.MM.yyyy") : "")</td>
                                            <td class="text-end">@(row["Износ на рата"] != DBNull.Value ? Convert.ToDecimal(row["Износ на рата"]).ToString("N2") : "")</td>
                                            <td>@(row["Датум на уплата"] != DBNull.Value ? Convert.ToDateTime(row["Датум на уплата"]).ToString("dd.MM.yyyy") : "")</td>
                                            <td class="text-end">@(row["Уплатен износ"] != DBNull.Value ? Convert.ToDecimal(row["Уплатен износ"]).ToString("N2") : "")</td>
                                            <td class="text-center">
                                                @if (row["Затворена рата"] != DBNull.Value && Convert.ToBoolean(row["Затворена рата"]))
                                                {
                                                    @:Да
                                                }
                                            </td>
                                            <td class="text-center">
                                                @if (row["Сторно"] != DBNull.Value && Convert.ToBoolean(row["Сторно"]))
                                                {
                                                    @:Да
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <p class="text-muted mb-0">Нема податоци во картица.</p>
                    }

                    <!-- OZ Information Section -->
                    @if (Model.OZIznosIzleznaFakturaPremija != 0 || Model.OZIznosPolisa != 0)
                    {
                        <hr class="my-4">
                        <h6 class="text-primary mb-3">Одобрување / Задолжување информации</h6>
                        <div class="row">
                            @if (Model.OZIznosIzleznaFakturaPremija != 0)
                            {
                                <div class="col-12 mb-2">
                                    <div class="alert alert-info mb-2">
                                        <strong>За фактурата од оваа полиса е пресметано одобрување / задолжување со износ од:</strong> 
                                        <span class="fw-bold">@Model.OZIznosIzleznaFakturaPremija.ToString("N2")</span>
                                    </div>
                                </div>
                            }
                            @if (Model.OZIznosPolisa != 0)
                            {
                                <div class="col-12 mb-2">
                                    <div class="alert alert-warning mb-2">
                                        <strong>За оваа полиса е пресметано одобрување / задолжување со износ од:</strong> 
                                        <span class="fw-bold">@Model.OZIznosPolisa.ToString("N2")</span>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>

         
        <!-- Generiraj izlezna faktura -->
        <div class="card mb-4" style="@(Model.Input.TipNaFaktura == "VleznaFakturaKonKlient" || Model.Input.TipNaFaktura == "Влезна фактура кон клиент" ? "display:none;" : "")">
            <div class="card-header" role="button" data-bs-toggle="collapse" data-bs-target="#izleznaFakturaInfo" aria-expanded="false" aria-controls="izleznaFakturaInfo" style="cursor: pointer;">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Менаџирање со фактури</h5>
                    <i class="bi bi-chevron-down"></i>
                </div>
            <div class="collapse show" id="izleznaFakturaInfo">
                <div class="card-body">
                    <button type="button" class="btn btn-primary" onclick="openGenerirajFakturaPopup(@Model.Input.Id)">
                        <i class="bi bi-file-earmark-text"></i> Приказ и генерирање фактури за полиса
                    </button>
                </div>
            </div>
            <script>
                function openGenerirajFakturaPopup(polisaId) {
                    window.open('/Finansii/GenerirajIzleznaFakturaKonKlient?id=' + polisaId, 'GenerirajFaktura', 
                        'width=1024,height=768,resizable=yes,scrollbars=yes,status=yes');
                }
            </script>
            </div>
        </div>



        <!-- Files Card -->
        <div class="card mb-4">
            <div class="card-header" role="button" data-bs-toggle="collapse" data-bs-target="#filesInfo" aria-expanded="false" aria-controls="filesInfo" style="cursor: pointer;">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Датотеки</h5>
                    <i class="bi bi-chevron-down"></i>
                </div>
            </div>
            <div class="collapse show" id="filesInfo">
                <div class="card-body">
                    @if (Model.Files != null && Model.Files.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Име на документ</th>
                                        <th>Датум на прикачување</th>
                                        <th>Прикачил</th>
                                        <th>Акции</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var file in Model.Files)
                                    {
                                        <tr>
                                            <td>@file.FileName</td>
                                            <td>@file.DateCreated.ToString("dd.MM.yyyy HH:mm")</td>
                                            <td>@file.UsernameCreated</td>
                                            <td>
                                                <a asp-page-handler="DownloadFile" asp-route-fileId="@file.Id" 
                                                   class="btn btn-sm btn-primary">
                                                    <i class="fas fa-download"></i> Преземи
                                                </a>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <p class="text-muted mb-0">Нема прикачени документи.</p>
                    }
                </div>
            </div>
        </div>


             <!-- Задолжување/Раздолжување Card -->
     <div class="card mb-4">
         <div class="card-header" role="button" data-bs-toggle="collapse" data-bs-target="#zadolzuvanjeInfo" aria-expanded="false" aria-controls="zadolzuvanjeInfo" style="cursor: pointer;">
             <div class="d-flex justify-content-between align-items-center">
                 <h5 class="mb-0">Задолжување/Раздолжување</h5>
                 <i class="bi bi-chevron-down"></i>
             </div>
         </div>
         <div class="collapse show" id="zadolzuvanjeInfo">
             <div class="card-body">
                 @if (Model.ZadolzuvanjeData != null)
                 {
                     <div class="row g-3">
                         <div class="col-md-4">
                             <label class="form-label fw-bold">Задолжен</label>
                             <div>@(Model.ZadolzuvanjeData.Zadolzen ?? "-")</div>
                         </div>
                         <div class="col-md-4">
                             <label class="form-label fw-bold">Датум на задолжување</label>
                             <div>@(Model.ZadolzuvanjeData.DatumNaZadolzuvanje?.ToString("dd.MM.yyyy") ?? "-")</div>
                         </div>
                         <div class="col-md-4">
                             <label class="form-label fw-bold">Основ за раздолжување</label>
                             <div>@(Model.ZadolzuvanjeData.OsnovZaRazdolzuvanje ?? "-")</div>
                         </div>
                         <div class="col-md-3">
                             <label class="form-label fw-bold">Потврдено кај брокер</label>
                             <div>@(Model.ZadolzuvanjeData.PotvrdenoRazdolzuvanjeKajBroker ? "Да" : "Не")</div>
                         </div>
                         <div class="col-md-3">
                             <label class="form-label fw-bold">Датум на раздолжување кај брокер</label>
                             <div>@(Model.ZadolzuvanjeData.DatumNaRazdolzuvanjeKajBroker?.ToString("dd.MM.yyyy") ?? "-")</div>
                         </div>
                         <div class="col-md-3">
                             <label class="form-label fw-bold">Потврдено кај осигурител</label>
                             <div>@(Model.ZadolzuvanjeData.PotvrdenoRazdolzuvanjeKajOsiguritel ? "Да" : "Не")</div>
                         </div>
                         <div class="col-md-3">
                             <label class="form-label fw-bold">Датум на раздолжување кај осигурител</label>
                             <div>@(Model.ZadolzuvanjeData.DatumNaRazdolzuvanjeKajOsiguritel?.ToString("dd.MM.yyyy") ?? "-")</div>
                         </div>
                     </div>
                 }
                 else
                 {
                     <div class="alert alert-info mb-0">Нема податоци за задолжување/раздолжување</div>
                 }
             </div>
         </div>
     </div>


        <!-- Save Button -->
        <div class="row mt-4">
            <div class="col-12">
                @if (Model.HasAdminAccess)
                {
                    <button type="submit" class="btn btn-primary">Зачувај промени</button>
                }
                <!-- Add the new Upload Files button -->
                @if (Model.Input.Id > 0 && Model.HasAdminAccess)
                {
                    <a href="/Polisi/PolisiFileUpload/@Model.Input.Id" class="btn btn-secondary ms-2">
                        <i class="bi bi-upload"></i> Прикачи документи
                    </a>
                }
            </div>
        </div>
    </form>
</div>

@section Scripts {
    <script>
        // Auto-hide success message after 3 seconds
        $(document).ready(function () {
            // Ensure read-only selects cannot be changed
            $('.select-readonly').on('mousedown', function(e) {
                e.preventDefault();
                return false;
            });

            // Date validation for Важи од/до
            const vaziOdInput = $('#Input_DatumVaziOd');
            const vaziDoInput = $('#Input_DatumVaziDo');
            const vaziOdError = $('<span class="text-danger field-validation-error"></span>');
            
            vaziOdInput.after(vaziOdError);

            function updateDates() {
                const vaziOd = vaziOdInput.val();
                const vaziDo = vaziDoInput.val();

                if (vaziDo && !vaziOd) {
                    vaziOdError.text('Важи од е задолжително поле');
                    vaziOdInput.addClass('input-validation-error');
                    return;
                }

                if (vaziOd && vaziDo) {
                    if (new Date(vaziDo) < new Date(vaziOd)) {
                        vaziOdInput.val(vaziDo);
                    }
                    vaziOdError.text('');
                    vaziOdInput.removeClass('input-validation-error');
                }
            }

            vaziOdInput.on('change', updateDates);
            vaziDoInput.on('change', updateDates);

            // Add custom validation method
            $.validator.addMethod('dateGreaterThan', function(value, element, params) {
                if (!value || !$(params).val()) return true;
                
                const startDate = new Date($(params).val());
                const endDate = new Date(value);
                
                if (endDate < startDate) {
                    $(params).val(value);
                    return true;
                }
                return true;
            });

            $.validator.unobtrusive.adapters.add('dateGreaterThan', ['otherdate'], function(options) {
                options.rules['dateGreaterThan'] = '#' + options.params.otherdate;
                options.messages['dateGreaterThan'] = options.message;
            });

            setTimeout(function () {
                $("#successMessage").fadeOut('slow');
            }, 3000);
        });

        $(document).ready(function() {
            // Function to create search functionality
            function createSearchFunctionality(searchInputId, resultsContainerId, hiddenInputId) {
                let searchTimeout;

                $(`#${searchInputId}`).on('input', function() {
                    clearTimeout(searchTimeout);
                    const searchTerm = $(this).val();
                    const resultsDiv = $(`#${resultsContainerId}`);

                    if (searchTerm.length < 2) {
                        resultsDiv.hide();
                        return;
                    }

                    searchTimeout = setTimeout(function() {
                        $.ajax({
                            url: `?handler=SearchKlienti`,
                            type: 'GET',
                            data: { mb: searchTerm },
                            success: function(data) {
                                if (data && data.length > 0) {
                                    let html = '<div class="list-group">';
                                    data.forEach(item => {
                                        let displayText = '';
                                        if (item.fizickoPravno === 'P') {
                                            displayText = item.naziv;
                                        } else {
                                            displayText = `${item.ime} ${item.prezime}`;
                                        }

                                        html += `<a href="#" class="list-group-item list-group-item-action" 
                                                  data-id="${item.id}" 
                                                  data-naziv="${displayText}">
                                                <div><strong>${displayText}</strong></div>
                                                <div class="small">
                                                    ${item.mb ? 'МБ: ' + item.mb : ''}
                                                    ${item.edb ? 'ЕДБ: ' + item.edb : ''}
                                                    ${item.embg ? 'ЕМБГ: ' + item.embg : ''}
                                                </div>
                                               </a>`;
                                    });
                                    html += '</div>';
                                    resultsDiv.html(html).show();
                                } else {
                                    resultsDiv.html('<div class="p-2">Нема резултати</div>').show();
                                }
                            }
                        });
                    }, 300);
                });

                // Handle selection
                $(document).on('click', `#${resultsContainerId} .list-group-item`, function(e) {
                    e.preventDefault();
                    const id = $(this).data('id');
                    const naziv = $(this).data('naziv');
                    $(`#${searchInputId}`).val(naziv);
                    $(`#${hiddenInputId}`).val(id);
                    $(`#${resultsContainerId}`).hide();
                });

                // Hide results when clicking outside
                $(document).on('click', function(e) {
                    if (!$(e.target).closest(`#${searchInputId}, #${resultsContainerId}`).length) {
                        $(`#${resultsContainerId}`).hide();
                    }
                });
            }

            // Initialize search for Dogovoruvac
            createSearchFunctionality('dogovoruvacMBSearch', 'dogovoruvacSearchResults', 'KlientiIdDogovoruvac');

            // Initialize search for Osigurenik
            createSearchFunctionality('osigurenikMBSearch', 'osigurenikSearchResults', 'KlientiIdOsigurenik');

            // Initialize search for Sorabotnik
            createSearchFunctionality('sorabotnikMBSearch', 'sorabotnikSearchResults', 'KlientiIdSorabotnik');

            // Add this new code for the nullify button
            $('#nullifySorabotnik').click(function() {
	                if (confirm('Дали сте сигурни дека сакате да го анулирате соработникот?')) {
                    $.ajax({
                        url: `?handler=NullifySorabotnik`,
                        type: 'POST',
                        data: {
                            id: @Model.Input.Id
                        },
                        headers: {
                            "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                        },
                        success: function(response) {
                            if (response.success) {
                                $('#sorabotnikMBSearch').val('');
                                $('#KlientiIdSorabotnik').val('');
                                location.reload();
                            }
                        }
                    });
                }
            });
        });

        // Collapse All Functionality
        $(document).ready(function() {
            const collapseAllBtn = $('#collapseAllBtn');
            const sections = $('.collapse');
            const icons = $('[data-bs-toggle="collapse"] .bi');
            let isAllCollapsed = false;

            // Initialize collapse icons
            icons.css('transform', 'rotate(180deg)');

            // Add click handlers for individual section headers
            $('[data-bs-toggle="collapse"]').on('click', function() {
                const icon = $(this).find('.bi');
                icon.css('transform', icon.css('transform') === 'rotate(180deg)' ? '' : 'rotate(180deg)');
            });

            // Handle collapse all button
            collapseAllBtn.on('click', function() {
                isAllCollapsed = !isAllCollapsed;
                if (isAllCollapsed) {
                    sections.collapse('hide');
                    icons.css('transform', '');
                    $(this).html('<i class="bi bi-chevron-down"></i> Отвори ги сите секции');
                } else {
                    sections.collapse('show');
                    icons.css('transform', 'rotate(180deg)');
                    $(this).html('<i class="bi bi-chevron-up"></i> Затвори ги сите секции');
                }
            });

            // Listen for collapse/show events to maintain icon state
            sections.on('show.bs.collapse', function() {
                $(this).prev().find('.bi').css('transform', 'rotate(180deg)');
            }).on('hide.bs.collapse', function() {
                $(this).prev().find('.bi').css('transform', '');
            });
        });
    </script>
}