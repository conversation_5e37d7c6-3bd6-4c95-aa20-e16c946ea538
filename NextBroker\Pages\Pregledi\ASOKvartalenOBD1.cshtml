@page
@model NextBroker.Pages.Pregledi.ASOKvartalenOBD1Model
@{
    ViewData["Title"] = "АСО Квартален ОБД1";
}

<div class="container-fluid">
    <h2 class="mb-4">АСО Квартален ОБД1</h2>
    @if (Model.SelectedOsiguritelId.HasValue && Model.OsiguritelOptions != null)
    {
        var selectedOsiguritel = Model.OsiguritelOptions.FirstOrDefault(x => x.Value == Model.SelectedOsiguritelId.ToString());
        if (selectedOsiguritel != null)
        {
            <div class="alert alert-info">
                <i class="fas fa-building me-2"></i> Избран осигурител: <strong>@selectedOsiguritel.Text</strong>
            </div>
        }
    }
    <form method="post" class="row g-3 mb-4">
        <div class="col-auto">
            <label asp-for="SelectedYear" class="form-label">Година</label>
            <select asp-for="SelectedYear" asp-items="Model.YearOptions" class="form-select"></select>
        </div>
        <div class="col-auto">
            <label asp-for="SelectedQuarter" class="form-label">Квартал</label>
            <select asp-for="SelectedQuarter" asp-items="Model.QuarterOptions" class="form-select"></select>
        </div>
        <div class="col-auto">
            <label asp-for="SelectedOsiguritelId" class="form-label">Осигурител</label>
            <select asp-for="SelectedOsiguritelId" asp-items="Model.OsiguritelOptions" class="form-select">
                <option value="">Сите осигурители</option>
            </select>
        </div>
        <div class="col-auto align-self-end">
            @if (Model.TableRows == null || Model.TableRows.Count == 0)
            {
                <button type="submit" class="btn btn-primary">Преглед</button>
            }
            else
            {
                <button type="submit" class="btn btn-outline-primary me-2">Нов преглед</button>
                <a href="@Url.Page(null)" class="btn btn-outline-secondary">Откажи</a>
            }
        </div>
    </form>
    
    @if (Model.TableRows != null && Model.TableRows.Count > 0)
    {
        <!-- Debug Information -->
        <div class="alert alert-info">
            <h6>Debug Information:</h6>
            <p><strong>Selected Osiguritel ID:</strong> @Model.SelectedOsiguritelId</p>
            <p><strong>Debug Column Names:</strong> @string.Join(", ", Model.DebugColumnNames)</p>
            <p><strong>Sample Realized Commission Values:</strong></p>
            <ul>
                @for (int i = 0; i < Math.Min(5, Model.TableRows.Count); i++)
                {
                    <li>@Model.TableRows[i].Name: @Model.TableRows[i].RealiziranaProvizija (Original: @Model.TableRows[i].OriginalRealiziranaProvizija)</li>
                }
            </ul>
        </div>
        
        <div class="row mb-3">
            @if (!Model.IsConfirmed)
            {
                <div class="col-auto">
                    <form method="post" asp-page-handler="Confirm" id="confirmForm" style="display:inline;">
                        <input type="hidden" name="SelectedYear" value="@Model.SelectedYear" />
                        <input type="hidden" name="SelectedQuarter" value="@Model.SelectedQuarter" />
                        <input type="hidden" name="SelectedOsiguritelId" value="@Model.SelectedOsiguritelId" />
                        @for (int i = 0; i < Model.TableRows.Count; i++)
                        {
                            <input type="hidden" name="TableRows[@i].Code" value="@Model.TableRows[i].Code" />
                            <input type="hidden" name="TableRows[@i].Name" value="@Model.TableRows[i].Name" />
                            <input type="hidden" name="TableRows[@i].BrojDogovori" value="@Model.TableRows[i].BrojDogovori" />
                            <input type="hidden" name="TableRows[@i].Premija" value="@Model.TableRows[i].Premija" />
                            <input type="hidden" name="TableRows[@i].RealiziranaProvizija" value="@Model.TableRows[i].RealiziranaProvizija" />
                            <input type="hidden" name="TableRows[@i].PresmetanaProvizija" value="@Model.TableRows[i].PresmetanaProvizija" />
                        }
                        <button type="submit" class="btn btn-success me-2">Потврди</button>
                    </form>
                </div>
                <div class="col-auto">
                    <form method="post" asp-page-handler="CancelPreview" id="cancelPreviewForm" style="display:inline;">
                        <input type="hidden" name="SelectedYear" value="@Model.SelectedYear" />
                        <input type="hidden" name="SelectedQuarter" value="@Model.SelectedQuarter" />
                        <input type="hidden" name="SelectedOsiguritelId" value="@Model.SelectedOsiguritelId" />
                        <button type="submit" class="btn btn-secondary me-2">Откажи</button>
                    </form>
                </div>
            }
            <div class="col-auto">
                <form method="post" asp-page-handler="ExportExcel" id="exportExcelForm" style="display:inline;">
                    <input type="hidden" name="SelectedYear" value="@Model.SelectedYear" />
                    <input type="hidden" name="SelectedQuarter" value="@Model.SelectedQuarter" />
                    <input type="hidden" name="SelectedOsiguritelId" value="@Model.SelectedOsiguritelId" />
                    @for (int i = 0; i < Model.TableRows.Count; i++)
                    {
                        <input type="hidden" name="TableRows[@i].Code" value="@Model.TableRows[i].Code" />
                        <input type="hidden" name="TableRows[@i].Name" value="@Model.TableRows[i].Name" />
                        <input type="hidden" name="TableRows[@i].BrojDogovori" value="@Model.TableRows[i].BrojDogovori" />
                        <input type="hidden" name="TableRows[@i].Premija" value="@Model.TableRows[i].Premija" />
                        <input type="hidden" name="TableRows[@i].RealiziranaProvizija" value="@Model.TableRows[i].RealiziranaProvizija" />
                        <input type="hidden" name="TableRows[@i].PresmetanaProvizija" value="@Model.TableRows[i].PresmetanaProvizija" />
                    }
                    <button type="submit" class="btn btn-outline-primary me-2">Export to Excel</button>
                </form>
            </div>
            <div class="col-auto">
                <form method="post" asp-page-handler="ExportPdf" id="exportPdfForm" style="display:inline;">
                    <input type="hidden" name="SelectedYear" value="@Model.SelectedYear" />
                    <input type="hidden" name="SelectedQuarter" value="@Model.SelectedQuarter" />
                    <input type="hidden" name="SelectedOsiguritelId" value="@Model.SelectedOsiguritelId" />
                    @for (int i = 0; i < Model.TableRows.Count; i++)
                    {
                        <input type="hidden" name="TableRows[@i].Code" value="@Model.TableRows[i].Code" />
                        <input type="hidden" name="TableRows[@i].Name" value="@Model.TableRows[i].Name" />
                        <input type="hidden" name="TableRows[@i].BrojDogovori" value="@Model.TableRows[i].BrojDogovori" />
                        <input type="hidden" name="TableRows[@i].Premija" value="@Model.TableRows[i].Premija" />
                        <input type="hidden" name="TableRows[@i].RealiziranaProvizija" value="@Model.TableRows[i].RealiziranaProvizija" />
                        <input type="hidden" name="TableRows[@i].PresmetanaProvizija" value="@Model.TableRows[i].PresmetanaProvizija" />
                    }
                    <button type="submit" class="btn btn-outline-danger">Export to PDF</button>
                </form>
            </div>
        </div>
        @if (Model.IsConfirmed)
        {
            <div class="alert alert-success mt-3">
                <i class="fas fa-check-circle me-2"></i> Податоците се зачувани во базата на податоци. Може да ги експортирате.
            </div>
        }
        else
        {
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i> Ова е преглед на податоците. Кликнете "Потврди" за да ги зачувате во базата на податоци.
            </div>
        }

        <div class="table-responsive">
            <table class="table table-bordered table-striped align-middle">
                <thead class="table-light">
                    <tr>
                        <th>Код</th>
                        <th>Класа</th>
                        <th>Број на договори</th>
                        <th>Премија</th>
                        <th>Реализирана провизија</th>
                        <th>Пресметана провизија</th>
                    </tr>
                </thead>
                <tbody>
                @for (int i = 0; i < Model.TableRows.Count; i++)
                {
                    <tr>
                        <td>@Model.TableRows[i].Code</td>
                        <td>@Model.TableRows[i].Name</td>
                        <td>
                            <input type="number" class="form-control" name="TableRows[@i].BrojDogovori" value="@Model.TableRows[i].BrojDogovori" @(Model.IsConfirmed ? "disabled" : "") />
                        </td>
                        <td>
                            <input type="number" step="1" class="form-control" name="TableRows[@i].Premija" value="@(Model.TableRows[i].Premija?.ToString("F0"))" @(Model.IsConfirmed ? "disabled" : "") />
                        </td>
                        <td>
                            <input type="number" step="1" class="form-control" name="TableRows[@i].RealiziranaProvizija" value="@(Model.TableRows[i].RealiziranaProvizija?.ToString("F0"))" @(Model.IsConfirmed ? "disabled" : "") />
                        </td>
                        <td>
                            <input type="number" step="1" class="form-control" name="TableRows[@i].PresmetanaProvizija" value="@(Model.TableRows[i].PresmetanaProvizija?.ToString("F0"))" @(Model.IsConfirmed ? "disabled" : "") />
                        </td>
                    </tr>
                }
                </tbody>
                <tfoot>
                    <tr>
                        <td><strong>ВКУПНО</strong></td>
                        <td></td>
                        <td><strong id="vkupno-broj">@Model.TotalBrojDogovori</strong></td>
                        <td><strong id="vkupno-premija">@(Model.TotalPremija?.ToString("F0"))</strong></td>
                        <td><strong id="vkupno-realizirana">@(Model.TotalRealiziranaProvizija?.ToString("F0"))</strong></td>
                        <td><strong id="vkupno-presmetana">@(Model.TotalPresmetanaProvizija?.ToString("F0"))</strong></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    }
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        console.log('ASOKvartalenOBD1: Page loaded');
        console.log('ASOKvartalenOBD1: Current URL:', window.location.href);
        
        // Store original server-side totals
        const originalTotals = {
            brojDogovori: parseInt(document.getElementById('vkupno-broj').textContent) || 0,
            premija: parseFloat(document.getElementById('vkupno-premija').textContent) || 0,
            realizirana: parseFloat(document.getElementById('vkupno-realizirana').textContent) || 0,
            presmetana: parseFloat(document.getElementById('vkupno-presmetana').textContent) || 0
        };
        
        console.log('ASOKvartalenOBD1: Original server-side totals:', originalTotals);
        
        // Custom rounding function that matches C# MidpointRounding.AwayFromZero
        function roundHalfUp(value) {
            // If the value is already an integer, return it as is
            if (value % 1 === 0) {
                console.log(`roundHalfUp(${value}) = ${value} (already integer)`);
                return value;
            }
            const result = Math.round(value + (value >= 0 ? 0.5 : -0.5));
            console.log(`roundHalfUp(${value}) = ${result}`);
            return result;
        }
        
        // Store original input values for each row
        const originalInputValues = [];
        const tableRows = document.querySelectorAll('tbody tr');
        
        tableRows.forEach((row, index) => {
            const brojDogovoriInput = row.querySelector('input[name$=".BrojDogovori"]');
            const premijaInput = row.querySelector('input[name$=".Premija"]');
            const realiziranaInput = row.querySelector('input[name$=".RealiziranaProvizija"]');
            const presmetanaInput = row.querySelector('input[name$=".PresmetanaProvizija"]');
            
            originalInputValues[index] = {
                brojDogovori: parseInt(brojDogovoriInput?.value) || 0,
                premija: parseFloat(premijaInput?.value) || 0,
                realizirana: parseFloat(realiziranaInput?.value) || 0,
                presmetana: parseFloat(presmetanaInput?.value) || 0
            };
        });
        
        console.log('ASOKvartalenOBD1: Original input values:', originalInputValues);
        
        // Add form submission debugging
        const forms = document.querySelectorAll('form[method="post"]');
        console.log('ASOKvartalenOBD1: Found', forms.length, 'forms');
        
        forms.forEach((form, index) => {
            console.log('ASOKvartalenOBD1: Form', index, 'action:', form.action);
            form.addEventListener('submit', function(e) {
                console.log('ASOKvartalenOBD1: Form', index, 'submitted');
                console.log('ASOKvartalenOBD1: Form action:', form.action);
                
                const yearSelect = form.querySelector('select[name="SelectedYear"]');
                const quarterSelect = form.querySelector('input[name="SelectedYear"]');
                const yearInput = form.querySelector('input[name="SelectedYear"]');
                const quarterInput = form.querySelector('input[name="SelectedQuarter"]');
                const osiguritelInput = form.querySelector('input[name="SelectedOsiguritelId"]');
                
                console.log('ASOKvartalenOBD1: Year select value:', yearSelect?.value);
                console.log('ASOKvartalenOBD1: Quarter select value:', quarterSelect?.value);
                console.log('ASOKvartalenOBD1: Year input value:', yearInput?.value);
                console.log('ASOKvartalenOBD1: Quarter input value:', quarterInput?.value);
                console.log('ASOKvartalenOBD1: Osiguritel input value:', osiguritelInput?.value);
            });
        });
        
        // Check if we have any data
        console.log('ASOKvartalenOBD1: Found', tableRows.length, 'table rows');
        
        // Log initial input values
        console.log('ASOKvartalenOBD1: Initial input values:');
        tableRows.forEach((row, index) => {
            const brojDogovoriInput = row.querySelector('input[name$=".BrojDogovori"]');
            const premijaInput = row.querySelector('input[name$=".Premija"]');
            const realiziranaInput = row.querySelector('input[name$=".RealiziranaProvizija"]');
            const presmetanaInput = row.querySelector('input[name$=".PresmetanaProvizija"]');
            
            console.log(`Row ${index}: BrojDogovori=${brojDogovoriInput?.value}, Premija=${premijaInput?.value}, Realizirana=${realiziranaInput?.value}, Presmetana=${presmetanaInput?.value}`);
        });
        
        // Add functionality to update confirm form with current input values
        const confirmForm = document.getElementById('confirmForm');
        if (confirmForm) {
            confirmForm.addEventListener('submit', function(e) {
                console.log('ASOKvartalenOBD1: Confirm form submitted, updating hidden fields');
                
                // Update hidden fields with current input values
                for (let i = 0; i < tableRows.length; i++) {
                    const row = tableRows[i];
                    const brojDogovoriInput = row.querySelector('input[name$=".BrojDogovori"]');
                    const premijaInput = row.querySelector('input[name$=".Premija"]');
                    const realiziranaInput = row.querySelector('input[name$=".RealiziranaProvizija"]');
                    const presmetanaInput = row.querySelector('input[name$=".PresmetanaProvizija"]');
                    
                    // Update hidden fields
                    const brojDogovoriHidden = confirmForm.querySelector(`input[name="TableRows[${i}].BrojDogovori"]`);
                    const premijaHidden = confirmForm.querySelector(`input[name="TableRows[${i}].Premija"]`);
                    const realiziranaHidden = confirmForm.querySelector(`input[name="TableRows[${i}].RealiziranaProvizija"]`);
                    const presmetanaHidden = confirmForm.querySelector(`input[name="TableRows[${i}].PresmetanaProvizija"]`);
                    
                    // Update Osiguritel hidden field
                    const osiguritelHidden = confirmForm.querySelector('input[name="SelectedOsiguritelId"]');
                    if (osiguritelHidden) {
                        const osiguritelSelect = document.querySelector('select[name="SelectedOsiguritelId"]');
                        osiguritelHidden.value = osiguritelSelect ? osiguritelSelect.value : '';
                        console.log(`ASOKvartalenOBD1: Updated SelectedOsiguritelId = ${osiguritelHidden.value}`);
                    }
                    
                    if (brojDogovoriHidden && brojDogovoriInput) {
                        brojDogovoriHidden.value = brojDogovoriInput.value || '';
                        console.log(`ASOKvartalenOBD1: Updated TableRows[${i}].BrojDogovori = ${brojDogovoriHidden.value}`);
                    }
                    if (premijaHidden && premijaInput) {
                        premijaHidden.value = premijaInput.value || '';
                        console.log(`ASOKvartalenOBD1: Updated TableRows[${i}].Premija = ${premijaHidden.value}`);
                    }
                    if (realiziranaHidden && realiziranaInput) {
                        realiziranaHidden.value = realiziranaInput.value || '';
                        console.log(`ASOKvartalenOBD1: Updated TableRows[${i}].RealiziranaProvizija = ${realiziranaHidden.value}`);
                    }
                    if (presmetanaHidden && presmetanaInput) {
                        presmetanaHidden.value = presmetanaInput.value || '';
                        console.log(`ASOKvartalenOBD1: Updated TableRows[${i}].PresmetanaProvizija = ${presmetanaHidden.value}`);
                    }
                }
                
                console.log('ASOKvartalenOBD1: Hidden fields updated, submitting form');
            });
        }
        
        // Add functionality to update Excel export form with current input values
        const exportExcelForm = document.getElementById('exportExcelForm');
        if (exportExcelForm) {
            exportExcelForm.addEventListener('submit', function(e) {
                console.log('ASOKvartalenOBD1: Excel export form submitted, updating hidden fields');
                
                // Update hidden fields with current input values
                for (let i = 0; i < tableRows.length; i++) {
                    const row = tableRows[i];
                    const brojDogovoriInput = row.querySelector('input[name$=".BrojDogovori"]');
                    const premijaInput = row.querySelector('input[name$=".Premija"]');
                    const realiziranaInput = row.querySelector('input[name$=".RealiziranaProvizija"]');
                    const presmetanaInput = row.querySelector('input[name$=".PresmetanaProvizija"]');
                    
                    // Update hidden fields
                    const brojDogovoriHidden = exportExcelForm.querySelector(`input[name="TableRows[${i}].BrojDogovori"]`);
                    const premijaHidden = exportExcelForm.querySelector(`input[name="TableRows[${i}].Premija"]`);
                    const realiziranaHidden = exportExcelForm.querySelector(`input[name="TableRows[${i}].RealiziranaProvizija"]`);
                    const presmetanaHidden = exportExcelForm.querySelector(`input[name="TableRows[${i}].PresmetanaProvizija"]`);
                    
                    // Update Osiguritel hidden field
                    const osiguritelHidden = exportExcelForm.querySelector('input[name="SelectedOsiguritelId"]');
                    if (osiguritelHidden) {
                        const osiguritelSelect = document.querySelector('select[name="SelectedOsiguritelId"]');
                        osiguritelHidden.value = osiguritelSelect ? osiguritelSelect.value : '';
                        console.log(`ASOKvartalenOBD1: Updated Excel SelectedOsiguritelId = ${osiguritelHidden.value}`);
                    }
                    
                    if (brojDogovoriHidden && brojDogovoriInput) {
                        brojDogovoriHidden.value = brojDogovoriInput.value || '';
                        console.log(`ASOKvartalenOBD1: Updated Excel TableRows[${i}].BrojDogovori = ${brojDogovoriHidden.value}`);
                    }
                    if (premijaHidden && premijaInput) {
                        premijaHidden.value = premijaInput.value || '';
                        console.log(`ASOKvartalenOBD1: Updated Excel TableRows[${i}].Premija = ${premijaHidden.value}`);
                    }
                    if (realiziranaHidden && realiziranaInput) {
                        realiziranaHidden.value = realiziranaInput.value || '';
                        console.log(`ASOKvartalenOBD1: Updated Excel TableRows[${i}].RealiziranaProvizija = ${realiziranaHidden.value}`);
                    }
                    if (presmetanaHidden && presmetanaInput) {
                        presmetanaHidden.value = presmetanaInput.value || '';
                        console.log(`ASOKvartalenOBD1: Updated Excel TableRows[${i}].PresmetanaProvizija = ${presmetanaHidden.value}`);
                    }
                }
                
                console.log('ASOKvartalenOBD1: Excel hidden fields updated, submitting form');
            });
        }
        
        // Add functionality to update PDF export form with current input values
        const exportPdfForm = document.getElementById('exportPdfForm');
        if (exportPdfForm) {
            exportPdfForm.addEventListener('submit', function(e) {
                console.log('ASOKvartalenOBD1: PDF export form submitted, updating hidden fields');
                
                // Update hidden fields with current input values
                for (let i = 0; i < tableRows.length; i++) {
                    const row = tableRows[i];
                    const brojDogovoriInput = row.querySelector('input[name$=".BrojDogovori"]');
                    const premijaInput = row.querySelector('input[name$=".Premija"]');
                    const realiziranaInput = row.querySelector('input[name$=".RealiziranaProvizija"]');
                    const presmetanaInput = row.querySelector('input[name$=".PresmetanaProvizija"]');
                    
                    // Update hidden fields
                    const brojDogovoriHidden = exportPdfForm.querySelector(`input[name="TableRows[${i}].BrojDogovori"]`);
                    const premijaHidden = exportPdfForm.querySelector(`input[name="TableRows[${i}].Premija"]`);
                    const realiziranaHidden = exportPdfForm.querySelector(`input[name="TableRows[${i}].RealiziranaProvizija"]`);
                    const presmetanaHidden = exportPdfForm.querySelector(`input[name="TableRows[${i}].PresmetanaProvizija"]`);
                    
                    // Update Osiguritel hidden field
                    const osiguritelHidden = exportPdfForm.querySelector('input[name="SelectedOsiguritelId"]');
                    if (osiguritelHidden) {
                        const osiguritelSelect = document.querySelector('select[name="SelectedOsiguritelId"]');
                        osiguritelHidden.value = osiguritelSelect ? osiguritelSelect.value : '';
                        console.log(`ASOKvartalenOBD1: Updated PDF SelectedOsiguritelId = ${osiguritelHidden.value}`);
                    }
                    
                    if (brojDogovoriHidden && brojDogovoriInput) {
                        brojDogovoriHidden.value = brojDogovoriInput.value || '';
                        console.log(`ASOKvartalenOBD1: Updated PDF TableRows[${i}].BrojDogovori = ${brojDogovoriHidden.value}`);
                    }
                    if (premijaHidden && premijaInput) {
                        premijaHidden.value = premijaInput.value || '';
                        console.log(`ASOKvartalenOBD1: Updated PDF TableRows[${i}].Premija = ${premijaHidden.value}`);
                    }
                    if (realiziranaHidden && realiziranaInput) {
                        realiziranaHidden.value = realiziranaInput.value || '';
                        console.log(`ASOKvartalenOBD1: Updated PDF TableRows[${i}].RealiziranaProvizija = ${realiziranaHidden.value}`);
                    }
                    if (presmetanaHidden && presmetanaInput) {
                        presmetanaHidden.value = presmetanaInput.value || '';
                        console.log(`ASOKvartalenOBD1: Updated PDF TableRows[${i}].PresmetanaProvizija = ${presmetanaHidden.value}`);
                    }
                }
                
                console.log('ASOKvartalenOBD1: PDF hidden fields updated, submitting form');
            });
        }
        
        // Track if any input has been modified by the user
        let userModified = false;
        
        // Check if we're in confirmed mode (data is saved)
        const isConfirmedMode = @Json.Serialize(Model.IsConfirmed);
        console.log('ASOKvartalenOBD1: Is confirmed mode:', isConfirmedMode);
        
        function recalcTotals() {
            // Always recalculate totals when user modifies values, regardless of mode
            if (!userModified) {
                console.log('ASOKvartalenOBD1: Skipping recalculation - no user modifications yet');
                return;
            }
            
            console.log('ASOKvartalenOBD1: ===== STARTING TOTAL RECALCULATION =====');
            let brojDogovori = 0, premija = 0, realizirana = 0, presmetana = 0;
            
            // Log all input values first - only from the visible table
            console.log('ASOKvartalenOBD1: All input values before calculation (visible table only):');
            document.querySelectorAll('tbody input[type="number"]').forEach((input, index) => {
                const rowIndex = Math.floor(index / 4);
                const fieldType = input.name.split('.').pop();
                console.log(`Row ${rowIndex}, ${fieldType}: "${input.value}" (raw)`);
            });
            
            // Calculate totals based on current input values (which include user modifications)
            // Only use inputs from the visible table body, not hidden form fields
            console.log('ASOKvartalenOBD1: Calculating BrojDogovori totals:');
            document.querySelectorAll('tbody input[name$=".BrojDogovori"]').forEach((input, index) => {
                const inputValue = input.value.trim();
                const currentValue = inputValue === '' ? 0 : parseInt(inputValue) || 0;
                brojDogovori += currentValue;
                console.log(`  Input ${index}: "${inputValue}" -> ${currentValue} (running total: ${brojDogovori})`);
            });
            
            console.log('ASOKvartalenOBD1: Calculating Premija totals:');
            document.querySelectorAll('tbody input[name$=".Premija"]').forEach((input, index) => {
                const inputValue = input.value.trim();
                const currentValue = inputValue === '' ? 0 : parseFloat(inputValue) || 0;
                const roundedValue = roundHalfUp(currentValue);
                premija += roundedValue;
                
                console.log(`  Input ${index}: "${inputValue}" -> ${currentValue} -> ${roundedValue} (running total: ${premija})`);
                console.log(`    roundHalfUp(${currentValue}) = ${roundedValue}, type: ${typeof roundedValue}`);
            });
            
            console.log('ASOKvartalenOBD1: Calculating RealiziranaProvizija totals:');
            document.querySelectorAll('tbody input[name$=".RealiziranaProvizija"]').forEach((input, index) => {
                const inputValue = input.value.trim();
                const currentValue = inputValue === '' ? 0 : parseFloat(inputValue) || 0;
                realizirana += roundHalfUp(currentValue);
                console.log(`  Input ${index}: "${inputValue}" -> ${currentValue} -> ${roundHalfUp(currentValue)} (running total: ${realizirana})`);
            });
            
            console.log('ASOKvartalenOBD1: Calculating PresmetanaProvizija totals:');
            document.querySelectorAll('tbody input[name$=".PresmetanaProvizija"]').forEach((input, index) => {
                const inputValue = input.value.trim();
                const currentValue = inputValue === '' ? 0 : parseFloat(inputValue) || 0;
                presmetana += roundHalfUp(currentValue);
                console.log(`  Input ${index}: "${inputValue}" -> ${currentValue} -> ${roundHalfUp(currentValue)} (running total: ${presmetana})`);
            });
            
            console.log(`ASOKvartalenOBD1: FINAL CALCULATED TOTALS - BrojDogovori: ${brojDogovori}, Premija: ${premija}, Realizirana: ${realizirana}, Presmetana: ${presmetana}`);
            
            // Get current displayed totals before update
            const oldBrojDogovori = document.getElementById('vkupno-broj').textContent;
            const oldPremija = document.getElementById('vkupno-premija').textContent;
            const oldRealizirana = document.getElementById('vkupno-realizirana').textContent;
            const oldPresmetana = document.getElementById('vkupno-presmetana').textContent;
            
            console.log(`ASOKvartalenOBD1: OLD DISPLAYED TOTALS - BrojDogovori: ${oldBrojDogovori}, Premija: ${oldPremija}, Realizirana: ${oldRealizirana}, Presmetana: ${oldPresmetana}`);
            
            // Update the totals display with the calculated values
            document.getElementById('vkupno-broj').textContent = brojDogovori;
            document.getElementById('vkupno-premija').textContent = premija;
            document.getElementById('vkupno-realizirana').textContent = realizirana;
            document.getElementById('vkupno-presmetana').textContent = presmetana;
            
            console.log(`ASOKvartalenOBD1: UPDATED DISPLAYED TOTALS - BrojDogovori: ${brojDogovori}, Premija: ${premija}, Realizirana: ${realizirana}, Presmetana: ${presmetana}`);
            console.log('ASOKvartalenOBD1: ===== TOTAL RECALCULATION COMPLETE =====');
        }
        
        // Only recalculate when user actually changes input values
        document.querySelectorAll('input[type="number"]').forEach((input, inputIndex) => {
            input.addEventListener('input', function() {
                userModified = true;
                const currentValue = this.value;
                const rowIndex = Math.floor(inputIndex / 4); // 4 inputs per row
                const fieldType = this.name.split('.').pop(); // Get the field type (BrojDogovori, Premija, etc.)
                
                console.log(`ASOKvartalenOBD1: User modified input: Row ${rowIndex}, Field ${fieldType} = ${currentValue}`);
                
                // Log the change from original value
                if (originalInputValues[rowIndex]) {
                    const originalValue = originalInputValues[rowIndex][fieldType.toLowerCase()] || 0;
                    const change = (parseFloat(currentValue) || 0) - originalValue;
                    console.log(`ASOKvartalenOBD1: Change from original: ${originalValue} -> ${currentValue} (difference: ${change})`);
                }
                
                // Validate the input value
                const numericValue = parseFloat(currentValue) || 0;
                console.log(`ASOKvartalenOBD1: Numeric value for calculation: ${numericValue}`);
                
                // Always recalculate totals when user makes changes for real-time updates
                console.log('ASOKvartalenOBD1: Recalculating totals for real-time update');
                recalcTotals();
                
                // Only validate in confirmed mode (commented out to prevent double calculation)
                // if (isConfirmedMode) {
                //     validateTotals();
                // }
            });
        });
        
        // Function to validate that totals are calculated correctly
        function validateTotals() {
            // Only validate in confirmed mode
            if (!isConfirmedMode) {
                console.log('ASOKvartalenOBD1: Skipping validation - in preview mode');
                return;
            }
            
            console.log('ASOKvartalenOBD1: Validating totals calculation...');
            
            let expectedBrojDogovori = 0, expectedPremija = 0, expectedRealizirana = 0, expectedPresmetana = 0;
            
            // Calculate expected totals from input values
            document.querySelectorAll('tbody input[name$=".BrojDogovori"]').forEach((input, index) => {
                const inputValue = input.value.trim();
                const value = inputValue === '' ? 0 : parseInt(inputValue) || 0;
                expectedBrojDogovori += value;
            });
            
            document.querySelectorAll('tbody input[name$=".Premija"]').forEach((input, index) => {
                const inputValue = input.value.trim();
                const value = inputValue === '' ? 0 : parseInt(inputValue) || 0;
                expectedPremija += value;
            });
            
            document.querySelectorAll('tbody input[name$=".RealiziranaProvizija"]').forEach((input, index) => {
                const inputValue = input.value.trim();
                const value = inputValue === '' ? 0 : parseInt(inputValue) || 0;
                expectedRealizirana += value;
            });
            
            document.querySelectorAll('tbody input[name$=".PresmetanaProvizija"]').forEach((input, index) => {
                const inputValue = input.value.trim();
                const value = inputValue === '' ? 0 : parseInt(inputValue) || 0;
                expectedPresmetana += value;
            });
            
            // Get displayed totals
            const displayedBrojDogovori = parseInt(document.getElementById('vkupno-broj').textContent) || 0;
            const displayedPremija = parseInt(document.getElementById('vkupno-premija').textContent) || 0;
            const displayedRealizirana = parseInt(document.getElementById('vkupno-realizirana').textContent) || 0;
            const displayedPresmetana = parseInt(document.getElementById('vkupno-presmetana').textContent) || 0;
            
            console.log(`ASOKvartalenOBD1: Expected totals - BrojDogovori: ${expectedBrojDogovori}, Premija: ${expectedPremija}, Realizirana: ${expectedRealizirana}, Presmetana: ${expectedPresmetana}`);
            console.log(`ASOKvartalenOBD1: Displayed totals - BrojDogovori: ${displayedBrojDogovori}, Premija: ${displayedPremija}, Realizirana: ${displayedRealizirana}, Presmetana: ${displayedPresmetana}`);
            
            // Check if totals match
            if (expectedBrojDogovori !== displayedBrojDogovori) {
                console.warn(`ASOKvartalenOBD1: BrojDogovori totals don't match! Expected: ${expectedBrojDogovori}, Displayed: ${displayedBrojDogovori}`);
            }
            if (Math.abs(expectedPremija - displayedPremija) > 0) {
                console.warn(`ASOKvartalenOBD1: Premija totals don't match! Expected: ${expectedPremija}, Displayed: ${displayedPremija}`);
            }
            if (Math.abs(expectedRealizirana - displayedRealizirana) > 0) {
                console.warn(`ASOKvartalenOBD1: Realizirana totals don't match! Expected: ${expectedRealizirana}, Displayed: ${displayedRealizirana}`);
            }
            if (Math.abs(expectedPresmetana - displayedPresmetana) > 0) {
                console.warn(`ASOKvartalenOBD1: Presmetana totals don't match! Expected: ${expectedPresmetana}, Displayed: ${displayedPresmetana}`);
            }
        }
        
        // Function to validate initial data and identify any issues
        function validateInitialData() {
            console.log('ASOKvartalenOBD1: Validating initial data...');
            
            let totalBrojDogovori = 0, totalPremija = 0, totalRealizirana = 0, totalPresmetana = 0;
            let emptyFields = 0, invalidFields = 0;
            
            // Check all input fields
            document.querySelectorAll('tbody input[type="number"]').forEach((input, index) => {
                const inputValue = input.value.trim();
                const fieldType = input.name.split('.').pop();
                const rowIndex = Math.floor(index / 4);
                
                if (inputValue === '') {
                    emptyFields++;
                    console.log(`ASOKvartalenOBD1: Empty field found - Row ${rowIndex}, ${fieldType}`);
                } else if (isNaN(parseFloat(inputValue))) {
                    invalidFields++;
                    console.log(`ASOKvartalenOBD1: Invalid field found - Row ${rowIndex}, ${fieldType}: "${inputValue}"`);
                } else {
                    const value = parseFloat(inputValue);
                    if (fieldType === 'BrojDogovori') {
                        totalBrojDogovori += parseInt(inputValue);
                    } else if (fieldType === 'Premija') {
                        totalPremija += parseInt(inputValue);
                    } else if (fieldType === 'RealiziranaProvizija') {
                        totalRealizirana += parseInt(inputValue);
                    } else if (fieldType === 'PresmetanaProvizija') {
                        totalPresmetana += parseInt(inputValue);
                    }
                    console.log(`ASOKvartalenOBD1: Valid field - Row ${rowIndex}, ${fieldType}: ${value}`);
                }
            });
            
            console.log(`ASOKvartalenOBD1: Data validation - Empty fields: ${emptyFields}, Invalid fields: ${invalidFields}`);
            console.log(`ASOKvartalenOBD1: Calculated totals from inputs - BrojDogovori: ${totalBrojDogovori}, Premija: ${totalPremija}, Realizirana: ${totalRealizirana}, Presmetana: ${totalPresmetana}`);
            
            // Compare with server-side totals
            const serverBrojDogovori = parseInt(document.getElementById('vkupno-broj').textContent) || 0;
            const serverPremija = parseInt(document.getElementById('vkupno-premija').textContent) || 0;
            const serverRealizirana = parseInt(document.getElementById('vkupno-realizirana').textContent) || 0;
            const serverPresmetana = parseInt(document.getElementById('vkupno-presmetana').textContent) || 0;
            
            console.log(`ASOKvartalenOBD1: Server-side totals - BrojDogovori: ${serverBrojDogovori}, Premija: ${serverPremija}, Realizirana: ${serverRealizirana}, Presmetana: ${serverPresmetana}`);
            
            if (totalBrojDogovori !== serverBrojDogovori) {
                console.warn(`ASOKvartalenOBD1: BrojDogovori mismatch! Input sum: ${totalBrojDogovori}, Server: ${serverBrojDogovori}, Difference: ${totalBrojDogovori - serverBrojDogovori}`);
            }
            if (Math.abs(totalPremija - serverPremija) > 0) {
                console.warn(`ASOKvartalenOBD1: Premija mismatch! Input sum: ${totalPremija}, Server: ${serverPremija}, Difference: ${totalPremija - serverPremija}`);
            }
            if (Math.abs(totalRealizirana - serverRealizirana) > 0) {
                console.warn(`ASOKvartalenOBD1: Realizirana mismatch! Input sum: ${totalRealizirana}, Server: ${serverRealizirana}, Difference: ${totalRealizirana - serverRealizirana}`);
            }
            if (Math.abs(totalPresmetana - serverPresmetana) > 0) {
                console.warn(`ASOKvartalenOBD1: Presmetana mismatch! Input sum: ${totalPresmetana}, Server: ${serverPresmetana}, Difference: ${totalPresmetana - serverPresmetana}`);
            }
        }
        
        // Function to debug a specific calculation issue
        function debugCalculation() {
            console.log('ASOKvartalenOBD1: ===== DEBUGGING CALCULATION ISSUE =====');
            
            // Get the specific field that was modified
            const activeElement = document.activeElement;
            if (activeElement && activeElement.tagName === 'INPUT') {
                const fieldType = activeElement.name.split('.').pop();
                const rowIndex = Math.floor(Array.from(document.querySelectorAll('input[type="number"]')).indexOf(activeElement) / 4);
                const oldValue = originalInputValues[rowIndex] ? originalInputValues[rowIndex][fieldType.toLowerCase()] : 'unknown';
                const newValue = activeElement.value;
                
                console.log(`ASOKvartalenOBD1: Modified field - Row ${rowIndex}, ${fieldType}: ${oldValue} -> ${newValue}`);
                
                // Calculate what the total should be
                let expectedTotal = 0;
                document.querySelectorAll(`tbody input[name$=".${fieldType}"]`).forEach((input, index) => {
                    const value = parseInt(input.value) || 0;
                    expectedTotal += value;
                    console.log(`ASOKvartalenOBD1: ${fieldType} input ${index}: ${input.value} -> ${value} (running total: ${expectedTotal})`);
                });
                
                console.log(`ASOKvartalenOBD1: Expected total for ${fieldType}: ${expectedTotal}`);
                
                // Get current displayed total
                const displayedTotal = document.getElementById(`vkupno-${fieldType.toLowerCase()}`).textContent;
                console.log(`ASOKvartalenOBD1: Currently displayed total for ${fieldType}: ${displayedTotal}`);
                
                if (Math.abs(expectedTotal - parseInt(displayedTotal)) > 0) {
                    console.error(`ASOKvartalenOBD1: CALCULATION ERROR! Expected: ${expectedTotal}, Displayed: ${displayedTotal}`);
                }
            }
            
            console.log('ASOKvartalenOBD1: ===== DEBUGGING COMPLETE =====');
        }
        
        // Don't call recalcTotals() on page load - keep the original server-side totals
        console.log('ASOKvartalenOBD1: Keeping original server-side totals on page load');
        
        // Initialize the page properly
        function initializePage() {
            console.log('ASOKvartalenOBD1: Initializing page...');
            console.log('ASOKvartalenOBD1: Current mode:', isConfirmedMode ? 'CONFIRMED' : 'PREVIEW');
            
            // Store the initial server-side totals
            const initialTotals = {
                brojDogovori: document.getElementById('vkupno-broj').textContent,
                premija: document.getElementById('vkupno-premija').textContent,
                realizirana: document.getElementById('vkupno-realizirana').textContent,
                presmetana: document.getElementById('vkupno-presmetana').textContent
            };
            
            console.log('ASOKvartalenOBD1: Initial server-side totals:', initialTotals);
            
            // Log all input values to verify they match the server-side data
            console.log('ASOKvartalenOBD1: Initial input values:');
            document.querySelectorAll('input[type="number"]').forEach((input, index) => {
                const rowIndex = Math.floor(index / 4);
                const fieldType = input.name.split('.').pop();
                console.log(`Row ${rowIndex}, ${fieldType}: ${input.value}`);
            });
            
            // Validate initial data to identify any issues
            validateInitialData();
            
            // Both modes will now support real-time total updates
            if (isConfirmedMode) {
                console.log('ASOKvartalenOBD1: Confirmed mode - real-time updates enabled with validation');
                validateTotals();
            } else {
                console.log('ASOKvartalenOBD1: Preview mode - real-time updates enabled, user changes will update totals immediately');
            }
            
            console.log('ASOKvartalenOBD1: Page initialization complete');
        }
        
        // Call initialization after a short delay to ensure everything is loaded
        setTimeout(initializePage, 100);
    });
</script>
